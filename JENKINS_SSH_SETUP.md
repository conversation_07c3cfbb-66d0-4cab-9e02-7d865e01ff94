# Jenkins SSH Publisher 配置指南

## 概述

本文档说明如何配置Jenkins使用SSH Publisher插件连接到szvs7165服务器进行MetisGrid DataPipeline的自动部署。

## 前置条件

### 1. Jenkins插件安装
确保Jenkins已安装以下插件：
- **Publish Over SSH** 插件
- **Pipeline** 插件

### 2. 服务器准备
确保目标服务器szvs7165满足以下条件：
- SSH服务正常运行
- Docker和Docker Compose已安装
- Git已安装
- 部署目录权限正确

## SSH Publisher 配置步骤

### 1. 配置SSH服务器连接

1. **进入Jenkins管理界面**
   ```
   <PERSON> → 系统管理 → 系统配置
   ```

2. **找到"Publish over SSH"配置节**
   滚动到页面底部找到"Publish over SSH"配置

3. **添加SSH服务器配置**
   点击"新增"按钮，配置如下：

   | 配置项 | 值 | 说明 |
   |--------|----|----- |
   | Name | `szvs7165` | 服务器配置名称（必须与Jenkinsfile中的configName一致） |
   | Hostname | `szvs7165` | 服务器主机名或IP地址 |
   | Username | `root` | SSH登录用户名 |
   | Remote Directory | `/` | 远程根目录 |

4. **配置SSH认证**
   
   **方式1: 使用SSH密钥（推荐）**
   - 点击"高级"按钮
   - 勾选"Use password authentication, or use a different key"
   - 在"Key"字段中粘贴私钥内容
   
   **方式2: 使用密码**
   - 点击"高级"按钮
   - 勾选"Use password authentication, or use a different key"
   - 在"Passphrase / Password"字段中输入SSH密码

5. **测试连接**
   - 点击"Test Configuration"按钮
   - 确保显示"Success"

6. **保存配置**
   - 点击页面底部的"保存"按钮

### 2. 创建Jenkins任务

1. **创建新任务**
   ```
   Jenkins → 新建任务 → Pipeline → 输入任务名称: MetisGrid-DataPipeline-Deploy
   ```

2. **配置Pipeline**
   - 在"Pipeline"部分选择"Pipeline script"
   - 将以下任一脚本复制到脚本框中：
     - `Jenkinsfile` (完整版本)
     - `Jenkinsfile-simple` (简化版本)

3. **配置参数（可选）**
   - 勾选"参数化构建过程"
   - 添加需要的参数

4. **配置触发器（可选）**
   - 定时构建: `H 2 * * *` (每天凌晨2点)
   - Git Hook触发
   - 手动触发

## 文件选择说明

### Jenkinsfile (完整版)
- **特点**: 功能完整，错误处理详细，日志丰富
- **适用**: 生产环境，需要详细监控和日志
- **文件**: `Jenkinsfile`

### Jenkinsfile-simple (简化版)
- **特点**: 代码简洁，易于理解和修改
- **适用**: 快速部署，测试环境
- **文件**: `Jenkinsfile-simple`

## 部署流程

### 自动执行步骤
1. **代码更新**: 从Git仓库拉取最新代码
2. **环境检查**: 验证Docker和必要文件
3. **服务停止**: 停止现有容器
4. **资源清理**: 清理旧的Docker镜像
5. **前端构建**: 预构建前端资源（如果需要）
6. **镜像构建**: 构建新的Docker镜像
7. **服务启动**: 启动新的容器
8. **健康检查**: 验证服务是否正常
9. **资源清理**: 清理未使用的资源

### 服务访问地址
- **前端**: http://localhost:8091
- **后端**: http://localhost:8081

## 故障排除

### 常见问题

1. **SSH连接失败**
   ```
   解决方案:
   - 检查服务器SSH服务状态: systemctl status sshd
   - 验证用户名和密码/密钥
   - 检查防火墙设置
   - 确保Jenkins服务器能访问目标服务器
   ```

2. **权限问题**
   ```bash
   # 在目标服务器上执行
   sudo mkdir -p /opt/deploy/dataPipeline
   sudo chown -R root:root /opt/deploy/dataPipeline
   sudo chmod -R 755 /opt/deploy/dataPipeline
   ```

3. **Docker权限问题**
   ```bash
   # 确保用户在docker组中
   sudo usermod -aG docker root
   sudo systemctl restart docker
   ```

4. **Git访问问题**
   ```bash
   # 测试Git仓库访问
   git clone https://************:8088/nizhangpeng/metisgrid.git /tmp/test
   ```

### 调试命令

```bash
# 在目标服务器上手动测试部署脚本
cd /opt/deploy/dataPipeline/metisgrid

# 检查Git状态
git status
git log --oneline -5

# 检查Docker状态
docker ps -a
docker images
docker-compose -f docker-compose.prod.yml ps

# 检查服务响应
curl -I http://localhost:8091
curl -I http://localhost:8081

# 查看容器日志
docker-compose -f docker-compose.prod.yml logs --tail=50
```

## 安全建议

1. **使用SSH密钥认证**而不是密码认证
2. **限制SSH访问**，只允许Jenkins服务器连接
3. **定期更新**SSH密钥和密码
4. **监控部署日志**，及时发现异常
5. **备份重要数据**，包括数据库和配置文件

## 监控和维护

### 定期检查项目
- [ ] Jenkins服务器磁盘空间
- [ ] 目标服务器资源使用情况
- [ ] Docker镜像和容器状态
- [ ] 应用日志文件大小
- [ ] 网络连接稳定性

### 维护建议
- 每周检查部署日志
- 每月清理旧的Docker镜像
- 定期备份部署配置
- 监控服务器性能指标

## 联系支持

如遇到问题，请按以下顺序排查：
1. 检查Jenkins构建日志
2. 验证SSH连接配置
3. 检查目标服务器状态
4. 查看应用容器日志
5. 联系开发团队或系统管理员

---
**文档版本**: 1.0  
**最后更新**: 2025-06-21  
**适用版本**: Jenkins 2.300+, SSH Publisher Plugin 1.22+
