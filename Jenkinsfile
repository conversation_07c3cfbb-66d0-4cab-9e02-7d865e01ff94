pipeline {
    agent any

    environment {
        PROJECT_NAME = 'datapipeline'
        DEPLOY_PATH = '/opt/deploy/dataPipeline/metisgrid'
        GIT_REPO_URL = 'https://************:8088/nizhangpeng/metisgrid.git'
        DOCKER_COMPOSE_FILE = 'docker-compose.prod.yml'
        FRONTEND_PORT = '8091'
        BACKEND_PORT = '8001'
        SERVER_NAME = 'szvs7165'
    }

    options {
        timeout(time: 45, unit: 'MINUTES')
        retry(1)
        skipDefaultCheckout()
    }
    
    stages {
        stage('Preparation') {
            steps {
                script {
                    echo "🚀 开始部署 MetisGrid DataPipeline"
                    echo "📋 部署配置信息:"
                    echo "  - 项目名称: ${PROJECT_NAME}"
                    echo "  - 服务器: ${SERVER_NAME}"
                    echo "  - 部署路径: ${DEPLOY_PATH}"
                    echo "  - Git仓库: ${GIT_REPO_URL}"
                    echo "  - 前端端口: ${FRONTEND_PORT}"
                    echo "  - 后端端口: ${BACKEND_PORT}"
                    echo "========================================"
                }
            }
        }

        stage('Deploy to Server') {
            steps {
                script {
                    echo "📡 开始部署到服务器 ${SERVER_NAME}..."

                    sshPublisher(publishers: [
                        sshPublisherDesc(
                            configName: "${SERVER_NAME}",
                            transfers: [
                                sshTransfer(
                                    cleanRemote: false,
                                    excludes: '',
                                    execCommand: """
                                        echo "🚀 开始 MetisGrid DataPipeline 自动部署"
                                        echo "📅 部署时间: \$(date)"
                                        echo "📋 配置信息:"
                                        echo "  - 项目: ${PROJECT_NAME}"
                                        echo "  - 部署路径: ${DEPLOY_PATH}"
                                        echo "  - Git仓库: ${GIT_REPO_URL}"
                                        echo "========================================"

                                        # 1. 更新Git代码
                                        echo "📥 步骤1: 更新Git代码"
                                        if [ ! -d "${DEPLOY_PATH}" ]; then
                                            echo "创建部署目录并克隆代码..."
                                            mkdir -p \$(dirname "${DEPLOY_PATH}")
                                            git clone "${GIT_REPO_URL}" "${DEPLOY_PATH}"
                                            echo "✅ 代码克隆成功"
                                        else
                                            echo "更新现有代码..."
                                            cd "${DEPLOY_PATH}"
                                            git fetch origin
                                            git checkout main
                                            git reset --hard origin/main
                                            git pull origin main
                                            echo "✅ 代码更新成功"
                                        fi

                                        cd "${DEPLOY_PATH}"
                                        echo "📝 当前版本: \$(git log --oneline -1)"
                                        echo ""

                                        # 2. 环境检查
                                        echo "🔍 步骤2: 环境检查"
                                        if [ ! -f "${DOCKER_COMPOSE_FILE}" ]; then
                                            echo "❌ 找不到 ${DOCKER_COMPOSE_FILE}"
                                            exit 1
                                        fi
                                        if [ ! -f "docker_env/web/Dockerfile.prod" ]; then
                                            echo "❌ 找不到前端Dockerfile"
                                            exit 1
                                        fi
                                        if [ ! -f "docker_env/django/Dockerfile.prod" ]; then
                                            echo "❌ 找不到后端Dockerfile"
                                            exit 1
                                        fi
                                        if ! command -v docker >/dev/null 2>&1; then
                                            echo "❌ Docker未安装"
                                            exit 1
                                        fi
                                        if ! command -v docker-compose >/dev/null 2>&1; then
                                            echo "❌ docker-compose未安装"
                                            exit 1
                                        fi
                                        echo "✅ 环境检查通过"
                                        echo ""

                                        # 3. 停止现有服务
                                        echo "🛑 步骤3: 停止现有服务"
                                        if docker-compose -f "${DOCKER_COMPOSE_FILE}" ps -q 2>/dev/null | grep -q .; then
                                            docker-compose -f "${DOCKER_COMPOSE_FILE}" down --remove-orphans || true
                                            echo "✅ 现有服务已停止"
                                        else
                                            echo "ℹ️ 没有运行中的服务"
                                        fi
                                        echo ""

                                        # 4. 清理Docker资源
                                        echo "🧹 步骤4: 清理Docker资源"
                                        # 删除项目相关镜像
                                        docker images --filter "label=project=datapipeline" -q | xargs -r docker rmi -f 2>/dev/null || true
                                        docker images | grep datapipeline | awk '{print \$3}' | xargs -r docker rmi -f 2>/dev/null || true
                                        # 清理悬空镜像
                                        docker image prune -f >/dev/null 2>&1 || true
                                        echo "✅ Docker资源清理完成"
                                        echo ""

                                        # 5. 预构建前端（如果需要）
                                        echo "🔨 步骤5: 预构建前端资源"
                                        if [ -d "web" ] && [ -f "web/package.json" ]; then
                                            echo "检测到前端项目，开始预构建..."
                                            cd web
                                            if command -v pnpm >/dev/null 2>&1; then
                                                echo "使用pnpm构建前端..."
                                                pnpm install --frozen-lockfile
                                                pnpm build
                                            elif command -v npm >/dev/null 2>&1; then
                                                echo "使用npm构建前端..."
                                                npm ci
                                                npm run build
                                            else
                                                echo "⚠️ 未找到pnpm或npm，跳过前端预构建"
                                            fi
                                            cd ..
                                            echo "✅ 前端预构建完成"
                                        else
                                            echo "ℹ️ 未检测到前端项目，跳过预构建"
                                        fi
                                        echo ""

                                        # 6. 构建Docker镜像
                                        echo "🏗️ 步骤6: 构建Docker镜像"
                                        docker-compose -f "${DOCKER_COMPOSE_FILE}" build --no-cache --parallel
                                        echo "✅ 镜像构建完成"
                                        echo ""

                                        # 7. 启动服务
                                        echo "🚀 步骤7: 启动服务"
                                        docker-compose -f "${DOCKER_COMPOSE_FILE}" up -d
                                        echo "⏳ 等待服务启动..."
                                        sleep 30
                                        docker-compose -f "${DOCKER_COMPOSE_FILE}" ps
                                        echo "✅ 服务启动完成"
                                        echo ""

                                        # 8. 健康检查
                                        echo "🏥 步骤8: 健康检查"
                                        # 检查容器状态
                                        if ! docker-compose -f "${DOCKER_COMPOSE_FILE}" ps | grep -q "Up"; then
                                            echo "❌ 容器启动失败"
                                            docker-compose -f "${DOCKER_COMPOSE_FILE}" logs --tail=20
                                            exit 1
                                        fi

                                        # 检查前端服务
                                        echo "检查前端服务 (端口${FRONTEND_PORT})..."
                                        for i in \$(seq 1 12); do
                                            if curl -f -s http://localhost:${FRONTEND_PORT} >/dev/null 2>&1; then
                                                echo "✅ 前端服务正常"
                                                break
                                            fi
                                            echo "⏳ 前端服务检查 \$i/12 ..."
                                            sleep 10
                                        done

                                        # 检查后端服务
                                        echo "检查后端服务 (端口${BACKEND_PORT})..."
                                        for i in \$(seq 1 12); do
                                            if curl -f -s http://localhost:${BACKEND_PORT} >/dev/null 2>&1; then
                                                echo "✅ 后端服务正常"
                                                break
                                            fi
                                            echo "⏳ 后端服务检查 \$i/12 ..."
                                            sleep 10
                                        done

                                        echo "✅ 健康检查完成"
                                        echo ""

                                        # 9. 清理未使用资源
                                        echo "🧽 步骤9: 清理未使用资源"
                                        docker image prune -f --filter "label=project=${PROJECT_NAME}" >/dev/null 2>&1 || true
                                        docker system prune -f >/dev/null 2>&1 || true
                                        echo "✅ 资源清理完成"
                                        echo ""

                                        # 10. 显示部署结果
                                        echo "📊 部署结果"
                                        echo "========================================"
                                        echo "🎉 部署成功！"
                                        echo "📱 前端访问地址: http://localhost:${FRONTEND_PORT}"
                                        echo "🔧 后端访问地址: http://localhost:${BACKEND_PORT}"
                                        echo "📅 部署时间: \$(date)"
                                        echo "📝 当前版本: \$(git log --oneline -1)"
                                        echo ""
                                        echo "📋 容器状态:"
                                        docker-compose -f "${DOCKER_COMPOSE_FILE}" ps
                                        echo "========================================"
                                        echo "🎊 MetisGrid DataPipeline 部署完成！"
                                    """,
                                    execTimeout: 600000,
                                    flatten: false,
                                    makeEmptyDirs: false,
                                    noDefaultExcludes: false,
                                    patternSeparator: '[, ]+',
                                    remoteDirectory: '/',
                                    remoteDirectorySDF: false,
                                    removePrefix: '',
                                    sourceFiles: ''
                                )
                            ],
                            usePromotionTimestamp: false,
                            useWorkspaceInPromotion: false,
                            verbose: true
                        )
                    ])
                }
            }
        }
    }
    
    post {
        success {
            script {
                echo "🎉 Jenkins Pipeline 执行成功！"
                echo "========================================"
                echo "📋 部署摘要:"
                echo "  - 项目: ${PROJECT_NAME}"
                echo "  - 服务器: ${SERVER_NAME}"
                echo "  - 部署路径: ${DEPLOY_PATH}"
                echo "  - 前端访问: http://localhost:${FRONTEND_PORT}"
                echo "  - 后端访问: http://localhost:${BACKEND_PORT}"
                echo "  - 执行时间: ${new Date()}"
                echo "========================================"
                echo "✅ MetisGrid DataPipeline 部署成功完成！"
            }
        }

        failure {
            script {
                echo "❌ Jenkins Pipeline 执行失败！"
                echo "========================================"
                echo "📋 失败信息:"
                echo "  - 项目: ${PROJECT_NAME}"
                echo "  - 服务器: ${SERVER_NAME}"
                echo "  - 失败时间: ${new Date()}"
                echo "========================================"
                echo "🔧 请检查以下内容:"
                echo "  1. 服务器 ${SERVER_NAME} 连接是否正常"
                echo "  2. Git仓库 ${GIT_REPO_URL} 是否可访问"
                echo "  3. 部署路径 ${DEPLOY_PATH} 权限是否正确"
                echo "  4. Docker服务是否正常运行"
                echo "  5. 端口 ${FRONTEND_PORT} 和 ${BACKEND_PORT} 是否被占用"
                echo "========================================"
                echo "📞 如需帮助，请联系开发团队或系统管理员"
            }
        }

        always {
            script {
                echo "🧹 清理Jenkins工作空间..."
                // 清理工作空间，但不影响构建结果
                try {
                    cleanWs(cleanWhenNotBuilt: false,
                            deleteDirs: true,
                            disableDeferredWipeout: true,
                            notFailBuild: true)
                    echo "✅ 工作空间清理完成"
                } catch (Exception e) {
                    echo "⚠️ 工作空间清理失败: ${e.getMessage()}"
                }
            }
        }
    }
}
