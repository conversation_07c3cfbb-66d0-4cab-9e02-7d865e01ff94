#!/bin/bash

# =============================================================================
# Git代码更新脚本 - MetisGrid DataPipeline 项目
# 用于从Git仓库拉取最新代码到指定目录
# =============================================================================

set -e  # 遇到错误立即退出

# 参数检查
if [ $# -lt 2 ]; then
    echo "用法: $0 <git_repo_url> <deploy_path>"
    echo "示例: $0 https://************:8088/nizhangpeng/metisgrid.git /opt/deploy/dataPipeline/metisgrid"
    exit 1
fi

GIT_REPO_URL="$1"
DEPLOY_PATH="$2"
BRANCH="main"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Git是否安装
check_git() {
    if ! command -v git >/dev/null 2>&1; then
        log_error "Git未安装，请先安装Git"
        exit 1
    fi
    log_info "Git版本: $(git --version)"
}

# 创建部署目录
create_deploy_directory() {
    if [ ! -d "$(dirname "$DEPLOY_PATH")" ]; then
        log_info "创建父目录: $(dirname "$DEPLOY_PATH")"
        mkdir -p "$(dirname "$DEPLOY_PATH")"
    fi
}

# 克隆或更新代码
update_code() {
    log_info "开始更新Git代码..."
    log_info "仓库URL: $GIT_REPO_URL"
    log_info "部署路径: $DEPLOY_PATH"
    log_info "目标分支: $BRANCH"
    
    if [ ! -d "$DEPLOY_PATH" ]; then
        # 目录不存在，执行克隆
        log_info "部署目录不存在，开始克隆代码..."
        create_deploy_directory
        
        if git clone "$GIT_REPO_URL" "$DEPLOY_PATH"; then
            log_success "代码克隆成功"
        else
            log_error "代码克隆失败"
            exit 1
        fi
    else
        # 目录存在，检查是否为Git仓库
        if [ ! -d "$DEPLOY_PATH/.git" ]; then
            log_warning "目录存在但不是Git仓库，备份现有目录并重新克隆..."
            
            # 备份现有目录
            backup_dir="${DEPLOY_PATH}_backup_$(date +%Y%m%d_%H%M%S)"
            mv "$DEPLOY_PATH" "$backup_dir"
            log_info "现有目录已备份到: $backup_dir"
            
            # 重新克隆
            if git clone "$GIT_REPO_URL" "$DEPLOY_PATH"; then
                log_success "代码重新克隆成功"
            else
                log_error "代码重新克隆失败"
                exit 1
            fi
        else
            # 是Git仓库，执行更新
            log_info "更新现有Git仓库..."
            
            cd "$DEPLOY_PATH"
            
            # 检查当前状态
            log_info "当前目录: $(pwd)"
            log_info "当前分支: $(git branch --show-current 2>/dev/null || echo '未知')"
            
            # 保存本地修改（如果有）
            if ! git diff --quiet || ! git diff --cached --quiet; then
                log_warning "检测到本地修改，保存到stash..."
                git stash push -m "Jenkins auto-deploy backup $(date)"
            fi
            
            # 获取远程更新
            log_info "获取远程更新..."
            if git fetch origin; then
                log_success "远程更新获取成功"
            else
                log_error "获取远程更新失败"
                exit 1
            fi
            
            # 切换到目标分支
            log_info "切换到 $BRANCH 分支..."
            if git checkout "$BRANCH"; then
                log_success "分支切换成功"
            else
                log_error "分支切换失败"
                exit 1
            fi
            
            # 重置到远程分支
            log_info "重置到远程 $BRANCH 分支..."
            if git reset --hard "origin/$BRANCH"; then
                log_success "代码重置成功"
            else
                log_error "代码重置失败"
                exit 1
            fi
            
            # 拉取最新代码
            log_info "拉取最新代码..."
            if git pull origin "$BRANCH"; then
                log_success "代码拉取成功"
            else
                log_error "代码拉取失败"
                exit 1
            fi
        fi
    fi
}

# 显示更新结果
show_update_result() {
    log_info "显示更新结果..."
    
    cd "$DEPLOY_PATH"
    
    echo "=================================="
    echo "Git仓库信息:"
    echo "  仓库URL: $GIT_REPO_URL"
    echo "  本地路径: $DEPLOY_PATH"
    echo "  当前分支: $(git branch --show-current)"
    echo "  最新提交: $(git log --oneline -1)"
    echo ""
    
    echo "最近5次提交:"
    git log --oneline -5
    echo ""
    
    echo "仓库状态:"
    git status --porcelain
    echo "=================================="
}

# 主函数
main() {
    log_info "开始执行Git代码更新..."
    echo "执行时间: $(date)"
    echo ""
    
    check_git
    update_code
    show_update_result
    
    log_success "🎉 Git代码更新完成！"
}

# 执行主函数
main "$@"
