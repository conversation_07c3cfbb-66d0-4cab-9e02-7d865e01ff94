# 部署脚本说明

本目录包含了 MetisGrid DataPipeline 项目的自动化部署脚本。

## 脚本列表

### 1. `deploy.sh` - 完整部署脚本
完整的自动化部署脚本，包含所有部署步骤。

**功能：**
- Git代码更新（切换到main分支并拉取最新代码）
- 清理Docker资源（删除旧容器和镜像）
- 构建和启动服务
- 执行健康检查

**使用方法：**
```bash
# 完整部署流程
./scripts/deploy.sh

# 指定部署路径
./scripts/deploy.sh --path /custom/deploy/path

# 部署指定分支
./scripts/deploy.sh --branch develop

# 跳过Git更新（仅重新构建）
./scripts/deploy.sh --skip-git

# 跳过Docker构建（仅重启服务）
./scripts/deploy.sh --skip-build

# 跳过健康检查
./scripts/deploy.sh --skip-test

# 显示帮助信息
./scripts/deploy.sh --help
```

### 2. `health_check.sh` - 健康检查脚本
独立的健康检查脚本，用于验证服务是否正常运行。

**功能：**
- 检查Docker Compose服务状态
- 检查容器健康状态
- 检查前端服务（端口8091）
- 检查后端服务（端口8001）
- 验证API接口响应
- 显示服务详细信息

**使用方法：**
```bash
# 执行健康检查
./scripts/health_check.sh
```

## Jenkins集成

### Jenkinsfile配置
项目根目录的 `Jenkinsfile` 已配置为使用这些脚本进行自动化部署。

**部署流程：**
1. **Git Update** - 更新代码到main分支
2. **Pre-build Checks** - 检查必要文件和Docker服务
3. **Clean Docker Resources** - 清理旧的容器和镜像
4. **Build Images** - 构建新的Docker镜像
5. **Deploy Services** - 启动服务
6. **Health Check** - 执行健康检查脚本

### Jenkins环境变量
```groovy
environment {
    PROJECT_NAME = 'datapipeline'
    DEPLOY_PATH = '/opt/deploy/dataPipeline/metisgrid'
    GIT_REPO_URL = 'https://************:8088/gitlab-instance-9025eaea/project001-libra.git'
    DOCKER_COMPOSE_FILE = 'docker-compose.prod.yml'
    FRONTEND_PORT = '8091'
    BACKEND_PORT = '8001'
}
```

## 服务配置

### 端口映射
- **前端服务**: 容器端口8080 → 主机端口8091
- **后端服务**: 容器端口8000 → 主机端口8001

### 容器名称
- **前端容器**: `datapipeline-web`
- **后端容器**: `datapipeline-django`

### 网络配置
- **网络名称**: `datapipeline_network`
- **子网**: `**********/16`
- **前端IP**: `***********`
- **后端IP**: `***********`

## 故障排除

### 常见问题

1. **权限问题**
   ```bash
   chmod +x scripts/*.sh
   ```

2. **端口占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8091
   netstat -tlnp | grep :8001
   
   # 停止占用端口的进程
   sudo kill -9 <PID>
   ```

3. **Docker资源不足**
   ```bash
   # 清理Docker资源
   docker system prune -a -f
   docker volume prune -f
   ```

4. **Git权限问题**
   ```bash
   # 配置Git凭据
   git config --global credential.helper store
   ```

### 日志查看

1. **容器日志**
   ```bash
   # 查看所有服务日志
   docker-compose -f docker-compose.prod.yml logs
   
   # 查看特定服务日志
   docker-compose -f docker-compose.prod.yml logs datapipeline-web
   docker-compose -f docker-compose.prod.yml logs datapipeline-django
   
   # 实时查看日志
   docker-compose -f docker-compose.prod.yml logs -f
   ```

2. **Jenkins日志**
   - 在Jenkins界面查看构建日志
   - 检查每个阶段的执行结果

### 手动验证

1. **服务状态**
   ```bash
   docker-compose -f docker-compose.prod.yml ps
   docker ps
   ```

2. **网络连接**
   ```bash
   curl http://localhost:8091
   curl http://localhost:8001/api/health
   ```

3. **容器内部检查**
   ```bash
   docker exec -it datapipeline-web sh
   docker exec -it datapipeline-django bash
   ```

## 安全注意事项

1. **脚本权限**: 确保脚本文件权限设置正确
2. **网络安全**: 生产环境中应配置防火墙规则
3. **数据备份**: 部署前备份重要数据
4. **访问控制**: 限制Jenkins和部署服务器的访问权限

## 更新说明

- 脚本支持参数化配置，可根据不同环境调整
- 健康检查包含详细的状态验证
- 支持跳过特定步骤以便调试
- 包含完整的错误处理和日志输出
