#!/bin/bash

# =============================================================================
# 设置脚本 - MetisGrid DataPipeline 项目
# 用于初始化脚本权限和环境配置
# =============================================================================

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

log_info "初始化项目脚本..."
echo "脚本目录: $SCRIPT_DIR"
echo "项目根目录: $PROJECT_ROOT"
echo ""

# 设置脚本权限
log_info "设置脚本执行权限..."
chmod +x "$SCRIPT_DIR"/*.sh
log_success "脚本权限设置完成"

# 检查必要工具
log_info "检查必要工具..."

tools=("git" "docker" "docker-compose" "curl")
missing_tools=()

for tool in "${tools[@]}"; do
    if ! command -v "$tool" &> /dev/null; then
        missing_tools+=("$tool")
    else
        log_success "$tool 已安装"
    fi
done

if [ ${#missing_tools[@]} -gt 0 ]; then
    log_error "以下工具未安装: ${missing_tools[*]}"
    echo ""
    echo "请安装缺失的工具："
    for tool in "${missing_tools[@]}"; do
        case $tool in
            git)
                echo "  Git: sudo apt-get install git (Ubuntu/Debian) 或 yum install git (CentOS/RHEL)"
                ;;
            docker)
                echo "  Docker: 请参考 https://docs.docker.com/engine/install/"
                ;;
            docker-compose)
                echo "  Docker Compose: sudo apt-get install docker-compose 或 pip install docker-compose"
                ;;
            curl)
                echo "  curl: sudo apt-get install curl (Ubuntu/Debian) 或 yum install curl (CentOS/RHEL)"
                ;;
        esac
    done
    exit 1
else
    log_success "所有必要工具已安装"
fi

# 检查Docker服务状态
log_info "检查Docker服务状态..."
if systemctl is-active --quiet docker; then
    log_success "Docker服务正在运行"
else
    log_warning "Docker服务未运行，尝试启动..."
    if sudo systemctl start docker; then
        log_success "Docker服务启动成功"
    else
        log_error "Docker服务启动失败"
        exit 1
    fi
fi

# 检查Docker权限
log_info "检查Docker权限..."
if docker ps &> /dev/null; then
    log_success "Docker权限正常"
else
    log_warning "当前用户无Docker权限，可能需要添加到docker组："
    echo "  sudo usermod -aG docker \$USER"
    echo "  然后重新登录或执行: newgrp docker"
fi

# 显示脚本使用说明
echo ""
echo "=================================="
echo "🎉 初始化完成！"
echo "=================================="
echo ""
echo "可用脚本："
echo "  ./scripts/deploy.sh      - 完整部署流程"
echo "  ./scripts/health_check.sh - 健康检查"
echo ""
echo "使用示例："
echo "  # 完整部署"
echo "  ./scripts/deploy.sh"
echo ""
echo "  # 跳过Git更新（仅重新构建）"
echo "  ./scripts/deploy.sh --skip-git"
echo ""
echo "  # 仅执行健康检查"
echo "  ./scripts/health_check.sh"
echo ""
echo "  # 查看帮助"
echo "  ./scripts/deploy.sh --help"
echo ""
echo "Jenkins部署："
echo "  在Jenkins中创建Pipeline项目，使用项目根目录的Jenkinsfile"
echo ""
echo "服务访问地址："
echo "  前端: http://localhost:8091"
echo "  后端: http://localhost:8001"
echo "=================================="
