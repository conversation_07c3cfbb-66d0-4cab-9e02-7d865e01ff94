#!/bin/bash

# =============================================================================
# 健康检查脚本 - MetisGrid DataPipeline 项目
# 用于检查前端和后端容器是否正常运行，以及接口是否可用
# =============================================================================

set -e  # 遇到错误立即退出

# 配置变量
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
FRONTEND_PORT="8091"
BACKEND_PORT="8081"  # 映射到主机的端口
BACKEND_CONTAINER_PORT="8001"  # 容器内部端口
FRONTEND_CONTAINER="datapipeline-web"
BACKEND_CONTAINER="datapipeline-django"
MAX_WAIT_TIME=180  # 最大等待时间（秒）
CHECK_INTERVAL=10   # 检查间隔（秒）

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker Compose服务状态
check_docker_compose_status() {
    log_info "检查Docker Compose服务状态..."
    
    if ! docker-compose -f $DOCKER_COMPOSE_FILE ps; then
        log_error "无法获取Docker Compose服务状态"
        return 1
    fi
    
    # 检查容器是否运行
    local running_containers=$(docker-compose -f $DOCKER_COMPOSE_FILE ps -q)
    if [ -z "$running_containers" ]; then
        log_error "没有运行中的容器"
        return 1
    fi
    
    log_success "Docker Compose服务状态检查通过"
    return 0
}

# 检查容器健康状态
check_container_health() {
    local container_name=$1
    log_info "检查容器 $container_name 健康状态..."
    
    # 检查容器是否存在并运行
    if ! docker ps --filter "name=$container_name" --filter "status=running" | grep -q $container_name; then
        log_error "容器 $container_name 未运行"
        return 1
    fi
    
    # 检查容器健康状态（如果有健康检查）
    local health_status=$(docker inspect --format='{{.State.Health.Status}}' $container_name 2>/dev/null || echo "none")
    if [ "$health_status" = "unhealthy" ]; then
        log_error "容器 $container_name 健康检查失败"
        return 1
    elif [ "$health_status" = "healthy" ]; then
        log_success "容器 $container_name 健康检查通过"
    else
        log_info "容器 $container_name 无健康检查配置，检查运行状态通过"
    fi
    
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service_name=$2
    local wait_time=0
    
    log_info "等待 $service_name 服务启动 (URL: $url)..."
    
    while [ $wait_time -lt $MAX_WAIT_TIME ]; do
        if curl -f -s --connect-timeout 5 --max-time 10 "$url" > /dev/null 2>&1; then
            log_success "$service_name 服务已启动并响应正常"
            return 0
        fi
        
        log_info "等待 $service_name 服务启动... (${wait_time}s/${MAX_WAIT_TIME}s)"
        sleep $CHECK_INTERVAL
        wait_time=$((wait_time + CHECK_INTERVAL))
    done
    
    log_error "$service_name 服务启动超时"
    return 1
}

# 检查前端服务
check_frontend_service() {
    log_info "检查前端服务..."
    
    # 检查容器状态
    if ! check_container_health $FRONTEND_CONTAINER; then
        return 1
    fi
    
    # 检查服务响应
    local frontend_url="http://localhost:$FRONTEND_PORT"
    if ! wait_for_service "$frontend_url" "前端"; then
        log_error "前端服务检查失败"
        return 1
    fi
    
    # 检查前端页面内容
    log_info "检查前端页面内容..."
    local response=$(curl -s --connect-timeout 5 --max-time 10 "$frontend_url" || echo "")
    if echo "$response" | grep -q "<!DOCTYPE html>"; then
        log_success "前端页面内容检查通过"
    else
        log_warning "前端页面内容可能异常"
    fi
    
    return 0
}

# 检查后端服务
check_backend_service() {
    log_info "检查后端服务..."
    
    # 检查容器状态
    if ! check_container_health $BACKEND_CONTAINER; then
        return 1
    fi
    
    # 检查健康检查接口
    local health_url="http://localhost:$BACKEND_PORT/api/health"
    if ! wait_for_service "$health_url" "后端健康检查"; then
        log_warning "后端健康检查接口不可用，尝试检查根路径..."

        # 尝试检查根路径
        local backend_url="http://localhost:$BACKEND_PORT"
        if ! wait_for_service "$backend_url" "后端"; then
            log_error "后端服务检查失败"
            return 1
        fi
    fi

    # 检查API响应
    log_info "检查后端API响应..."
    local api_response=$(curl -s --connect-timeout 5 --max-time 10 "http://localhost:$BACKEND_PORT/api/health" || echo "")
    if [ -n "$api_response" ]; then
        log_success "后端API响应正常: $api_response"
    else
        log_warning "后端API响应为空或超时，尝试检查基本连接..."
        # 尝试基本连接测试
        if curl -s --connect-timeout 5 --max-time 10 "http://localhost:$BACKEND_PORT" >/dev/null 2>&1; then
            log_success "后端基本连接正常"
        else
            log_error "后端连接失败"
            return 1
        fi
    fi
    
    return 0
}

# 显示服务信息
show_service_info() {
    log_info "显示服务详细信息..."
    
    echo "=================================="
    echo "容器状态:"
    docker-compose -f $DOCKER_COMPOSE_FILE ps
    
    echo ""
    echo "容器资源使用情况:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" $FRONTEND_CONTAINER $BACKEND_CONTAINER 2>/dev/null || true
    
    echo ""
    echo "端口映射:"
    docker port $FRONTEND_CONTAINER 2>/dev/null || true
    docker port $BACKEND_CONTAINER 2>/dev/null || true
    
    echo ""
    echo "服务访问地址:"
    echo "  前端: http://localhost:$FRONTEND_PORT"
    echo "  后端: http://localhost:$BACKEND_PORT"
    echo "=================================="
}

# 主函数
main() {
    log_info "开始执行健康检查..."
    echo "检查时间: $(date)"
    echo "项目路径: $(pwd)"
    echo ""
    
    # 检查Docker Compose状态
    if ! check_docker_compose_status; then
        log_error "Docker Compose状态检查失败"
        exit 1
    fi
    
    # 等待容器完全启动
    log_info "等待容器完全启动..."
    sleep 10
    
    # 检查前端服务
    if ! check_frontend_service; then
        log_error "前端服务检查失败"
        show_service_info
        exit 1
    fi
    
    # 检查后端服务
    if ! check_backend_service; then
        log_error "后端服务检查失败"
        show_service_info
        exit 1
    fi
    
    # 显示服务信息
    show_service_info
    
    log_success "🎉 所有健康检查通过！服务部署成功！"
    exit 0
}

# 执行主函数
main "$@"
