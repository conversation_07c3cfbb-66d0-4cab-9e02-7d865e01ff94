version: "3.8"
services:
  datapipeline-web:
    container_name: datapipeline-web
    ports:
      - "8091:8080"
    build:
      context: ./
      dockerfile: ./docker_env/web/Dockerfile.prod
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ./docker_env/nginx/my.conf:/etc/nginx/conf.d/my.conf
      - ./backend/media:/backend/media
    expose:
      - "8091"
    restart: always
    depends_on:
      - datapipeline-django
    networks:
      datapipeline_network:
        ipv4_address: ***********
    labels:
      - "project=datapipeline"

  datapipeline-django:
    build:
      context: .
      dockerfile: ./docker_env/django/Dockerfile.prod
    container_name: datapipeline-django
    working_dir: /backend
    environment:
      PYTHONUNBUFFERED: 1
      TZ: Asia/Shanghai
      DJANGO_SETTINGS_MODULE: application.settings
    volumes:
      - ./backend/logs:/backend/logs
      - ./backend/media:/backend/media
    ports:
      - "8001:8000"
    expose:
      - "8001"
    restart: always
    networks:
      datapipeline_network:
        ipv4_address: ***********
    labels:
      - "project=datapipeline"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  datapipeline_network:
    ipam:
      driver: default
      config:
        - subnet: '**********/16'
