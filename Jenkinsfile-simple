pipeline {
    agent any
    
    environment {
        PROJECT_NAME = 'MetisGrid-DataPipeline'
        DEPLOY_PATH = '/opt/deploy/dataPipeline/metisgrid'
        GIT_REPO_URL = 'https://************:8088/nizhangpeng/metisgrid.git'
        DOCKER_COMPOSE_FILE = 'docker-compose.prod.yml'
        FRONTEND_PORT = '8091'
        BACKEND_PORT = '8081'
    }
    
    stages {
        stage('Deploy to szvs7165') {
            steps {
                sshPublisher(publishers: [
                    sshPublisherDesc(
                        configName: 'szvs7165', 
                        transfers: [
                            sshTransfer(
                                cleanRemote: false, 
                                excludes: '', 
                                execCommand: """
                                    echo "🚀 开始部署 MetisGrid DataPipeline"
                                    echo "部署时间: \$(date)"
                                    echo "========================================"
                                    
                                    # 更新代码
                                    if [ ! -d "${DEPLOY_PATH}" ]; then
                                        echo "📥 克隆代码到 ${DEPLOY_PATH}"
                                        mkdir -p \$(dirname "${DEPLOY_PATH}")
                                        git clone "${GIT_REPO_URL}" "${DEPLOY_PATH}"
                                    else
                                        echo "📥 更新现有代码"
                                        cd "${DEPLOY_PATH}"
                                        git fetch origin
                                        git checkout main
                                        git reset --hard origin/main
                                        git pull origin main
                                    fi
                                    
                                    cd "${DEPLOY_PATH}"
                                    echo "当前版本: \$(git log --oneline -1)"
                                    
                                    # 停止现有服务
                                    echo "🛑 停止现有服务"
                                    docker-compose -f "${DOCKER_COMPOSE_FILE}" down --remove-orphans || true
                                    
                                    # 清理镜像
                                    echo "🧹 清理Docker镜像"
                                    docker images | grep datapipeline | awk '{print \$3}' | xargs -r docker rmi -f || true
                                    docker image prune -f || true
                                    
                                    # 预构建前端（如果需要）
                                    if [ -d "web" ] && [ -f "web/package.json" ]; then
                                        echo "🔨 预构建前端"
                                        cd web
                                        if command -v pnpm >/dev/null 2>&1; then
                                            pnpm install --frozen-lockfile
                                            pnpm build
                                        elif command -v npm >/dev/null 2>&1; then
                                            npm ci
                                            npm run build
                                        fi
                                        cd ..
                                    fi
                                    
                                    # 构建并启动服务
                                    echo "🏗️ 构建Docker镜像"
                                    docker-compose -f "${DOCKER_COMPOSE_FILE}" build --no-cache --parallel
                                    
                                    echo "🚀 启动服务"
                                    docker-compose -f "${DOCKER_COMPOSE_FILE}" up -d
                                    
                                    # 等待服务启动
                                    echo "⏳ 等待服务启动..."
                                    sleep 30
                                    
                                    # 健康检查
                                    echo "🏥 健康检查"
                                    docker-compose -f "${DOCKER_COMPOSE_FILE}" ps
                                    
                                    # 检查前端
                                    for i in \$(seq 1 6); do
                                        if curl -f -s http://localhost:${FRONTEND_PORT} >/dev/null 2>&1; then
                                            echo "✅ 前端服务正常 (端口${FRONTEND_PORT})"
                                            break
                                        fi
                                        echo "⏳ 前端检查 \$i/6..."
                                        sleep 10
                                    done
                                    
                                    # 检查后端
                                    for i in \$(seq 1 6); do
                                        if curl -f -s http://localhost:${BACKEND_PORT} >/dev/null 2>&1; then
                                            echo "✅ 后端服务正常 (端口${BACKEND_PORT})"
                                            break
                                        fi
                                        echo "⏳ 后端检查 \$i/6..."
                                        sleep 10
                                    done
                                    
                                    # 清理资源
                                    echo "🧽 清理未使用资源"
                                    docker image prune -f || true
                                    docker system prune -f || true
                                    
                                    # 显示结果
                                    echo "========================================"
                                    echo "🎉 部署完成！"
                                    echo "📱 前端: http://localhost:${FRONTEND_PORT}"
                                    echo "🔧 后端: http://localhost:${BACKEND_PORT}"
                                    echo "📅 时间: \$(date)"
                                    echo "📝 版本: \$(git log --oneline -1)"
                                    echo "========================================"
                                """, 
                                execTimeout: 600000, 
                                flatten: false, 
                                makeEmptyDirs: false, 
                                noDefaultExcludes: false, 
                                patternSeparator: '[, ]+', 
                                remoteDirectory: '/', 
                                remoteDirectorySDF: false, 
                                removePrefix: '', 
                                sourceFiles: ''
                            )
                        ], 
                        usePromotionTimestamp: false, 
                        useWorkspaceInPromotion: false, 
                        verbose: true
                    )
                ])
            }
        }
    }
    
    post {
        success {
            echo "🎉 Jenkins部署成功！"
            echo "前端访问: http://localhost:${FRONTEND_PORT}"
            echo "后端访问: http://localhost:${BACKEND_PORT}"
        }
        failure {
            echo "❌ Jenkins部署失败！请检查日志"
        }
    }
}
