services:
  postgres_db: # 服务名称
    image: postgres:latest # 指定镜像及其版本
    container_name: dataPipeline_postgres # 指定容器的名称
    restart: always
    environment:
      POSTGRES_USER: admin          # 新增：设置用户名
      POSTGRES_PASSWORD: admin123456  # 修改：密码改为 admin123456
      POSTGRES_DB: dataPipeline  # 添加这行，容器启动时会自动创建这个数据库
      #POSTGRES_DB: default
    ports: # 端口映射
      - "5431:5432"                 # 修改：将主机的 5431 映射到容器的 5432
    volumes: # 数据持久化的配置
      - data:/var/lib/postgresql/data
      - log:/var/log/postgresql
    logging:
      options:
        max-size: "10m"
        max-file: "3"
    networks:  # 网络配置
      - pub-network  # 加入到 pub-network 网络

volumes: # 数据卷
  data:
  log:

networks:  # 网络
  pub-network:
    external: true