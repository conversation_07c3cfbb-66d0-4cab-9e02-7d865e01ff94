# FROM artifactory-esc.corp.knorr-bremse.com:8481/docker-kb/libra-backend-base-root:20250214
FROM artifactory-esc.corp.knorr-bremse.com:8481/docker-kb/libra-backend-base-root:20250214

WORKDIR /backend

# 复制pip配置
COPY pip.conf /etc/

# 打印 Python 版本 和 系统信息
RUN python3 --version && \
    python3 -c "import platform; print('Python Full Version:', platform.python_version()); print('System:', platform.system(), 'Release:', platform.release(), 'Machine:', platform.machine())" && \
    cat /etc/os-release || true
    
# 复制requirements文件并安装依赖（利用Docker缓存）
COPY ./backend/requirements.txt .
RUN python3 -m pip install -r requirements.txt

# 复制应用代码
COPY ./backend/ .

# 重新编译
RUN /bin/sh -c "cd ./data_pipeline/c_files && ./build.sh"

# 复制环境配置文件
RUN if [ -f ./conf/env_prod.py ]; then cp -f ./conf/env_prod.py ./conf/env.py; fi

# 创建必要的目录
RUN mkdir -p logs media static

# 设置权限
RUN chmod +x docker_start.sh

# 添加健康检查端点支持
RUN echo "from django.http import JsonResponse\nfrom django.urls import path\n\ndef health_check(request):\n    return JsonResponse({'status': 'healthy'})\n\nurlpatterns = [path('api/health', health_check)]" > health_urls.py

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["sh", "docker_start.sh"]
