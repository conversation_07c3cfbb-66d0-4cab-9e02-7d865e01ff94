# 多阶段构建 - 构建阶段
FROM artifactory-esc.corp.knorr-bremse.com:8481/docker-kb/libra-frontend-base-root-clean:20250217 AS builder

WORKDIR /web/

# # 复制package文件并安装依赖（利用Docker缓存）
# COPY web/package.json ./
# # 尝试复制pnpm-lock.yaml（如果存在）
# COPY web/pnpm-lock.yaml* ./

# 复制源代码并构建
COPY web/. .
RUN pnpm install --registry=https://artifactory-esc.corp.knorr-bremse.com:8482/artifactory/api/npm/npm-public
RUN pnpm build

# 生产阶段 - Nginx
FROM artifactory-esc.corp.knorr-bremse.com:8481/docker-public/nginx:alpine

# 复制构建产物
COPY --from=builder /web/dist /usr/share/nginx/html

# 复制nginx配置
COPY ./docker_env/nginx/my.conf /etc/nginx/conf.d/my.conf

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/ || exit 1

# 暴露端口
EXPOSE 8080

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
