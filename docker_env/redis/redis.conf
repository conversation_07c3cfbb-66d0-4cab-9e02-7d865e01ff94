### 指定redis绑定的主机地址，注释掉这部分，使redis可以外部访问
# bind 127.0.0.1 -::1
### 指定访问redis服务端的端口
port 6379
### 指定客户端连接redis服务器时，当闲置的时间为多少（如300）秒时关闭连接（0表示禁用）
timeout 0
### 默认情况下，Redis不作为守护进程运行。如果需要，请使用“yes”
daemonize no
### 给redis设置密码，不需要密码的话则注释
# requirepass dvadmin
### 开启redis持久化，默认为no
appendonly yes
### 防止出现远程主机强迫关闭了一个现有的连接的错误 默认是300
tcp-keepalive 300
### 指定redis数据库的日志级别，常用的日志级别有debug、verbose、notice、warning，不进行修改的情况下默认的是notice
loglevel notice
### 指定redis数据库多长时间内（s）有多少次（c）更新操作时就把缓存中的数据同步到本地库，比如：save 600 2，指的是10分钟内有2次更新操作，就同步到本地库
save 600 2
### 指定redis的最大内存。由于Redis 在启动时会把数据加载到内存中，当数据达到最大内存时，redis会自动把已经到期和即将到期的key值。所以可以根据需求调整自己的所需的最大内存
maxmemory 500mb
### 设置了maxmemory的选项，redis内存使用达到上限。可以通过设置LRU算法来删除部分key，释放空间。默认是按照过期时间的,如果set时候没有加上过期时间就会导致数据写满maxmemory
maxmemory-policy volatile-lru
### 设置外部网络连接redis服务，开启需配置bind ip或者设置访问密码，关闭此时外部网络可以直接访问
# protected-mode yes
