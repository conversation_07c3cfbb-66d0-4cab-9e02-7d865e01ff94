FROM registry.cn-zhangjiakou.aliyuncs.com/dvadmin-pro/dvadmin3-base-backend:latest
WORKDIR /backend
# 删除 COPY 指令，由 docker-compose 挂载代码
COPY backend/conf/ /backend/conf/
COPY backend/requirements.txt /backend/requirements.txt
RUN python3 -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements.txt
CMD ["python3", "-m", "celery", "-A", "application", "worker", "-B", "--loglevel=info"]