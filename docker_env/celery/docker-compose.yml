version: '3.8'

services:
  celery-worker:
    build:
      context: ../../
      dockerfile: docker_env/celery/Dockerfile
    container_name: dataPipeline_celery
    restart: always
    environment:
      - ENV_FILE_EXISTS=true
    volumes:
      - ../../backend:/backend
      - celery_data:/data
    networks:
      - pub-network

  flower:
    build:
      context: ../../
      dockerfile: docker_env/celery/Dockerfile
    container_name: dataPipeline_celery_flower
    restart: always
    command: python3 -m celery -A application flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - ENV_FILE_EXISTS=true
    volumes:
      - ../../backend:/backend
      - celery_data:/data
    networks:
      - pub-network
    depends_on:
      - celery-worker

volumes:
  celery_data:

networks:
  pub-network:
    external: true