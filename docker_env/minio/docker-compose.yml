version: '3.8'

services:
  minio:
    image: minio/minio:latest
    container_name: dataPipeline_minio
    environment:
      MINIO_ROOT_USER: admin         # 设置 MinIO 的访问密钥（类似 AWS AccessKey）
      MINIO_ROOT_PASSWORD: admin123456  # 设置 MinIO 的私钥（类似 AWS SecretKey）
    ports:
      - "9000:9000"                  # S3 API 端口
      - "9001:9001"                  # Web 控制台端口
    volumes:
      - minio_data:/data            # 数据持久化
    command: server /data --console-address :9001  # 启动命令，指定控制台端口
    networks:
      - pub-network
    restart: always

volumes:
  minio_data:

networks:
  pub-network:
    external: true