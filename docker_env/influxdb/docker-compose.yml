version: '3.8'

services:
  influxdb:
    image: influxdb:latest  # 你可以使用 latest 或指定版本
    container_name: dataPipeline_influxdb
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=admin123456
      - DOCKER_INFLUXDB_INIT_ORG=kb
      - DOCKER_INFLUXDB_INIT_BUCKET=data-pipeline
      - DOCKER_INFLUXDB_INIT_RETENTION=1d  # 可选：数据保留策略
    ports:
      - "8086:8086"  # InfluxDB HTTP API 和 Web UI 端口
    volumes:
      - influxdb_data:/var/lib/influxdb2
      - influxdb_config:/etc/influxdb2
    networks:
      - pub-network
    restart: always

volumes:
  influxdb_data:
  influxdb_config:

networks:  # 网络
  pub-network:
    external: true