const http = require('http');

const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'text/plain' });
  res.end('Hello from port 8001\n');
});

server.listen(8001, '0.0.0.0', () => {
  console.log('✅ 成功监听在 8001 端口');
  console.log('你可以访问 http://localhost:8001 测试');

  // 自动关闭服务器（可选）
  setTimeout(() => {
    server.close(() => {
      console.log('🔌 服务器已自动关闭');
    });
  }, 3000); // 3秒后关闭
});

server.on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.error('❌ 8001 端口已被占用');
  } else if (err.code === 'EACCES') {
    console.error('🚫 没有权限监听 8001 端口（可能需要 root 权限）');
  } else {
    console.error('⚠️ 监听失败:', err.message);
  }
  process.exit(1);
});