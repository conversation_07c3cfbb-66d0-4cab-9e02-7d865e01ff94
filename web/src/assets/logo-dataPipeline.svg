<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"
     version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink">
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#60A5FA"/>
      <stop offset="100%" style="stop-color:#10B981"/>
    </linearGradient>
  </defs>

  <!-- Logo图形：抽象的数据处理符号 -->
  <g transform="translate(35, 15)">
    <!-- 外部六边形 -->
    <path d="M65 0 L125 35 L125 105 L65 140 L5 105 L5 35 Z" 
          fill="none" 
          stroke="url(#logoGradient)" 
          stroke-width="4"/>
    
    <!-- 内部图形 -->
    <path d="M65 30 L105 55 L105 85 L65 110 L25 85 L25 55 Z" 
          fill="none" 
          stroke="url(#logoGradient)" 
          stroke-width="4"/>
    
    <!-- 连接线 -->
    <line x1="65" y1="0" x2="65" y2="30" stroke="url(#logoGradient)" stroke-width="4"/>
    <line x1="125" y1="35" x2="105" y2="55" stroke="url(#logoGradient)" stroke-width="4"/>
    <line x1="125" y1="105" x2="105" y2="85" stroke="url(#logoGradient)" stroke-width="4"/>
    <line x1="65" y1="140" x2="65" y2="110" stroke="url(#logoGradient)" stroke-width="4"/>
    <line x1="5" y1="105" x2="25" y2="85" stroke="url(#logoGradient)" stroke-width="4"/>
    <line x1="5" y1="35" x2="25" y2="55" stroke="url(#logoGradient)" stroke-width="4"/>
    
    <!-- 中心点 -->
    <circle cx="65" cy="70" r="10" fill="url(#logoGradient)"/>
  </g>

  <!-- MetisGrid文字 -->
  <text x="100" y="200" 
        font-family="Arial, sans-serif" 
        font-size="40" 
        font-weight="bold"
        fill="#60A5FA" 
        text-anchor="middle">
    MetisGrid
  </text>
</svg>