/* 定义新的主题色变量 */
:root {
  --primary-color: #1A3A5D; /* 深沉海军蓝 */
  --secondary-color: #eaeaea; 
  --highlight-color: #ffcc40; 
  --font-color: #333; 
  --font-color-light: #fff;
  --transition-duration: 0.2s;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  background-color: #f7f9fa;
  color: var(--font-color);
  margin: 0;
  transition: background-color var(--transition-duration);
}

/* 顶部导航栏 */
header {
  background-color: var(--primary-color);
  height: 60px;
  display: flex;
  align-items: center;
  color: var(--font-color-light);
  padding: 0 20px;
}

header .logo {
  font-size: 1.4rem;
  font-weight: bold;
  color: var(--font-color-light);
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-right: auto;
}

nav ul {
  list-style: none;
  display: flex;
  margin: 0;
  padding: 0;
}

nav ul li {
  margin-left: 20px;
}

nav ul li a {
  color: var(--font-color-light);
  text-decoration: none;
  transition: color var(--transition-duration);
}

nav ul li a:hover {
  color: var(--highlight-color);
}

/* 页面主体布局 */
.main-container {
  padding: 20px;
}

/* 常见按钮样式 */
button.primary-btn {
  background-color: var(--primary-color);
  color: var(--font-color-light);
  border: none;
  padding: 10px 16px;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color var(--transition-duration), box-shadow var(--transition-duration);
}

button.primary-btn:hover {
  background-color: #16324d;
  box-shadow: 0 2px 6px rgba(26, 58, 93, 0.3);
} 