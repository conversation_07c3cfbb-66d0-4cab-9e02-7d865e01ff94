import { request } from "/@/utils/service";

/**
 * 获取所有项目list
 */
export function getProjects() {
  return request({
    url: '/api/data_pipeline/project/',
    method: 'get',
  });
}

/**
 * 添加单个项目
  {
      "project_name": "40",
      "customer_name": "41",
      "project_number": "42",
      "template_type": 0
  }
 */
export function addProject(data: object) {
  return request({
    url: '/api/data_pipeline/project/',
    method: 'post',
    data: data
  });
}

/**
 * 删除单个项目
  "id": 17
*/
export function delSingleProject(id: number) {
  console.log('删除单个项目 id = ', id)
  return request({
    url: `/api/data_pipeline/project/${id}/`,
    method: 'delete',
  });
}

/**
 * 删除多个项目
  {
  "keys": [14, 13]  // 要删除的项目id列表
  }
*/
export function delMultiProjects(data: object) {
  console.log('删除多个项目 data = ', data)
  return request({
    url: '/api/data_pipeline/project/multiple_delete/',
    method: 'delete',
    data: data
  });
}

/**
* 修改更新单个项目
data: {
  "id": 17
  "project_number": "4040",
  "project_name": "4141",
  "customer_name": "42",
  "template_type": 0
}
*/
export function updateSingleProject(data: object) {
  console.log('更新单个项目 data = ', data)
  return request({
    url: `/api/data_pipeline/project/${data.id}/`,
    method: 'put',
    data: data
  });
}
