<template>
    <!-- 修改1: 多个信号显示的x轴问题 -->
	<div class="mt-0">
		<!-- 这里添加数据可视化的内容 -->
		<!-- 点击, 弹窗显示 节点类型 |> 节点名称 |> 信号 -->

		<!-- 当从文件列表点击进入时显示文件信息 -->
		<div v-if="props.isFromClick && props.fileData" class="mb-6">
			<!-- 文件信息卡片 -->
			<div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 shadow-sm overflow-hidden">
				<!-- 卡片头部 -->
				<div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
					<div class="flex items-center space-x-3">
						<div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
							<svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
							</svg>
						</div>
						<h4 class="text-lg font-semibold text-white">文件信息</h4>
					</div>
				</div>
				
				<!-- 卡片内容 -->
				<div class="p-6">
					<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						<!-- 基本信息组 -->
						<div class="space-y-4">
							<h5 class="text-sm font-medium text-gray-700 uppercase tracking-wide flex items-center space-x-2">
								<svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								<span>基本信息</span>
							</h5>
							<div class="space-y-3">
								<div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100 shadow-sm">
									<span class="text-sm font-medium text-gray-600">文件名</span>
									<span class="text-sm text-gray-900 font-mono">{{ props.fileData.name || '-' }}</span>
								</div>
								<div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100 shadow-sm">
									<span class="text-sm font-medium text-gray-600">文件大小</span>
									<span class="text-sm text-gray-900">{{ props.fileData.size / 1024 + ' ' + 'KB' || '-' }}</span>
								</div>
							</div>
						</div>

						<!-- 时间信息组 -->
						<div class="space-y-4">
							<h5 class="text-sm font-medium text-gray-700 uppercase tracking-wide flex items-center space-x-2">
								<svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
								</svg>
								<span>时间信息</span>
							</h5>
							<div class="space-y-3">
								<div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100 shadow-sm">
									<span class="text-sm font-medium text-gray-600">上传时间</span>
									<span class="text-sm text-gray-900">{{ formatDate2(props.fileData.last_modified) || '-' }}</span>
								</div>
								<div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100 shadow-sm">
									<span class="text-sm font-medium text-gray-600">开始时间</span>
									<span class="text-sm text-gray-900">{{ props.fileData.start_time || '-' }}</span>
								</div>
								<div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100 shadow-sm">
									<span class="text-sm font-medium text-gray-600">结束时间</span>
									<span class="text-sm text-gray-900">{{ props.fileData.end_time || '-' }}</span>
								</div>
							</div>
						</div>

						<!-- 系统信息组 -->
						<div class="space-y-4">
							<h5 class="text-sm font-medium text-gray-700 uppercase tracking-wide flex items-center space-x-2">
								<svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
								</svg>
								<span>系统信息</span>
							</h5>
							<div class="space-y-3">
								<div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100 shadow-sm">
									<span class="text-sm font-medium text-gray-600">MinIo ID</span>
									<span class="text-sm text-gray-900 font-mono text-xs">{{ props.fileData.file_Minio_version_id || '-' }}</span>
								</div>
								<div class="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100 shadow-sm">
									<span class="text-sm font-medium text-gray-600">InfluxDB Tag</span>
									<span class="text-sm text-gray-900 font-mono text-xs">{{ props.fileData.influx_ubique_tagId || '-' }}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="flex items-center space-x-4 w-full">
		<el-form :inline="true" class="w-full flex items-center justify-between">
			<!-- 左侧：时间选择器 -->
			<el-form-item class="mb-0">
			<!-- 时间选择器 -->
			<el-date-picker
				v-model="timeRange"
				type="datetimerange"
				range-separator="to"
				start-placeholder="Start Time"
				end-placeholder="End Time"
				:default-time="[
				new Date(2000, 1, 1, 0, 0, 0),
				new Date(2000, 1, 1, 23, 59, 59),
				]"
				value-format="YYYY-MM-DD HH:mm:ss"
				:time-zone="'Asia/Shanghai'"
				class="w-full"
				@change="handleTimeRangeConfirm"
			/>
			</el-form-item>

			<!-- 右侧：按钮组 -->
			<div class="flex space-x-1">
			<el-form-item class="mb-0">
				<el-button type="primary" @click="showNodeData">Signals</el-button>
			</el-form-item>
			<el-form-item class="mb-0">
				<el-button type="primary" @click="updateEchart">Query</el-button>
			</el-form-item>
			<el-form-item class="mb-0">
				<el-button type="success" @click="exportToExcel">
				<el-icon><Download /></el-icon>
				Export
				</el-button>
			</el-form-item>
			</div>
		</el-form>
		</div>
		<div>
			<!-- <h3 class="text-lg font-medium mb-4">数据可视化</h3> -->
			
			<!-- 添加弹窗, 各区域的节点/信号选择 -->
			<el-dialog
				v-model="dialogVisible"
				title="Selection of Nodes - Signals"
				width="80%"
				:before-close="handleClose"
				class="signal-selection-dialog"
			>
				<div class="flex justify-between space-x-6">
					<!-- 区域1：节点类型 -->
					<div class="flex-1">
						<div class="section-header">
							<h4 class="text-base font-medium text-gray-700">Node Type</h4>
						</div>
						<el-scrollbar height="450px" class="custom-scrollbar">
							<div class="signal-list">
								<div
									v-for="type in Object.keys(nodeData)"
									:key="type"
									:class="[
										'signal-item',
										{ 'multi-selected': selectedType === type }
									]"
									@click="handleTypeSelect(type)"
								>
									{{ type }}
								</div>
							</div>
						</el-scrollbar>
					</div>

					<!-- 区域2：节点名称 -->
					<div class="flex-1">
						<div class="section-header">
							<h4 class="text-base font-medium text-gray-700">Node Name</h4>
						</div>
						<el-scrollbar height="450px" class="custom-scrollbar">
							<div class="signal-list">
								<div
									v-for="nodeName in Object.keys(nodeData[selectedType] || {})"
									:key="nodeName"
									:class="[
										'signal-item',
										{ 'multi-selected': selectedNode === nodeName }
									]"
									@click="handleNodeSelect(nodeName)"
								>
									{{ nodeName }}
								</div>
							</div>
						</el-scrollbar>
					</div>

					<!-- 区域3：信号列表 -->
					<div class="flex-1 relative">
						<div class="section-header">
							<h4 class="text-base font-medium text-gray-700">Optional Signals</h4>
						</div>
						<el-scrollbar height="450px" class="custom-scrollbar">
							<div class="signal-list">
								<div
									v-for="signal in nodeData[selectedType]?.[selectedNode] || []"
									:key="signal"
									:class="[
										'signal-item',
										{
											'multi-selected': selectedSignals.includes(signal)
										}
									]"
									@click="handleSignalSelect(signal, $event)"
								>
									{{ signal }}
								</div>
							</div>
						</el-scrollbar>
						<div class="transfer-buttons">
							<span
								class="transfer-btn"
								@click="addToFinal"
								:title="selectedSignals.length > 0 ? `Add ${selectedSignals.length} signals` : 'Add selected signal'"
							>
								&gt;&gt;
							</span>
							<span
								class="transfer-btn"
								@click="removeFromFinal"
								:title="selectedFinalSignals.length > 0 ? `Remove ${selectedFinalSignals.length} signals` : 'Remove selected signal'"
							>
								&lt;&lt;
							</span>
						</div>
					</div>

					<!-- 区域4：已选信号 -->
					<div class="flex-1">
						<div class="section-header">
							<h4 class="text-base font-medium text-gray-700">Selected Signals</h4>
						</div>
						<el-scrollbar height="450px" class="custom-scrollbar">
							<div class="signal-list">
								<div
									v-for="item in finalSelectedSignals"
									:key="item.signal"
									:class="[
										'signal-item final-signal',
										{
											'multi-selected': selectedFinalSignals.includes(item)
										}
									]"
									@click="handleFinalSignalSelect(item, $event)"
								>
									<div class="signal-info">
										<div class="signal-type">{{ item.nodeType }}</div>
										<div class="signal-name">{{ item.nodeName }}</div>
										<div class="signal-value">{{ item.signal }}</div>
									</div>
								</div>
							</div>
						</el-scrollbar>
					</div>
				</div>

				<template #footer>
					<span class="dialog-footer">
						<el-button @click="dialogVisible = false">Cancel</el-button>
						<el-button type="primary" @click="handleConfirm">Confirm</el-button>
					</span>
				</template>
			</el-dialog>
			<div 
				id="echartContainer" 
				class="echart-container border border-gray-300 rounded-lg shadow-sm bg-white"
				style="width: 100%; height: 400px;"
			></div>
			<!-- 计算方法选择select, 选项有: 短时傅里叶变换STFT / 小波变换WT / 傅里叶变换FFT / 希尔伯特-黄变换-->
			<div class="flex justify-end mb-3 mt-5">
				<el-select
					v-model="calculatedMethod"
					placeholder="Select Calculated Method"
					class="bg-white rounded shadow-sm"
					style="min-width: 100px; width: 250px;"
					size="large"
					popper-class="bg-white"
					@change="handleCalculatedMethodChange"
				>
					<el-option label="短时傅里叶变换" value="STFT"></el-option>
					<el-option label="小波变换" value="WT"></el-option>
					<el-option label="傅里叶变换" value="FFT"></el-option>
					<el-option label="希尔伯特-黄变换" value="HHT"></el-option>
				</el-select>
			</div>
			<div class="flex justify-between">
				<div 
					id="calculatedEchartContainer" 
					class="echart-container border border-gray-300 rounded-lg shadow-sm bg-white"
					style="width: 70%; height: 400px; margin-right: 10px;"
				></div>
				<div 
					class="echart-container border border-gray-300 rounded-lg shadow-sm bg-white overflow-auto"
					style="width: 30%; height: 400px;"
				>
					<div class="p-5 font-sans">
						<h3 class="mb-4 text-lg font-medium text-gray-800">
							{{ calculatedMethod === 'WT' ? '尺度信息' : '频率统计信息' }}
						</h3>
						
						<!-- 小波变换尺度信息 -->
						<div v-if="calculatedMethod === 'WT' && waveletScaleInfo.length > 0">
							<!-- 统计信息 -->
							<div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
								<div class="text-sm text-blue-800">
									<div class="flex justify-between items-center mb-1">
										<span class="font-medium">信号统计:</span>
										<span class="text-blue-600 font-semibold">
											{{ waveletScaleInfo.filter(s => s.visible).length }} / {{ waveletScaleInfo.length }} 个信号可见
										</span>
									</div>
									<div class="flex justify-between items-center">
										<span class="font-medium">尺度统计:</span>
										<span class="text-blue-600 font-semibold">
											{{ waveletScaleInfo.reduce((total, s) => total + s.scales.filter(scale => scale.visible).length, 0) }} / 
											{{ waveletScaleInfo.reduce((total, s) => total + s.scales.length, 0) }} 个尺度可见
										</span>
									</div>
									<!-- 调试信息：显示当前可见的尺度 -->
									<div class="mt-2 text-xs text-blue-600">
										<div class="font-medium mb-1">可见尺度列表:</div>
										<div class="max-h-20 overflow-y-auto">
											{{ waveletScaleInfo
												.filter(s => s.visible)
												.flatMap(s => s.scales.filter(scale => scale.visible))
												.map(scale => scale.name)
												.join(', ') || '无可见尺度' }}
										</div>
									</div>
								</div>
							</div>
							
							<div class="mb-3 text-sm text-gray-600">
								点击信号名称控制该信号所有尺度的显示/隐藏，点击尺度项控制单个尺度
							</div>
							<div class="mb-3 flex gap-2 flex-wrap">
								<!-- <button 
									@click="selectAllScales"
									class="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
								>
									全选
								</button>
								<button 
									@click="deselectAllScales"
									class="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
								>
									全不选
								</button> -->
								<button 
									@click="selectAllSignals"
									class="px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
								>
									全选
								</button>
								<button 
									@click="deselectAllSignals"
									class="px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
								>
									全不选
								</button>
							</div>
							<div 
								v-for="signalInfo in waveletScaleInfo" 
								:key="signalInfo.signalName"
								class="mb-5 p-3 border border-gray-200 rounded-lg bg-gray-50"
							>
								<!-- 信号名称点击控制 -->
								<div 
									class="mb-3 p-2 border border-gray-300 rounded cursor-pointer transition-colors duration-200"
									:class="{ 
										'bg-green-100 border-green-400': signalInfo.visible,
										'bg-gray-100 border-gray-300 hover:bg-gray-200': !signalInfo.visible
									}"
									@click="toggleSignalVisibility(signalInfo.signalName)"
								>
									<div class="flex items-center justify-between">
										<div class="flex-1">
											<div class="font-medium text-base" :class="{ 'text-green-700': signalInfo.visible }">
												{{ signalInfo.signalName }}
											</div>
											<div class="text-xs text-gray-500 mt-1">
												尺度数量: {{ signalInfo.scales.length }} | 
												可见尺度: {{ signalInfo.scales.filter(s => s.visible).length }}
											</div>
										</div>
										<div class="ml-2">
											<div class="w-4 h-4 rounded-full transition-colors duration-200" 
												:class="signalInfo.visible ? 'bg-green-500' : 'bg-gray-300'">
											</div>
										</div>
									</div>
								</div>
								
								<!-- 尺度列表 -->
								<div class="space-y-2 text-sm max-h-48 overflow-y-auto">
									<div 
										v-for="scale in signalInfo.scales" 
										:key="scale.key"
										class="flex items-center justify-between p-2 border border-gray-300 rounded cursor-pointer transition-colors duration-200"
										:class="{ 
											'bg-blue-100 border-blue-400': scale.visible,
											'bg-gray-50 border-gray-300 hover:bg-gray-100': !scale.visible
										}"
										@click.stop="toggleScaleVisibility(scale.key)"
									>
										<div class="flex-1">
											<div class="font-medium" :class="{ 'text-blue-700': scale.visible }">
												{{ scale.name }}
											</div>
											<div class="text-xs text-gray-500">
												尺度: {{ scale.scale.toFixed(2) }} | 频率: {{ scale.frequency.toFixed(4) }} Hz
											</div>
										</div>
										<div class="ml-2">
											<div class="w-3 h-3 rounded-full transition-colors duration-200" 
												:class="scale.visible ? 'bg-green-500' : 'bg-gray-300'">
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						
						<!-- 其他变换方法的频率统计信息 -->
						<div v-else-if="calculatedMethod !== 'WT' && frequencyStatistics.length > 0">
							<div 
								v-for="stat in frequencyStatistics" 
								:key="stat.name"
								class="mb-5 p-3 border border-gray-200 rounded-lg bg-gray-50"
							>
								<h4 class="mb-3 text-base font-medium text-gray-700">{{ stat.name }}</h4>
								<div class="space-y-2 text-sm">
									<p><span class="font-medium">总频率点数:</span> {{ stat.totalPoints }}</p>
									<p><span class="font-medium">负频率点数:</span> {{ stat.negativePoints }}</p>
									<p><span class="font-medium">零频率点数:</span> {{ stat.zeroPoints }}</p>
									<p><span class="font-medium">正频率点数:</span> {{ stat.positivePoints }}</p>
									<p v-if="stat.negativeRange">
										<span class="font-medium">负频率范围:</span> {{ stat.negativeRange }}
									</p>
									<p v-if="stat.positiveRange">
										<span class="font-medium">正频率范围:</span> {{ stat.positiveRange }}
									</p>
									<p><span class="font-medium">最大幅度:</span> {{ stat.maxMagnitude.toFixed(4) }}</p>
								</div>
							</div>
						</div>
						
						<!-- 无数据状态 -->
						<div v-else class="text-gray-500 text-center py-8">
							{{ calculatedMethod === 'WT' ? '暂无尺度信息' : '暂无统计信息' }}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
// 可以引入图表库,如ECharts
import { ref, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Session } from '/@/utils/storage';
import { getBaseURL } from '/@/utils/baseUrl';
import * as echarts from 'echarts';
import { Download } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { dataPipeline } from '/@/stores/dataPipeline';
import dayjs from 'dayjs';

// 定义 props
interface Props {
  fileData?: any;
  isFromClick?: boolean;
}

// 计算方法选择
const calculatedMethod = ref('FFT')

// 缓存ID
const cache_id = ref('')

const handleCalculatedMethodChange = async (value: string) => {
	console.log('频率分析 刷新 value = ', value)
	const data = {
		"calculated_method": value,
		"queryType": "fromRedis",
		"cache_id": cache_id.value
	}
	
	try {
		const response = await fetch(`${baseBackendUrl}api/data_pipeline/file/influxdb/getStatistics/`, {
			method: 'POST',
			body: JSON.stringify(data),
			credentials: 'include',
			headers: {
				'Content-Type': 'application/json',
				"Authorization": 'JWT ' + Session.get('token') 
			},
		});
		
		const result = await response.json();
		
		if (result.code === 2000) {
			// 显示傅立叶变换结果
			displayFourierResults(result.data, value);
		} else {
			ElMessage.error(result.msg || '分析失败');
		}
	} catch (error) {
		console.error('傅立叶分析错误:', error);
		ElMessage.error('分析请求失败');
	}
}
let calculatedEchart: any = null
// 显示傅立叶变换结果的函数
const displayFourierResults = (data: any[], method: string) => {
	console.log('频率分析 刷新 data = ', data)
	if (!calculatedEchart) {
        calculatedEchart = echarts.init(document.getElementById('calculatedEchartContainer'));
    }

    calculatedEchart.clear();

	// 数据预处理：插入 null 分隔大跨度区域
	function processFourierData(signalData: any[]) {
		const processed: any[] = [];
		if (signalData.length === 0) return processed;
		
		// 后端已经确保数据按频率排序，直接处理所有数据
		for (let i = 0; i < signalData.length; i++) {
			processed.push([
				parseFloat(signalData[i].time),
				parseFloat(signalData[i].value)
			]);
		}

		return processed;
	}

	// 处理小波变换数据：将按尺度分组的数据转换为系列数据
	function processWaveletData(signalData: any) {
		const series: any[] = [];
		
		// 检查是否有尺度分组数据
		if (signalData.scale_groups) {
			// 处理按尺度分组的小波变换数据
			Object.keys(signalData.scale_groups).forEach(scaleKey => {
				const scaleGroup = signalData.scale_groups[scaleKey];
				const processedData = processFourierData(scaleGroup.data);
				
				series.push({
					name: scaleGroup.name,
					type: 'line',
					data: processedData,
					smooth: true,
					showSymbol: false,
					lineStyle: {
						width: 1.5
					}
				});
			});
		} else {
			// 兼容旧的数据格式
			const processedData = processFourierData(signalData.data || []);
			series.push({
				name: signalData.name,
				type: 'line',
				data: processedData,
				smooth: true,
				showSymbol: false,
				lineStyle: {
					width: 2
				}
			});
		}
		
		return series;
	}

	// 准备数据
	let series: any[] = [];
	
	// 根据方法类型选择不同的数据处理方式
	if (method === 'WT') {
		// 小波变换：处理每个信号的尺度分组数据
		data.forEach(signal => {
			const signalSeries = processWaveletData(signal);
			series = series.concat(signalSeries);
		});
	} else {
		// 其他变换方法：使用原有逻辑
		series = data.map(signal => {
			const signalData = signal.data || [];
			return {
				name: signal.name,
				type: 'line',
				data: processFourierData(signalData),
				smooth: true,
				showSymbol: false,
				lineStyle: {
					width: 2
				}
			};
		});
	}
	
	// 配置选项
	const option = {
		title: {
			text: method === 'WT' ? `${method} 分析结果 - 尺度功率谱` : `${method} 分析结果 - 完整频率谱`,
			left: 'center',
			textStyle: {
				fontSize: 16,
				fontWeight: 'bold'
			}
		},
		tooltip: {
			trigger: 'axis',
			formatter: function(params: any) {
				if (method === 'WT') {
					// 小波变换的tooltip只显示可见系列的数据
					const visibleParams = params.filter((param: any) => {
						// 检查该系列是否可见
						return waveletScaleInfo.value.some(signalInfo => 
							signalInfo.scales.some(scale => 
								scale.key === param.seriesName && scale.visible
							)
						);
					});
					
					if (visibleParams.length === 0) {
						return '无可见数据';
					}
					
					let result = `时间: ${visibleParams[0].data[0].toFixed(6)}<br/>`;
					visibleParams.forEach((param: any) => {
						result += `${param.seriesName}: ${param.data[1].toFixed(4)}<br/>`;
					});
					return result;
				} else {
					// 其他变换方法的tooltip显示频率
					let result = `频率: ${params[0].data[0].toFixed(6)} Hz<br/>`;
					params.forEach((param: any) => {
						result += `${param.seriesName}: ${param.data[1].toFixed(4)}<br/>`;
					});
					return result;
				}
			},
			// 确保tooltip只显示可见系列的数据点
			confine: true,
			enterable: false
		},
		legend: {
			data: series.map(s => s.name),
			top: 30,
			type: 'scroll',
			show: method !== 'WT' // 小波变换时隐藏legend，通过右侧卡片控制
		},
		dataZoom: [
			{
				type: 'inside',
				start: 0,
				end: 100
			}
		],
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			top: '15%',
			containLabel: true
		},
		xAxis: {
			type: 'time',
			axisLabel: {
				showMaxLabel: true
			}
		},
		yAxis: {
			type: 'value',
			splitLine: {
				lineStyle: {
					type: 'dashed'
				}
			}
		},
		series: series,
		markLine: method === 'WT' ? undefined : {
			silent: true,
			symbol: 'none',
			lineStyle: {
				color: '#ff6b6b',
				type: 'dashed'
			},
			data: [
				{
					yAxis: 0
				}
			]
		}
	};
	
	// 设置配置并渲染
	calculatedEchart.setOption(option);
	
	// 为小波变换添加legend点击事件处理
	if (method === 'WT') {
		calculatedEchart.off('legendselectchanged');
		calculatedEchart.on('legendselectchanged', (params: any) => {
			// 同步更新尺度信息卡片的可见性
			waveletScaleInfo.value.forEach(signalInfo => {
				signalInfo.scales.forEach(scale => {
					if (scale.key === params.name) {
						scale.visible = params.selected[params.name];
					}
				});
			});
		});
	}
	
	// 响应式调整
	window.addEventListener('resize', () => {
		calculatedEchart.resize();
	});
	
	// 根据方法类型显示不同的统计信息
	if (method === 'WT') {
		// 小波变换：处理尺度信息
		processWaveletScaleInfo(data);
		// 清空频率统计信息
		frequencyStatistics.value = [];
	} else {
		// 其他变换方法：显示频率统计信息
		displayFrequencyStatistics(data);
		// 清空尺度信息
		waveletScaleInfo.value = [];
	}
	
	ElMessage.success(`${method} 分析完成 - 包含完整频率谱`);
	
	// 保存原始系列数据，用于恢复可见性
	if (method === 'WT') {
		originalSeriesData.value = series.map(s => ({ ...s }));
	}
}

// 定义统计信息的接口
interface FrequencyStats {
	name: string;
	totalPoints: number;
	negativePoints: number;
	zeroPoints: number;
	positivePoints: number;
	negativeRange?: string;
	positiveRange?: string;
	maxMagnitude: number;
}

// 定义小波变换尺度信息的接口
interface WaveletScale {
	key: string;
	name: string;
	scale: number;
	frequency: number;
	visible: boolean;
}

interface WaveletSignalInfo {
	signalName: string;
	scales: WaveletScale[];
	visible: boolean; // 添加信号级别的可见性控制
}

// 小波变换尺度可见性控制
const toggleScaleVisibility = (scaleKey: string) => {
	// 更新尺度可见性
	waveletScaleInfo.value.forEach(signalInfo => {
		signalInfo.scales.forEach(scale => {
			if (scale.key === scaleKey) {
				scale.visible = !scale.visible;
			}
		});
	});
	
	// 更新ECharts图表
	updateWaveletChartVisibility();
}

// 小波变换信号可见性控制
const toggleSignalVisibility = (signalName: string) => {
	// 找到对应的信号信息
	const signalInfo = waveletScaleInfo.value.find(info => info.signalName === signalName);
	if (signalInfo) {
		// 切换信号级别的可见性
		signalInfo.visible = !signalInfo.visible;
		
		// 同步更新该信号下所有尺度的可见性
		signalInfo.scales.forEach(scale => {
			scale.visible = signalInfo.visible;
		});
		
		// 更新ECharts图表
		updateWaveletChartVisibility();
	}
}

// 更新小波变换图表的可见性
const updateWaveletChartVisibility = () => {
	if (!calculatedEchart || calculatedMethod.value !== 'WT') return;
	
	const option = calculatedEchart.getOption();
	
	// 更新系列的可见性和数据
	option.series.forEach((series: any, index: number) => {
		const scaleKey = series.name; // 使用系列名称作为尺度键
		const isVisible = waveletScaleInfo.value.some(signalInfo => 
			signalInfo.scales.some(scale => 
				scale.key === scaleKey && scale.visible
			)
		);
		
		if (!isVisible) {
			// 完全隐藏不可见的系列
			option.series[index].silent = true;
			option.series[index].lineStyle = {
				...option.series[index].lineStyle,
				opacity: 0
			};
			// 隐藏数据点
			option.series[index].showSymbol = false;
			// 设置系列为不可交互
			option.series[index].emphasis = {
				disabled: true
			};
			// 清空数据，确保不会触发tooltip
			option.series[index].data = [];
		} else {
			// 显示可见的系列
			option.series[index].silent = false;
			option.series[index].lineStyle = {
				...option.series[index].lineStyle,
				opacity: 1
			};
			option.series[index].showSymbol = false;
			option.series[index].emphasis = {
				disabled: false
			};
			// 恢复原始数据
			if (originalSeriesData.value[index]) {
				option.series[index].data = originalSeriesData.value[index].data;
			}
		}
		
		// 更新legend的显示状态
		if (option.legend && option.legend.data) {
			const legendIndex = option.legend.data.findIndex((name: string) => name === scaleKey);
			if (legendIndex !== -1) {
				// 如果系列不可见，在legend中标记为隐藏
				if (!isVisible) {
					option.legend.selected = option.legend.selected || {};
					option.legend.selected[scaleKey] = false;
				} else {
					option.legend.selected = option.legend.selected || {};
					option.legend.selected[scaleKey] = true;
				}
			}
		}
	});
	
	// 更新tooltip配置，确保只显示可见系列
	option.tooltip = {
		...option.tooltip,
		formatter: function(params: any) {
			// 小波变换的tooltip只显示可见系列的数据
			const visibleParams = params.filter((param: any) => {
				// 检查该系列是否可见
				return waveletScaleInfo.value.some(signalInfo => 
					signalInfo.scales.some(scale => 
						scale.key === param.seriesName && scale.visible
					)
				);
			});
			
			if (visibleParams.length === 0) {
				return '无可见数据';
			}
			
			let result = `时间: ${visibleParams[0].data[0].toFixed(6)}<br/>`;
			visibleParams.forEach((param: any) => {
				result += `${param.seriesName}: ${param.data[1].toFixed(4)}<br/>`;
			});
			return result;
		}
	};
	
	calculatedEchart.setOption(option);
}

// 显示频率统计信息的函数 - 重构版本
const displayFrequencyStatistics = (data: any[]) => {
	const stats: FrequencyStats[] = [];
	
	data.forEach((signal) => {
		const signalData = signal.data || [];
		if (signalData.length === 0) return;
		
		const freqs = signalData.map((point: any) => parseFloat(point.time));
		const negFreqs = freqs.filter((f: number) => f < 0);
		const zeroFreqs = freqs.filter((f: number) => f === 0);
		const posFreqs = freqs.filter((f: number) => f > 0);
		
		const stat: FrequencyStats = {
			name: signal.name,
			totalPoints: freqs.length,
			negativePoints: negFreqs.length,
			zeroPoints: zeroFreqs.length,
			positivePoints: posFreqs.length,
			maxMagnitude: Math.max(...signalData.map((point: any) => parseFloat(point.value)))
		};
		
		if (negFreqs.length > 0) {
			stat.negativeRange = `${Math.min(...negFreqs).toFixed(4)} - ${Math.max(...negFreqs).toFixed(4)} Hz`;
		}
		if (posFreqs.length > 0) {
			stat.positiveRange = `${Math.min(...posFreqs).toFixed(4)} - ${Math.max(...posFreqs).toFixed(4)} Hz`;
		}
		
		stats.push(stat);
	});
	
	frequencyStatistics.value = stats;
}

// 处理小波变换尺度信息
const processWaveletScaleInfo = (data: any[]) => {
	const scaleInfo: WaveletSignalInfo[] = [];
	
	data.forEach((signal) => {
		if (signal.scale_groups) {
			const scales: WaveletScale[] = [];
			
			Object.keys(signal.scale_groups).forEach(scaleKey => {
				const scaleGroup = signal.scale_groups[scaleKey];
				scales.push({
					key: scaleGroup.name, // 使用名称作为键
					name: scaleGroup.name,
					scale: scaleGroup.scale,
					frequency: scaleGroup.frequency,
					visible: true // 默认可见
				});
			});
			
			scaleInfo.push({
				signalName: signal.name,
				scales: scales,
				visible: true // 添加信号级别的可见性控制
			});
		}
	});
	
	waveletScaleInfo.value = scaleInfo;
}

// 批量选择所有尺度
const selectAllScales = () => {
	waveletScaleInfo.value.forEach(signalInfo => {
		signalInfo.visible = true; // 设置信号级别可见
		signalInfo.scales.forEach(scale => {
			scale.visible = true;
		});
	});
	updateWaveletChartVisibility();
}

// 批量取消选择所有尺度
const deselectAllScales = () => {
	waveletScaleInfo.value.forEach(signalInfo => {
		signalInfo.visible = false; // 设置信号级别不可见
		signalInfo.scales.forEach(scale => {
			scale.visible = false;
		});
	});
	updateWaveletChartVisibility();
}

// 批量选择所有信号
const selectAllSignals = () => {
	waveletScaleInfo.value.forEach(signalInfo => {
		signalInfo.visible = true;
		signalInfo.scales.forEach(scale => {
			scale.visible = true;
		});
	});
	updateWaveletChartVisibility();
}

// 批量取消选择所有信号
const deselectAllSignals = () => {
	waveletScaleInfo.value.forEach(signalInfo => {
		signalInfo.visible = false;
		signalInfo.scales.forEach(scale => {
			scale.visible = false;
		});
	});
	updateWaveletChartVisibility();
}

// 测试小波变换功能（开发用）
const testWaveletFunction = () => {
	const testData = [
		{
			name: "Test Signal - WT",
			scale_groups: {
				"scale_1.00": {
					name: "Test Signal - Scale 1.00",
					scale: 1.0,
					frequency: 0.5,
					data: [
						{ time: "0.000000", value: 0.1, scale: 1.0, frequency: 0.5 },
						{ time: "1.000000", value: 0.2, scale: 1.0, frequency: 0.5 },
						{ time: "2.000000", value: 0.3, scale: 1.0, frequency: 0.5 },
						{ time: "3.000000", value: 0.4, scale: 1.0, frequency: 0.5 },
						{ time: "4.000000", value: 0.5, scale: 1.0, frequency: 0.5 }
					]
				},
				"scale_2.00": {
					name: "Test Signal - Scale 2.00",
					scale: 2.0,
					frequency: 0.25,
					data: [
						{ time: "0.000000", value: 0.4, scale: 2.0, frequency: 0.25 },
						{ time: "1.000000", value: 0.5, scale: 2.0, frequency: 0.25 },
						{ time: "2.000000", value: 0.6, scale: 2.0, frequency: 0.25 },
						{ time: "3.000000", value: 0.7, scale: 2.0, frequency: 0.25 },
						{ time: "4.000000", value: 0.8, scale: 2.0, frequency: 0.25 }
					]
				},
				"scale_3.00": {
					name: "Test Signal - Scale 3.00",
					scale: 3.0,
					frequency: 0.167,
					data: [
						{ time: "0.000000", value: 0.7, scale: 3.0, frequency: 0.167 },
						{ time: "1.000000", value: 0.8, scale: 3.0, frequency: 0.167 },
						{ time: "2.000000", value: 0.9, scale: 3.0, frequency: 0.167 },
						{ time: "3.000000", value: 1.0, scale: 3.0, frequency: 0.167 },
						{ time: "4.000000", value: 1.1, scale: 3.0, frequency: 0.167 }
					]
				}
			},
			data: []
		},
		{
			name: "Another Signal - WT",
			scale_groups: {
				"scale_1.00": {
					name: "Another Signal - Scale 1.00",
					scale: 1.0,
					frequency: 0.5,
					data: [
						{ time: "0.000000", value: 0.2, scale: 1.0, frequency: 0.5 },
						{ time: "1.000000", value: 0.3, scale: 1.0, frequency: 0.5 },
						{ time: "2.000000", value: 0.4, scale: 1.0, frequency: 0.5 },
						{ time: "3.000000", value: 0.5, scale: 1.0, frequency: 0.5 },
						{ time: "4.000000", value: 0.6, scale: 1.0, frequency: 0.5 }
					]
				},
				"scale_2.00": {
					name: "Another Signal - Scale 2.00",
					scale: 2.0,
					frequency: 0.25,
					data: [
						{ time: "0.000000", value: 0.5, scale: 2.0, frequency: 0.25 },
						{ time: "1.000000", value: 0.6, scale: 2.0, frequency: 0.25 },
						{ time: "2.000000", value: 0.7, scale: 2.0, frequency: 0.25 },
						{ time: "3.000000", value: 0.8, scale: 2.0, frequency: 0.25 },
						{ time: "4.000000", value: 0.9, scale: 2.0, frequency: 0.25 }
					]
				}
			},
			data: []
		}
	];
	
	calculatedMethod.value = 'WT';
	displayFourierResults(testData, 'WT');
}

const props = withDefaults(defineProps<Props>(), {
  fileData: null,
  isFromClick: false
});

const store = dataPipeline();
const baseBackendUrl = getBaseURL()

// 数据状态
const projName = ref('EP2002')
const nodeTypeVal = ref('')
const nodeNameVal = ref('')
const signalVal = ref('')

// 默认开始时间/结束时间 - 从pinia里获取
const binFile_startTime = store.$state.influxdbInfo.binFile_startTime
const binFile_endTime = store.$state.influxdbInfo.binFile_endTime

// 初始化时间范围 - 优先使用 fileData 中的时间，否则使用 pinia 中的默认时间
const initTimeRange = () => {
	if (props.fileData && props.isFromClick && props.fileData.start_time && props.fileData.end_time) {
		return [props.fileData.start_time, props.fileData.end_time];
	}
	return [binFile_startTime, binFile_endTime];
};

const timeRange = ref(initTimeRange())

const nodeTypes = ref([])
const nodeNames = ref([])
const signals = ref([])
let myChart: any = null

// 弹窗相关的状态
const dialogVisible = ref(false)
const nodeData = ref<Record<string, Record<string, string[]>>>({})
const selectedType = ref('')
const selectedNode = ref('')
const finalSelectedSignals = ref<SignalItem[]>([])

// 批量选择相关的状态
const selectedSignals = ref<string[]>([]) // 区域3中选中的多个信号
const selectedFinalSignals = ref<SignalItem[]>([]) // 区域4中选中的多个信号

// 修改时间格式处理
const formatDate2 = (dateStr: string) => {
	// 将字符串转换为 Date 对象
	// const date = new Date(dateStr);
	// return date.toISOString().split('.')[0];
	return dayjs(dateStr).format('YYYY-MM-DD HH:mm:ss');
};

// 定义信号项的类型
interface SignalItem {
	signal: string;
	nodeName: string;
	nodeType: string;
}

// 获取项目信息
const getInfluxdbInfo = async () => {
	if (!timeRange.value || !timeRange.value[0] || !timeRange.value[1]) {
		ElMessage.warning('请选择时间范围');
		return;
	}

	try {
		const formatDate = (dateStr: string) => {
			// 将字符串转换为 Date 对象
			const date = new Date(dateStr);
			return date.toISOString().split('.')[0] + 'Z';
		};
		const start_time = formatDate(timeRange.value[0]);
		const end_time = formatDate(timeRange.value[1]);

		// const response = await fetch(
		// 	`${baseBackendUrl}api/data_pipeline/file/influxdb/?type=info&project_name=${project_name.value}&project_type=${project_type.value}&start_time=${start_time}&end_time=${end_time}`,
		// 	{
		// 		credentials: 'include',
		// 		headers: {
		// 			"Authorization": 'JWT ' + Session.get('token')
		// 		}
		// 	}
		// );
		
		// const result = await response.json();
		// console.log('节点信息 result = ', result)

		// 各区域的节点/信号选择
		const result = {
			"code": 2000,
			"msg": "查询成功",
			"data": {
				"BCU": {
					"BCU 1": [
						"Auto-configuration Active",
						"Current Loop Output",
						"MVB Interface Working",
						"Prime Active"
					],
					"BCU 2": [
						"Auto-configuration Active",
						"Current Loop Output",
						"MVB Interface Working",
						"Prime Active"
					]
				},
				"CAR-logical": {
					"L1": [
						"Physical Car Code"
					],
					"L2": [
						"Physical Car Code"
					],
					"L3": [
						"Physical Car Code"
					],
					"L4": [
						"Physical Car Code"
					],
					"L5": [
						"Physical Car Code"
					]
				},
				"GSM": {
					"GSM 1P": [
						"GS Test Status",
						"Ground Speed",
						"Odometer 100m",
						"Odometer 1km",
						"ROCOGS",
						"Test Axle 1 status",
						"Test Axle 2 status"
					],
					"GSM 1S": [
						"GS Test Status",
						"Ground Speed",
						"Odometer 100m",
						"Odometer 1km",
						"ROCOGS",
						"Test Axle 1 status",
						"Test Axle 2 status"
					],
					"GSM 2P": [
						"GS Test Status",
						"Ground Speed",
						"Odometer 100m",
						"Odometer 1km",
						"ROCOGS",
						"Test Axle 1 status",
						"Test Axle 2 status"
					],
					"GSM 2S": [
						"GS Test Status",
						"Ground Speed",
						"Odometer 100m",
						"Odometer 1km",
						"ROCOGS",
						"Test Axle 1 status",
						"Test Axle 2 status"
					]
				},
				"LOGGER": {
					"LOGGER": [
						"Data Logger Calculated Ground Speed"
					]
				},
				"RBX-logical": {
					"RBX L1A": [
						"ASP 1",
						"ASP 1 Transducer Status",
						"ASP 2",
						"ASP 2 Transducer Status",
						"ASP Errors",
						"ASP Fit",
						"AUX 1",
						"AUX 2",
						"AUX Fit",
						"Auxiliary Pressure 1",
						"Auxiliary Pressure 2",
						"Axle 1 Speed",
						"Axle 1 Tacho (LSB)",
						"Axle 1 Tacho (MSB)",
						"Axle 2 Speed",
						"Axle 2 Tacho (LSB)",
						"Axle 2 Tacho (MSB)",
						"Axle Speed Out of Range (Axle 1)",
						"Axle Speed Out of Range (Axle 2)",
						"BCP 1 Transducer Status",
						"BCP 2 Transducer Status",
						"BCP Axle 1",
						"BCP Axle 2",
						"BSRP",
						"BSRP Fitted",
						"BSRP Transducer Status",
						"Bogie (Axle 1) Target BCP",
						"Bogie (Axle 2) Target BCP",
						"Bogie Air Consumption",
						"Bogie Control",
						"Brake Mode",
						"Brakes Active",
						"Emergency Brake Demand",
						"Emergency Jerk Timer",
						"Emergency Level Confirmation",
						"Emergency Level Select",
						"Emergency Selftest Status",
						"Fail to Release/Apply Mode",
						"GS Master Fail Only",
						"GS Secondary Master",
						"GS Test Master",
						"Heater Status",
						"Heater Supply",
						"Low BSR",
						"Max Deceleration",
						"RBX Health Status",
						"RBX Selftest Status",
						"RBX Valve States",
						"Ramp Rate (Bogie/Axle 1)",
						"Ramp Rate (Bogie/Axle 2)",
						"Remote Release Demand",
						"Residual Pressure Switches Axle 1",
						"Residual Pressure Switches Axle 2",
						"Self-test Active",
						"Service Brake Selftest Status",
						"Slip Axle 1",
						"Slip Axle 2",
						"Super Inlet",
						"Tacho Health",
						"Tacho Status",
						"Time To Test",
						"VLCP",
						"VLCP Fitted",
						"VLCP Transducer Status",
						"Valve Temperature",
						"Valve Timeout Status",
						"Valve temperature status",
						"Virtual WSP Segment",
						"WSP Inhibit",
						"WSP Selftest Status",
						"Wheel-slide Enabled",
						"Wheel-slip Indicator",
						"airMRI BCP",
						"airMRI DRAGGING BR",
						"airMRI LEAKING",
						"airMRI LINK VALVE",
						"airMRI REMOTE RELE ASE",
						"airMRI SVB HOLD1",
						"airMRI SVB HOLD2",
						"airMRI SVB VENT1",
						"airMRI SVB VENT2",
						"airMRI VLCP",
						"airMRI WSP HOLD1",
						"airMRI WSP HOLD2",
						"airMRI WSP VENT1",
						"airMRI WSP VENT2",
						"elcMRI BRAKES APPLIED",
						"elcMRI HEATER",
						"elcMRI LOW BSR",
						"elcMRI MISC",
						"elcMRI PSU",
						"elcMRI SW TIMERS",
						"elcMRI TIMER 2 LATCH",
						"elcMRI VOLTAGE",
						"elcMRI WSP ENABLE",
						"evtMRI MISC",
						"evtMRI SW TIMEOUT",
						"evtMRI WSP TIMEOUT",
						"senMRI BCP SENSORS",
						"senMRI PRESSURE SENSORS",
						"senMRI TACHO 1",
						"senMRI TACHO 2",
						"senMRI VOLTAGE SENSORS",
						"sysMRI C167 DEVICE",
						"sysMRI EPROM",
						"sysMRI EXT DEVICE",
						"sysMRI PROCESS",
						"sysMRI RAM"
					],
					"RBX L1B": [
						"ASP 1",
						"ASP 1 Transducer Status",
						"ASP 2",
						"ASP 2 Transducer Status",
						"ASP Errors",
						"ASP Fit",
						"AUX 1",
						"AUX 2",
						"AUX Fit",
						"Auxiliary Pressure 1",
						"Auxiliary Pressure 2",
						"Axle 1 Speed",
						"Axle 1 Tacho (LSB)",
						"Axle 1 Tacho (MSB)",
						"Axle 2 Speed",
						"Axle 2 Tacho (LSB)",
						"Axle 2 Tacho (MSB)",
						"Axle Speed Out of Range (Axle 1)",
						"Axle Speed Out of Range (Axle 2)",
						"BCP 1 Transducer Status",
						"BCP 2 Transducer Status",
						"BCP Axle 1",
						"BCP Axle 2",
						"BSRP",
						"BSRP Fitted",
						"BSRP Transducer Status",
						"Bogie (Axle 1) Target BCP",
						"Bogie (Axle 2) Target BCP",
						"Bogie Air Consumption",
						"Bogie Control",
						"Brake Mode",
						"Brakes Active",
						"Emergency Brake Demand",
						"Emergency Jerk Timer",
						"Emergency Level Confirmation",
						"Emergency Level Select",
						"Emergency Selftest Status",
						"Fail to Release/Apply Mode",
						"GS Master Fail Only",
						"GS Secondary Master",
						"GS Test Master",
						"Heater Status",
						"Heater Supply",
						"Low BSR",
						"Max Deceleration",
						"RBX Health Status",
						"RBX Selftest Status",
						"RBX Valve States",
						"Ramp Rate (Bogie/Axle 1)",
						"Ramp Rate (Bogie/Axle 2)",
						"Remote Release Demand",
						"Residual Pressure Switches Axle 1",
						"Residual Pressure Switches Axle 2",
						"Self-test Active",
						"Service Brake Selftest Status",
						"Slip Axle 1",
						"Slip Axle 2",
						"Super Inlet",
						"Tacho Health",
						"Tacho Status",
						"Time To Test",
						"VLCP",
						"VLCP Fitted",
						"VLCP Transducer Status",
						"Valve Temperature",
						"Valve Timeout Status",
						"Valve temperature status",
						"Virtual WSP Segment",
						"WSP Inhibit",
						"WSP Selftest Status",
						"Wheel-slide Enabled",
						"Wheel-slip Indicator",
						"airMRI BCP",
						"airMRI DRAGGING BR",
						"airMRI LEAKING",
						"airMRI LINK VALVE",
						"airMRI REMOTE RELE ASE",
						"airMRI SVB HOLD1",
						"airMRI SVB HOLD2",
						"airMRI SVB VENT1",
						"airMRI SVB VENT2",
						"airMRI VLCP",
						"airMRI WSP HOLD1",
						"airMRI WSP HOLD2",
						"airMRI WSP VENT1",
						"airMRI WSP VENT2",
						"elcMRI BRAKES APPLIED",
						"elcMRI HEATER",
						"elcMRI LOW BSR",
						"elcMRI MISC",
						"elcMRI PSU",
						"elcMRI SW TIMERS",
						"elcMRI TIMER 2 LATCH",
						"elcMRI VOLTAGE",
						"elcMRI WSP ENABLE",
						"evtMRI MISC",
						"evtMRI SW TIMEOUT",
						"evtMRI WSP TIMEOUT",
						"senMRI BCP SENSORS",
						"senMRI PRESSURE SENSORS",
						"senMRI TACHO 1",
						"senMRI TACHO 2",
						"senMRI VOLTAGE SENSORS",
						"sysMRI C167 DEVICE",
						"sysMRI EPROM",
						"sysMRI EXT DEVICE",
						"sysMRI PROCESS",
						"sysMRI RAM"
					],
					"RBX L2A": [
						"ASP 1",
						"ASP 1 Transducer Status",
						"ASP 2",
						"ASP 2 Transducer Status",
						"ASP Errors",
						"ASP Fit",
						"AUX 1",
						"AUX 2",
						"AUX Fit",
						"Auxiliary Pressure 1",
						"Auxiliary Pressure 2",
						"Axle 1 Speed",
						"Axle 1 Tacho (LSB)",
						"Axle 1 Tacho (MSB)",
						"Axle 2 Speed",
						"Axle 2 Tacho (LSB)",
						"Axle 2 Tacho (MSB)",
						"Axle Speed Out of Range (Axle 1)",
						"Axle Speed Out of Range (Axle 2)",
						"BCP 1 Transducer Status",
						"BCP 2 Transducer Status",
						"BCP Axle 1",
						"BCP Axle 2",
						"BSRP",
						"BSRP Fitted",
						"BSRP Transducer Status",
						"Bogie (Axle 1) Target BCP",
						"Bogie (Axle 2) Target BCP",
						"Bogie Air Consumption",
						"Bogie Control",
						"Brake Mode",
						"Brakes Active",
						"Emergency Brake Demand",
						"Emergency Jerk Timer",
						"Emergency Level Confirmation",
						"Emergency Level Select",
						"Emergency Selftest Status",
						"Fail to Release/Apply Mode",
						"GS Master Fail Only",
						"GS Secondary Master",
						"GS Test Master",
						"Heater Status",
						"Heater Supply",
						"Low BSR",
						"Max Deceleration",
						"RBX Health Status",
						"RBX Selftest Status",
						"RBX Valve States",
						"Ramp Rate (Bogie/Axle 1)",
						"Ramp Rate (Bogie/Axle 2)",
						"Remote Release Demand",
						"Residual Pressure Switches Axle 1",
						"Residual Pressure Switches Axle 2",
						"Self-test Active",
						"Service Brake Selftest Status",
						"Slip Axle 1",
						"Slip Axle 2",
						"Super Inlet",
						"Tacho Health",
						"Tacho Status",
						"Time To Test",
						"VLCP",
						"VLCP Fitted",
						"VLCP Transducer Status",
						"Valve Temperature",
						"Valve Timeout Status",
						"Valve temperature status",
						"Virtual WSP Segment",
						"WSP Inhibit",
						"WSP Selftest Status",
						"Wheel-slide Enabled",
						"Wheel-slip Indicator",
						"airMRI BCP",
						"airMRI DRAGGING BR",
						"airMRI LEAKING",
						"airMRI LINK VALVE",
						"airMRI REMOTE RELE ASE",
						"airMRI SVB HOLD1",
						"airMRI SVB HOLD2",
						"airMRI SVB VENT1",
						"airMRI SVB VENT2",
						"airMRI VLCP",
						"airMRI WSP HOLD1",
						"airMRI WSP HOLD2",
						"airMRI WSP VENT1",
						"airMRI WSP VENT2",
						"elcMRI BRAKES APPLIED",
						"elcMRI HEATER",
						"elcMRI LOW BSR",
						"elcMRI MISC",
						"elcMRI PSU",
						"elcMRI SW TIMERS",
						"elcMRI TIMER 2 LATCH",
						"elcMRI VOLTAGE",
						"elcMRI WSP ENABLE",
						"evtMRI MISC",
						"evtMRI SW TIMEOUT",
						"evtMRI WSP TIMEOUT",
						"senMRI BCP SENSORS",
						"senMRI PRESSURE SENSORS",
						"senMRI TACHO 1",
						"senMRI TACHO 2",
						"senMRI VOLTAGE SENSORS",
						"sysMRI C167 DEVICE",
						"sysMRI EPROM",
						"sysMRI EXT DEVICE",
						"sysMRI PROCESS",
						"sysMRI RAM"
					],
					"RBX L2B": [
						"ASP 1",
						"ASP 1 Transducer Status",
						"ASP 2",
						"ASP 2 Transducer Status",
						"ASP Errors",
						"ASP Fit",
						"AUX 1",
						"AUX 2",
						"AUX Fit",
						"Auxiliary Pressure 1",
						"Auxiliary Pressure 2",
						"Axle 1 Speed",
						"Axle 1 Tacho (LSB)",
						"Axle 1 Tacho (MSB)",
						"Axle 2 Speed",
						"Axle 2 Tacho (LSB)",
						"Axle 2 Tacho (MSB)",
						"Axle Speed Out of Range (Axle 1)",
						"Axle Speed Out of Range (Axle 2)",
						"BCP 1 Transducer Status",
						"BCP 2 Transducer Status",
						"BCP Axle 1",
						"BCP Axle 2",
						"BSRP",
						"BSRP Fitted",
						"BSRP Transducer Status",
						"Bogie (Axle 1) Target BCP",
						"Bogie (Axle 2) Target BCP",
						"Bogie Air Consumption",
						"Bogie Control",
						"Brake Mode",
						"Brakes Active",
						"Emergency Brake Demand",
						"Emergency Jerk Timer",
						"Emergency Level Confirmation",
						"Emergency Level Select",
						"Emergency Selftest Status",
						"Fail to Release/Apply Mode",
						"GS Master Fail Only",
						"GS Secondary Master",
						"GS Test Master",
						"Heater Status",
						"Heater Supply",
						"Low BSR",
						"Max Deceleration",
						"RBX Health Status",
						"RBX Selftest Status",
						"RBX Valve States",
						"Ramp Rate (Bogie/Axle 1)",
						"Ramp Rate (Bogie/Axle 2)",
						"Remote Release Demand",
						"Residual Pressure Switches Axle 1",
						"Residual Pressure Switches Axle 2",
						"Self-test Active",
						"Service Brake Selftest Status",
						"Slip Axle 1",
						"Slip Axle 2",
						"Super Inlet",
						"Tacho Health",
						"Tacho Status",
						"Time To Test",
						"VLCP",
						"VLCP Fitted",
						"VLCP Transducer Status",
						"Valve Temperature",
						"Valve Timeout Status",
						"Valve temperature status",
						"Virtual WSP Segment",
						"WSP Inhibit",
						"WSP Selftest Status",
						"Wheel-slide Enabled",
						"Wheel-slip Indicator",
						"airMRI BCP",
						"airMRI DRAGGING BR",
						"airMRI LEAKING",
						"airMRI LINK VALVE",
						"airMRI REMOTE RELE ASE",
						"airMRI SVB HOLD1",
						"airMRI SVB HOLD2",
						"airMRI SVB VENT1",
						"airMRI SVB VENT2",
						"airMRI VLCP",
						"airMRI WSP HOLD1",
						"airMRI WSP HOLD2",
						"airMRI WSP VENT1",
						"airMRI WSP VENT2",
						"elcMRI BRAKES APPLIED",
						"elcMRI HEATER",
						"elcMRI LOW BSR",
						"elcMRI MISC",
						"elcMRI PSU",
						"elcMRI SW TIMERS",
						"elcMRI TIMER 2 LATCH",
						"elcMRI VOLTAGE",
						"elcMRI WSP ENABLE",
						"evtMRI MISC",
						"evtMRI SW TIMEOUT",
						"evtMRI WSP TIMEOUT",
						"senMRI BCP SENSORS",
						"senMRI PRESSURE SENSORS",
						"senMRI TACHO 1",
						"senMRI TACHO 2",
						"senMRI VOLTAGE SENSORS",
						"sysMRI C167 DEVICE",
						"sysMRI EPROM",
						"sysMRI EXT DEVICE",
						"sysMRI PROCESS",
						"sysMRI RAM"
					],
					"RBX L3A": [
						"ASP 1",
						"ASP 1 Transducer Status",
						"ASP 2",
						"ASP 2 Transducer Status",
						"ASP Errors",
						"ASP Fit",
						"AUX 1",
						"AUX 2",
						"AUX Fit",
						"Auxiliary Pressure 1",
						"Auxiliary Pressure 2",
						"Axle 1 Speed",
						"Axle 1 Tacho (LSB)",
						"Axle 1 Tacho (MSB)",
						"Axle 2 Speed",
						"Axle 2 Tacho (LSB)",
						"Axle 2 Tacho (MSB)",
						"Axle Speed Out of Range (Axle 1)",
						"Axle Speed Out of Range (Axle 2)",
						"BCP 1 Transducer Status",
						"BCP 2 Transducer Status",
						"BCP Axle 1",
						"BCP Axle 2",
						"BSRP",
						"BSRP Fitted",
						"BSRP Transducer Status",
						"Bogie (Axle 1) Target BCP",
						"Bogie (Axle 2) Target BCP",
						"Bogie Air Consumption",
						"Bogie Control",
						"Brake Mode",
						"Brakes Active",
						"Emergency Brake Demand",
						"Emergency Jerk Timer",
						"Emergency Level Confirmation",
						"Emergency Level Select",
						"Emergency Selftest Status",
						"Fail to Release/Apply Mode",
						"GS Master Fail Only",
						"GS Secondary Master",
						"GS Test Master",
						"Heater Status",
						"Heater Supply",
						"Low BSR",
						"Max Deceleration",
						"RBX Health Status",
						"RBX Selftest Status",
						"RBX Valve States",
						"Ramp Rate (Bogie/Axle 1)",
						"Ramp Rate (Bogie/Axle 2)",
						"Remote Release Demand",
						"Residual Pressure Switches Axle 1",
						"Residual Pressure Switches Axle 2",
						"Self-test Active",
						"Service Brake Selftest Status",
						"Slip Axle 1",
						"Slip Axle 2",
						"Super Inlet",
						"Tacho Health",
						"Tacho Status",
						"Time To Test",
						"VLCP",
						"VLCP Fitted",
						"VLCP Transducer Status",
						"Valve Temperature",
						"Valve Timeout Status",
						"Valve temperature status",
						"Virtual WSP Segment",
						"WSP Inhibit",
						"WSP Selftest Status",
						"Wheel-slide Enabled",
						"airMRI BCP",
						"airMRI DRAGGING BR",
						"airMRI LEAKING",
						"airMRI LINK VALVE",
						"airMRI REMOTE RELE ASE",
						"airMRI SVB HOLD1",
						"airMRI SVB HOLD2",
						"airMRI SVB VENT1",
						"airMRI SVB VENT2",
						"airMRI VLCP",
						"airMRI WSP HOLD1",
						"airMRI WSP HOLD2",
						"airMRI WSP VENT1",
						"airMRI WSP VENT2",
						"elcMRI BRAKES APPLIED",
						"elcMRI HEATER",
						"elcMRI LOW BSR",
						"elcMRI MISC",
						"elcMRI PSU",
						"elcMRI SW TIMERS",
						"elcMRI TIMER 2 LATCH",
						"elcMRI VOLTAGE",
						"elcMRI WSP ENABLE",
						"evtMRI MISC",
						"evtMRI SW TIMEOUT",
						"evtMRI WSP TIMEOUT",
						"senMRI BCP SENSORS",
						"senMRI PRESSURE SENSORS",
						"senMRI TACHO 1",
						"senMRI TACHO 2",
						"senMRI VOLTAGE SENSORS",
						"sysMRI C167 DEVICE",
						"sysMRI EPROM",
						"sysMRI EXT DEVICE",
						"sysMRI PROCESS",
						"sysMRI RAM"
					],
					"RBX L3B": [
						"ASP 1",
						"ASP 1 Transducer Status",
						"ASP 2",
						"ASP 2 Transducer Status",
						"ASP Errors",
						"ASP Fit",
						"AUX 1",
						"AUX 2",
						"AUX Fit",
						"Auxiliary Pressure 1",
						"Auxiliary Pressure 2",
						"Axle 1 Speed",
						"Axle 1 Tacho (LSB)",
						"Axle 1 Tacho (MSB)",
						"Axle 2 Speed",
						"Axle 2 Tacho (LSB)",
						"Axle 2 Tacho (MSB)",
						"Axle Speed Out of Range (Axle 1)",
						"Axle Speed Out of Range (Axle 2)",
						"BCP 1 Transducer Status",
						"BCP 2 Transducer Status",
						"BCP Axle 1",
						"BCP Axle 2",
						"BSRP",
						"BSRP Fitted",
						"BSRP Transducer Status",
						"Bogie (Axle 1) Target BCP",
						"Bogie (Axle 2) Target BCP",
						"Bogie Air Consumption",
						"Bogie Control",
						"Brake Mode",
						"Brakes Active",
						"Emergency Brake Demand",
						"Emergency Jerk Timer",
						"Emergency Level Confirmation",
						"Emergency Level Select",
						"Emergency Selftest Status",
						"Fail to Release/Apply Mode",
						"GS Master Fail Only",
						"GS Secondary Master",
						"GS Test Master",
						"Heater Status",
						"Heater Supply",
						"Low BSR",
						"Max Deceleration",
						"RBX Health Status",
						"RBX Selftest Status",
						"RBX Valve States",
						"Ramp Rate (Bogie/Axle 1)",
						"Ramp Rate (Bogie/Axle 2)",
						"Remote Release Demand",
						"Residual Pressure Switches Axle 1",
						"Residual Pressure Switches Axle 2",
						"Self-test Active",
						"Service Brake Selftest Status",
						"Slip Axle 1",
						"Slip Axle 2",
						"Super Inlet",
						"Tacho Health",
						"Tacho Status",
						"Time To Test",
						"VLCP",
						"VLCP Fitted",
						"VLCP Transducer Status",
						"Valve Temperature",
						"Valve Timeout Status",
						"Valve temperature status",
						"Virtual WSP Segment",
						"WSP Inhibit",
						"WSP Selftest Status",
						"Wheel-slide Enabled",
						"airMRI BCP",
						"airMRI DRAGGING BR",
						"airMRI LEAKING",
						"airMRI LINK VALVE",
						"airMRI REMOTE RELE ASE",
						"airMRI SVB HOLD1",
						"airMRI SVB HOLD2",
						"airMRI SVB VENT1",
						"airMRI SVB VENT2",
						"airMRI VLCP",
						"airMRI WSP HOLD1",
						"airMRI WSP HOLD2",
						"airMRI WSP VENT1",
						"airMRI WSP VENT2",
						"elcMRI BRAKES APPLIED",
						"elcMRI HEATER",
						"elcMRI LOW BSR",
						"elcMRI MISC",
						"elcMRI PSU",
						"elcMRI SW TIMERS",
						"elcMRI TIMER 2 LATCH",
						"elcMRI VOLTAGE",
						"elcMRI WSP ENABLE",
						"evtMRI MISC",
						"evtMRI SW TIMEOUT",
						"evtMRI WSP TIMEOUT",
						"senMRI BCP SENSORS",
						"senMRI PRESSURE SENSORS",
						"senMRI TACHO 1",
						"senMRI TACHO 2",
						"senMRI VOLTAGE SENSORS",
						"sysMRI C167 DEVICE",
						"sysMRI EPROM",
						"sysMRI EXT DEVICE",
						"sysMRI PROCESS",
						"sysMRI RAM"
					],
					"RBX L4A": [
						"ASP 1",
						"ASP 1 Transducer Status",
						"ASP 2",
						"ASP 2 Transducer Status",
						"ASP Errors",
						"ASP Fit",
						"AUX 1",
						"AUX 2",
						"AUX Fit",
						"Auxiliary Pressure 1",
						"Auxiliary Pressure 2",
						"Axle 1 Speed",
						"Axle 1 Tacho (LSB)",
						"Axle 1 Tacho (MSB)",
						"Axle 2 Speed",
						"Axle 2 Tacho (LSB)",
						"Axle 2 Tacho (MSB)",
						"Axle Speed Out of Range (Axle 1)",
						"Axle Speed Out of Range (Axle 2)",
						"BCP 1 Transducer Status",
						"BCP 2 Transducer Status",
						"BCP Axle 1",
						"BCP Axle 2",
						"BSRP",
						"BSRP Fitted",
						"BSRP Transducer Status",
						"Bogie (Axle 1) Target BCP",
						"Bogie (Axle 2) Target BCP",
						"Bogie Air Consumption",
						"Bogie Control",
						"Brake Mode",
						"Brakes Active",
						"Emergency Brake Demand",
						"Emergency Jerk Timer",
						"Emergency Level Confirmation",
						"Emergency Level Select",
						"Emergency Selftest Status",
						"Fail to Release/Apply Mode",
						"GS Master Fail Only",
						"GS Secondary Master",
						"GS Test Master",
						"Heater Status",
						"Heater Supply",
						"Low BSR",
						"Max Deceleration",
						"RBX Health Status",
						"RBX Selftest Status",
						"RBX Valve States",
						"Ramp Rate (Bogie/Axle 1)",
						"Ramp Rate (Bogie/Axle 2)",
						"Remote Release Demand",
						"Residual Pressure Switches Axle 1",
						"Residual Pressure Switches Axle 2",
						"Self-test Active",
						"Service Brake Selftest Status",
						"Slip Axle 1",
						"Slip Axle 2",
						"Super Inlet",
						"Tacho Health",
						"Tacho Status",
						"Time To Test",
						"VLCP",
						"VLCP Fitted",
						"VLCP Transducer Status",
						"Valve Temperature",
						"Valve Timeout Status",
						"Valve temperature status",
						"Virtual WSP Segment",
						"WSP Inhibit",
						"WSP Selftest Status",
						"Wheel-slide Enabled",
						"airMRI BCP",
						"airMRI DRAGGING BR",
						"airMRI LEAKING",
						"airMRI LINK VALVE",
						"airMRI REMOTE RELE ASE",
						"airMRI SVB HOLD1",
						"airMRI SVB HOLD2",
						"airMRI SVB VENT1",
						"airMRI SVB VENT2",
						"airMRI VLCP",
						"airMRI WSP HOLD1",
						"airMRI WSP HOLD2",
						"airMRI WSP VENT1",
						"airMRI WSP VENT2",
						"elcMRI BRAKES APPLIED",
						"elcMRI HEATER",
						"elcMRI LOW BSR",
						"elcMRI MISC",
						"elcMRI PSU",
						"elcMRI SW TIMERS",
						"elcMRI TIMER 2 LATCH",
						"elcMRI VOLTAGE",
						"elcMRI WSP ENABLE",
						"evtMRI MISC",
						"evtMRI SW TIMEOUT",
						"evtMRI WSP TIMEOUT",
						"senMRI BCP SENSORS",
						"senMRI PRESSURE SENSORS",
						"senMRI TACHO 1",
						"senMRI TACHO 2",
						"senMRI VOLTAGE SENSORS",
						"sysMRI C167 DEVICE",
						"sysMRI EPROM",
						"sysMRI EXT DEVICE",
						"sysMRI PROCESS",
						"sysMRI RAM"
					],
					"RBX L4B": [
						"ASP 1",
						"ASP 1 Transducer Status",
						"ASP 2",
						"ASP 2 Transducer Status",
						"ASP Errors",
						"ASP Fit",
						"AUX 1",
						"AUX 2",
						"AUX Fit",
						"Auxiliary Pressure 1",
						"Auxiliary Pressure 2",
						"Axle 1 Speed",
						"Axle 1 Tacho (LSB)",
						"Axle 1 Tacho (MSB)",
						"Axle 2 Speed",
						"Axle 2 Tacho (LSB)",
						"Axle 2 Tacho (MSB)",
						"Axle Speed Out of Range (Axle 1)",
						"Axle Speed Out of Range (Axle 2)",
						"BCP 1 Transducer Status",
						"BCP 2 Transducer Status",
						"BCP Axle 1",
						"BCP Axle 2",
						"BSRP",
						"BSRP Fitted",
						"BSRP Transducer Status",
						"Bogie (Axle 1) Target BCP",
						"Bogie (Axle 2) Target BCP",
						"Bogie Air Consumption",
						"Bogie Control",
						"Brake Mode",
						"Brakes Active",
						"Emergency Brake Demand",
						"Emergency Jerk Timer",
						"Emergency Level Confirmation",
						"Emergency Level Select",
						"Emergency Selftest Status",
						"Fail to Release/Apply Mode",
						"GS Master Fail Only",
						"GS Secondary Master",
						"GS Test Master",
						"Heater Status",
						"Heater Supply",
						"Low BSR",
						"Max Deceleration",
						"RBX Health Status",
						"RBX Selftest Status",
						"RBX Valve States",
						"Ramp Rate (Bogie/Axle 1)",
						"Ramp Rate (Bogie/Axle 2)",
						"Remote Release Demand",
						"Residual Pressure Switches Axle 1",
						"Residual Pressure Switches Axle 2",
						"Self-test Active",
						"Service Brake Selftest Status",
						"Slip Axle 1",
						"Slip Axle 2",
						"Super Inlet",
						"Tacho Health",
						"Tacho Status",
						"Time To Test",
						"VLCP",
						"VLCP Fitted",
						"VLCP Transducer Status",
						"Valve Temperature",
						"Valve Timeout Status",
						"Valve temperature status",
						"Virtual WSP Segment",
						"WSP Inhibit",
						"WSP Selftest Status",
						"Wheel-slide Enabled",
						"airMRI BCP",
						"airMRI DRAGGING BR",
						"airMRI LEAKING",
						"airMRI LINK VALVE",
						"airMRI REMOTE RELE ASE",
						"airMRI SVB HOLD1",
						"airMRI SVB HOLD2",
						"airMRI SVB VENT1",
						"airMRI SVB VENT2",
						"airMRI VLCP",
						"airMRI WSP HOLD1",
						"airMRI WSP HOLD2",
						"airMRI WSP VENT1",
						"airMRI WSP VENT2",
						"elcMRI BRAKES APPLIED",
						"elcMRI HEATER",
						"elcMRI LOW BSR",
						"elcMRI MISC",
						"elcMRI PSU",
						"elcMRI SW TIMERS",
						"elcMRI TIMER 2 LATCH",
						"elcMRI VOLTAGE",
						"elcMRI WSP ENABLE",
						"evtMRI MISC",
						"evtMRI SW TIMEOUT",
						"evtMRI WSP TIMEOUT",
						"senMRI BCP SENSORS",
						"senMRI PRESSURE SENSORS",
						"senMRI TACHO 1",
						"senMRI TACHO 2",
						"senMRI VOLTAGE SENSORS",
						"sysMRI C167 DEVICE",
						"sysMRI EPROM",
						"sysMRI EXT DEVICE",
						"sysMRI PROCESS",
						"sysMRI RAM"
					],
					"RBX L5A": [
						"ASP 1",
						"ASP 1 Transducer Status",
						"ASP 2",
						"ASP 2 Transducer Status",
						"ASP Errors",
						"ASP Fit",
						"AUX 1",
						"AUX 2",
						"AUX Fit",
						"Auxiliary Pressure 1",
						"Auxiliary Pressure 2",
						"Axle 1 Speed",
						"Axle 1 Tacho (LSB)",
						"Axle 1 Tacho (MSB)",
						"Axle 2 Speed",
						"Axle 2 Tacho (LSB)",
						"Axle 2 Tacho (MSB)",
						"Axle Speed Out of Range (Axle 1)",
						"Axle Speed Out of Range (Axle 2)",
						"BCP 1 Transducer Status",
						"BCP 2 Transducer Status",
						"BCP Axle 1",
						"BCP Axle 2",
						"BSRP",
						"BSRP Fitted",
						"BSRP Transducer Status",
						"Bogie (Axle 1) Target BCP",
						"Bogie (Axle 2) Target BCP",
						"Bogie Air Consumption",
						"Bogie Control",
						"Brake Mode",
						"Brakes Active",
						"Emergency Brake Demand",
						"Emergency Jerk Timer",
						"Emergency Level Confirmation",
						"Emergency Level Select",
						"Emergency Selftest Status",
						"Fail to Release/Apply Mode",
						"GS Master Fail Only",
						"GS Secondary Master",
						"GS Test Master",
						"Heater Status",
						"Heater Supply",
						"Low BSR",
						"Max Deceleration",
						"RBX Health Status",
						"RBX Selftest Status",
						"RBX Valve States",
						"Ramp Rate (Bogie/Axle 1)",
						"Ramp Rate (Bogie/Axle 2)",
						"Remote Release Demand",
						"Residual Pressure Switches Axle 1",
						"Residual Pressure Switches Axle 2",
						"Self-test Active",
						"Service Brake Selftest Status",
						"Slip Axle 1",
						"Slip Axle 2",
						"Super Inlet",
						"Tacho Health",
						"Tacho Status",
						"Time To Test",
						"VLCP",
						"VLCP Fitted",
						"VLCP Transducer Status",
						"Valve Temperature",
						"Valve Timeout Status",
						"Valve temperature status",
						"Virtual WSP Segment",
						"WSP Inhibit",
						"WSP Selftest Status",
						"Wheel-slide Enabled",
						"airMRI BCP",
						"airMRI DRAGGING BR",
						"airMRI LEAKING",
						"airMRI LINK VALVE",
						"airMRI REMOTE RELE ASE",
						"airMRI SVB HOLD1",
						"airMRI SVB HOLD2",
						"airMRI SVB VENT1",
						"airMRI SVB VENT2",
						"airMRI VLCP",
						"airMRI WSP HOLD1",
						"airMRI WSP HOLD2",
						"airMRI WSP VENT1",
						"airMRI WSP VENT2",
						"elcMRI BRAKES APPLIED",
						"elcMRI HEATER",
						"elcMRI LOW BSR",
						"elcMRI MISC",
						"elcMRI PSU",
						"elcMRI SW TIMERS",
						"elcMRI TIMER 2 LATCH",
						"elcMRI VOLTAGE",
						"elcMRI WSP ENABLE",
						"evtMRI MISC",
						"evtMRI SW TIMEOUT",
						"evtMRI WSP TIMEOUT",
						"senMRI BCP SENSORS",
						"senMRI PRESSURE SENSORS",
						"senMRI TACHO 1",
						"senMRI TACHO 2",
						"senMRI VOLTAGE SENSORS",
						"sysMRI C167 DEVICE",
						"sysMRI EPROM",
						"sysMRI EXT DEVICE",
						"sysMRI PROCESS",
						"sysMRI RAM"
					],
					"RBX L5B": [
						"ASP 1",
						"ASP 1 Transducer Status",
						"ASP 2",
						"ASP 2 Transducer Status",
						"ASP Errors",
						"ASP Fit",
						"AUX 1",
						"AUX 2",
						"AUX Fit",
						"Auxiliary Pressure 1",
						"Auxiliary Pressure 2",
						"Axle 1 Speed",
						"Axle 1 Tacho (LSB)",
						"Axle 1 Tacho (MSB)",
						"Axle 2 Speed",
						"Axle 2 Tacho (LSB)",
						"Axle 2 Tacho (MSB)",
						"Axle Speed Out of Range (Axle 1)",
						"Axle Speed Out of Range (Axle 2)",
						"BCP 1 Transducer Status",
						"BCP 2 Transducer Status",
						"BCP Axle 1",
						"BCP Axle 2",
						"BSRP",
						"BSRP Fitted",
						"BSRP Transducer Status",
						"Bogie (Axle 1) Target BCP",
						"Bogie (Axle 2) Target BCP",
						"Bogie Air Consumption",
						"Bogie Control",
						"Brake Mode",
						"Brakes Active",
						"Emergency Brake Demand",
						"Emergency Jerk Timer",
						"Emergency Level Confirmation",
						"Emergency Level Select",
						"Emergency Selftest Status",
						"Fail to Release/Apply Mode",
						"GS Master Fail Only",
						"GS Secondary Master",
						"GS Test Master",
						"Heater Status",
						"Heater Supply",
						"Low BSR",
						"Max Deceleration",
						"RBX Health Status",
						"RBX Selftest Status",
						"RBX Valve States",
						"Ramp Rate (Bogie/Axle 1)",
						"Ramp Rate (Bogie/Axle 2)",
						"Remote Release Demand",
						"Residual Pressure Switches Axle 1",
						"Residual Pressure Switches Axle 2",
						"Self-test Active",
						"Service Brake Selftest Status",
						"Slip Axle 1",
						"Slip Axle 2",
						"Super Inlet",
						"Tacho Health",
						"Tacho Status",
						"Time To Test",
						"VLCP",
						"VLCP Fitted",
						"VLCP Transducer Status",
						"Valve Temperature",
						"Valve Timeout Status",
						"Valve temperature status",
						"Virtual WSP Segment",
						"WSP Inhibit",
						"WSP Selftest Status",
						"Wheel-slide Enabled",
						"airMRI BCP",
						"airMRI DRAGGING BR",
						"airMRI LEAKING",
						"airMRI LINK VALVE",
						"airMRI REMOTE RELE ASE",
						"airMRI SVB HOLD1",
						"airMRI SVB HOLD2",
						"airMRI SVB VENT1",
						"airMRI SVB VENT2",
						"airMRI VLCP",
						"airMRI WSP HOLD1",
						"airMRI WSP HOLD2",
						"airMRI WSP VENT1",
						"airMRI WSP VENT2",
						"elcMRI BRAKES APPLIED",
						"elcMRI HEATER",
						"elcMRI LOW BSR",
						"elcMRI MISC",
						"elcMRI PSU",
						"elcMRI SW TIMERS",
						"elcMRI TIMER 2 LATCH",
						"elcMRI VOLTAGE",
						"elcMRI WSP ENABLE",
						"evtMRI MISC",
						"evtMRI SW TIMEOUT",
						"evtMRI WSP TIMEOUT",
						"senMRI BCP SENSORS",
						"senMRI PRESSURE SENSORS",
						"senMRI TACHO 1",
						"senMRI TACHO 2",
						"senMRI VOLTAGE SENSORS",
						"sysMRI C167 DEVICE",
						"sysMRI EPROM",
						"sysMRI EXT DEVICE",
						"sysMRI PROCESS",
						"sysMRI RAM"
					]
				},
				"RIO-logical": {
					"RIO L1A": [
						"Current Loop Fault",
						"Digital Inputs",
						"Digital Outputs",
						"Health Status",
						"Maintenance Event ID1",
						"Maintenance Event ID10",
						"Maintenance Event ID11",
						"Maintenance Event ID12",
						"Maintenance Event ID13",
						"Maintenance Event ID14",
						"Maintenance Event ID15",
						"Maintenance Event ID16",
						"Maintenance Event ID17",
						"Maintenance Event ID18",
						"Maintenance Event ID19",
						"Maintenance Event ID2",
						"Maintenance Event ID20",
						"Maintenance Event ID21",
						"Maintenance Event ID22",
						"Maintenance Event ID23",
						"Maintenance Event ID24",
						"Maintenance Event ID25",
						"Maintenance Event ID26",
						"Maintenance Event ID27",
						"Maintenance Event ID28",
						"Maintenance Event ID29",
						"Maintenance Event ID3",
						"Maintenance Event ID30",
						"Maintenance Event ID31",
						"Maintenance Event ID32",
						"Maintenance Event ID33",
						"Maintenance Event ID34",
						"Maintenance Event ID35",
						"Maintenance Event ID36",
						"Maintenance Event ID37",
						"Maintenance Event ID38",
						"Maintenance Event ID39",
						"Maintenance Event ID4",
						"Maintenance Event ID40",
						"Maintenance Event ID41",
						"Maintenance Event ID42",
						"Maintenance Event ID43",
						"Maintenance Event ID44",
						"Maintenance Event ID45",
						"Maintenance Event ID46",
						"Maintenance Event ID47",
						"Maintenance Event ID48",
						"Maintenance Event ID5",
						"Maintenance Event ID6",
						"Maintenance Event ID7",
						"Maintenance Event ID8",
						"Maintenance Event ID9",
						"PWM 1 input",
						"PWM 2 input",
						"PWM 3 input",
						"PWM Output 1",
						"PWM Output 2",
						"PWM Output 3",
						"Prime",
						"System Conditions OK"
					],
					"RIO L1B": [
						"Current Loop Fault",
						"Digital Inputs",
						"Digital Outputs",
						"Health Status",
						"Maintenance Event ID1",
						"Maintenance Event ID10",
						"Maintenance Event ID11",
						"Maintenance Event ID12",
						"Maintenance Event ID13",
						"Maintenance Event ID14",
						"Maintenance Event ID15",
						"Maintenance Event ID16",
						"Maintenance Event ID17",
						"Maintenance Event ID18",
						"Maintenance Event ID19",
						"Maintenance Event ID2",
						"Maintenance Event ID20",
						"Maintenance Event ID21",
						"Maintenance Event ID22",
						"Maintenance Event ID23",
						"Maintenance Event ID24",
						"Maintenance Event ID25",
						"Maintenance Event ID26",
						"Maintenance Event ID27",
						"Maintenance Event ID28",
						"Maintenance Event ID29",
						"Maintenance Event ID3",
						"Maintenance Event ID30",
						"Maintenance Event ID31",
						"Maintenance Event ID32",
						"Maintenance Event ID33",
						"Maintenance Event ID34",
						"Maintenance Event ID35",
						"Maintenance Event ID36",
						"Maintenance Event ID37",
						"Maintenance Event ID38",
						"Maintenance Event ID39",
						"Maintenance Event ID4",
						"Maintenance Event ID40",
						"Maintenance Event ID41",
						"Maintenance Event ID42",
						"Maintenance Event ID43",
						"Maintenance Event ID44",
						"Maintenance Event ID45",
						"Maintenance Event ID46",
						"Maintenance Event ID47",
						"Maintenance Event ID48",
						"Maintenance Event ID5",
						"Maintenance Event ID6",
						"Maintenance Event ID7",
						"Maintenance Event ID8",
						"Maintenance Event ID9",
						"PWM 1 input",
						"PWM 2 input",
						"PWM 3 input",
						"PWM Output 1",
						"PWM Output 2",
						"PWM Output 3",
						"Prime",
						"System Conditions OK"
					],
					"RIO L2A": [
						"Current Loop Fault",
						"Digital Inputs",
						"Digital Outputs",
						"Health Status",
						"Maintenance Event ID1",
						"Maintenance Event ID10",
						"Maintenance Event ID11",
						"Maintenance Event ID12",
						"Maintenance Event ID13",
						"Maintenance Event ID14",
						"Maintenance Event ID15",
						"Maintenance Event ID16",
						"Maintenance Event ID17",
						"Maintenance Event ID18",
						"Maintenance Event ID19",
						"Maintenance Event ID2",
						"Maintenance Event ID20",
						"Maintenance Event ID21",
						"Maintenance Event ID22",
						"Maintenance Event ID23",
						"Maintenance Event ID24",
						"Maintenance Event ID25",
						"Maintenance Event ID26",
						"Maintenance Event ID27",
						"Maintenance Event ID28",
						"Maintenance Event ID29",
						"Maintenance Event ID3",
						"Maintenance Event ID30",
						"Maintenance Event ID31",
						"Maintenance Event ID32",
						"Maintenance Event ID33",
						"Maintenance Event ID34",
						"Maintenance Event ID35",
						"Maintenance Event ID36",
						"Maintenance Event ID37",
						"Maintenance Event ID38",
						"Maintenance Event ID39",
						"Maintenance Event ID4",
						"Maintenance Event ID40",
						"Maintenance Event ID41",
						"Maintenance Event ID42",
						"Maintenance Event ID43",
						"Maintenance Event ID44",
						"Maintenance Event ID45",
						"Maintenance Event ID46",
						"Maintenance Event ID47",
						"Maintenance Event ID48",
						"Maintenance Event ID5",
						"Maintenance Event ID6",
						"Maintenance Event ID7",
						"Maintenance Event ID8",
						"Maintenance Event ID9",
						"PWM 1 input",
						"PWM 2 input",
						"PWM 3 input",
						"PWM Output 1",
						"PWM Output 2",
						"PWM Output 3",
						"Prime",
						"System Conditions OK"
					],
					"RIO L2B": [
						"Current Loop Fault",
						"Digital Inputs",
						"Digital Outputs",
						"Health Status",
						"Maintenance Event ID1",
						"Maintenance Event ID10",
						"Maintenance Event ID11",
						"Maintenance Event ID12",
						"Maintenance Event ID13",
						"Maintenance Event ID14",
						"Maintenance Event ID15",
						"Maintenance Event ID16",
						"Maintenance Event ID17",
						"Maintenance Event ID18",
						"Maintenance Event ID19",
						"Maintenance Event ID2",
						"Maintenance Event ID20",
						"Maintenance Event ID21",
						"Maintenance Event ID22",
						"Maintenance Event ID23",
						"Maintenance Event ID24",
						"Maintenance Event ID25",
						"Maintenance Event ID26",
						"Maintenance Event ID27",
						"Maintenance Event ID28",
						"Maintenance Event ID29",
						"Maintenance Event ID3",
						"Maintenance Event ID30",
						"Maintenance Event ID31",
						"Maintenance Event ID32",
						"Maintenance Event ID33",
						"Maintenance Event ID34",
						"Maintenance Event ID35",
						"Maintenance Event ID36",
						"Maintenance Event ID37",
						"Maintenance Event ID38",
						"Maintenance Event ID39",
						"Maintenance Event ID4",
						"Maintenance Event ID40",
						"Maintenance Event ID41",
						"Maintenance Event ID42",
						"Maintenance Event ID43",
						"Maintenance Event ID44",
						"Maintenance Event ID45",
						"Maintenance Event ID46",
						"Maintenance Event ID47",
						"Maintenance Event ID48",
						"Maintenance Event ID5",
						"Maintenance Event ID6",
						"Maintenance Event ID7",
						"Maintenance Event ID8",
						"Maintenance Event ID9",
						"PWM 1 input",
						"PWM 2 input",
						"PWM 3 input",
						"PWM Output 1",
						"PWM Output 2",
						"PWM Output 3",
						"Prime",
						"System Conditions OK"
					],
					"RIO L3A": [
						"Current Loop Fault",
						"Digital Inputs",
						"Digital Outputs",
						"Health Status",
						"Maintenance Event ID1",
						"Maintenance Event ID10",
						"Maintenance Event ID11",
						"Maintenance Event ID12",
						"Maintenance Event ID13",
						"Maintenance Event ID14",
						"Maintenance Event ID15",
						"Maintenance Event ID16",
						"Maintenance Event ID17",
						"Maintenance Event ID18",
						"Maintenance Event ID19",
						"Maintenance Event ID2",
						"Maintenance Event ID20",
						"Maintenance Event ID21",
						"Maintenance Event ID22",
						"Maintenance Event ID23",
						"Maintenance Event ID24",
						"Maintenance Event ID25",
						"Maintenance Event ID26",
						"Maintenance Event ID27",
						"Maintenance Event ID28",
						"Maintenance Event ID29",
						"Maintenance Event ID3",
						"Maintenance Event ID30",
						"Maintenance Event ID31",
						"Maintenance Event ID32",
						"Maintenance Event ID33",
						"Maintenance Event ID34",
						"Maintenance Event ID35",
						"Maintenance Event ID36",
						"Maintenance Event ID37",
						"Maintenance Event ID38",
						"Maintenance Event ID39",
						"Maintenance Event ID4",
						"Maintenance Event ID40",
						"Maintenance Event ID41",
						"Maintenance Event ID42",
						"Maintenance Event ID43",
						"Maintenance Event ID44",
						"Maintenance Event ID45",
						"Maintenance Event ID46",
						"Maintenance Event ID47",
						"Maintenance Event ID48",
						"Maintenance Event ID5",
						"Maintenance Event ID6",
						"Maintenance Event ID7",
						"Maintenance Event ID8",
						"Maintenance Event ID9",
						"PWM 1 input",
						"PWM 2 input",
						"PWM 3 input",
						"PWM Output 1",
						"PWM Output 2",
						"PWM Output 3",
						"Prime",
						"System Conditions OK"
					],
					"RIO L3B": [
						"Current Loop Fault",
						"Digital Inputs",
						"Digital Outputs",
						"Health Status",
						"Maintenance Event ID1",
						"Maintenance Event ID10",
						"Maintenance Event ID11",
						"Maintenance Event ID12",
						"Maintenance Event ID13",
						"Maintenance Event ID14",
						"Maintenance Event ID15",
						"Maintenance Event ID16",
						"Maintenance Event ID17",
						"Maintenance Event ID18",
						"Maintenance Event ID19",
						"Maintenance Event ID2",
						"Maintenance Event ID20",
						"Maintenance Event ID21",
						"Maintenance Event ID22",
						"Maintenance Event ID23",
						"Maintenance Event ID24",
						"Maintenance Event ID25",
						"Maintenance Event ID26",
						"Maintenance Event ID27",
						"Maintenance Event ID28",
						"Maintenance Event ID29",
						"Maintenance Event ID3",
						"Maintenance Event ID30",
						"Maintenance Event ID31",
						"Maintenance Event ID32",
						"Maintenance Event ID33",
						"Maintenance Event ID34",
						"Maintenance Event ID35",
						"Maintenance Event ID36",
						"Maintenance Event ID37",
						"Maintenance Event ID38",
						"Maintenance Event ID39",
						"Maintenance Event ID4",
						"Maintenance Event ID40",
						"Maintenance Event ID41",
						"Maintenance Event ID42",
						"Maintenance Event ID43",
						"Maintenance Event ID44",
						"Maintenance Event ID45",
						"Maintenance Event ID46",
						"Maintenance Event ID47",
						"Maintenance Event ID48",
						"Maintenance Event ID5",
						"Maintenance Event ID6",
						"Maintenance Event ID7",
						"Maintenance Event ID8",
						"Maintenance Event ID9",
						"PWM 1 input",
						"PWM 2 input",
						"PWM 3 input",
						"PWM Output 1",
						"PWM Output 2",
						"PWM Output 3",
						"Prime",
						"System Conditions OK"
					],
					"RIO L4A": [
						"Current Loop Fault",
						"Digital Inputs",
						"Digital Outputs",
						"Health Status",
						"Maintenance Event ID1",
						"Maintenance Event ID10",
						"Maintenance Event ID11",
						"Maintenance Event ID12",
						"Maintenance Event ID13",
						"Maintenance Event ID14",
						"Maintenance Event ID15",
						"Maintenance Event ID16",
						"Maintenance Event ID17",
						"Maintenance Event ID18",
						"Maintenance Event ID19",
						"Maintenance Event ID2",
						"Maintenance Event ID20",
						"Maintenance Event ID21",
						"Maintenance Event ID22",
						"Maintenance Event ID23",
						"Maintenance Event ID24",
						"Maintenance Event ID25",
						"Maintenance Event ID26",
						"Maintenance Event ID27",
						"Maintenance Event ID28",
						"Maintenance Event ID29",
						"Maintenance Event ID3",
						"Maintenance Event ID30",
						"Maintenance Event ID31",
						"Maintenance Event ID32",
						"Maintenance Event ID33",
						"Maintenance Event ID34",
						"Maintenance Event ID35",
						"Maintenance Event ID36",
						"Maintenance Event ID37",
						"Maintenance Event ID38",
						"Maintenance Event ID39",
						"Maintenance Event ID4",
						"Maintenance Event ID40",
						"Maintenance Event ID41",
						"Maintenance Event ID42",
						"Maintenance Event ID43",
						"Maintenance Event ID44",
						"Maintenance Event ID45",
						"Maintenance Event ID46",
						"Maintenance Event ID47",
						"Maintenance Event ID48",
						"Maintenance Event ID5",
						"Maintenance Event ID6",
						"Maintenance Event ID7",
						"Maintenance Event ID8",
						"Maintenance Event ID9",
						"PWM 1 input",
						"PWM 2 input",
						"PWM 3 input",
						"PWM Output 1",
						"PWM Output 2",
						"PWM Output 3",
						"Prime",
						"System Conditions OK"
					],
					"RIO L4B": [
						"Current Loop Fault",
						"Digital Inputs",
						"Digital Outputs",
						"Health Status",
						"Maintenance Event ID1",
						"Maintenance Event ID10",
						"Maintenance Event ID11",
						"Maintenance Event ID12",
						"Maintenance Event ID13",
						"Maintenance Event ID14",
						"Maintenance Event ID15",
						"Maintenance Event ID16",
						"Maintenance Event ID17",
						"Maintenance Event ID18",
						"Maintenance Event ID19",
						"Maintenance Event ID2",
						"Maintenance Event ID20",
						"Maintenance Event ID21",
						"Maintenance Event ID22",
						"Maintenance Event ID23",
						"Maintenance Event ID24",
						"Maintenance Event ID25",
						"Maintenance Event ID26",
						"Maintenance Event ID27",
						"Maintenance Event ID28",
						"Maintenance Event ID29",
						"Maintenance Event ID3",
						"Maintenance Event ID30",
						"Maintenance Event ID31",
						"Maintenance Event ID32",
						"Maintenance Event ID33",
						"Maintenance Event ID34",
						"Maintenance Event ID35",
						"Maintenance Event ID36",
						"Maintenance Event ID37",
						"Maintenance Event ID38",
						"Maintenance Event ID39",
						"Maintenance Event ID4",
						"Maintenance Event ID40",
						"Maintenance Event ID41",
						"Maintenance Event ID42",
						"Maintenance Event ID43",
						"Maintenance Event ID44",
						"Maintenance Event ID45",
						"Maintenance Event ID46",
						"Maintenance Event ID47",
						"Maintenance Event ID48",
						"Maintenance Event ID5",
						"Maintenance Event ID6",
						"Maintenance Event ID7",
						"Maintenance Event ID8",
						"Maintenance Event ID9",
						"PWM 1 input",
						"PWM 2 input",
						"PWM 3 input",
						"PWM Output 1",
						"PWM Output 2",
						"PWM Output 3",
						"Prime",
						"System Conditions OK"
					],
					"RIO L5A": [
						"Current Loop Fault",
						"Digital Inputs",
						"Digital Outputs",
						"Health Status",
						"Maintenance Event ID1",
						"Maintenance Event ID10",
						"Maintenance Event ID11",
						"Maintenance Event ID12",
						"Maintenance Event ID13",
						"Maintenance Event ID14",
						"Maintenance Event ID15",
						"Maintenance Event ID16",
						"Maintenance Event ID17",
						"Maintenance Event ID18",
						"Maintenance Event ID19",
						"Maintenance Event ID2",
						"Maintenance Event ID20",
						"Maintenance Event ID21",
						"Maintenance Event ID22",
						"Maintenance Event ID23",
						"Maintenance Event ID24",
						"Maintenance Event ID25",
						"Maintenance Event ID26",
						"Maintenance Event ID27",
						"Maintenance Event ID28",
						"Maintenance Event ID29",
						"Maintenance Event ID3",
						"Maintenance Event ID30",
						"Maintenance Event ID31",
						"Maintenance Event ID32",
						"Maintenance Event ID33",
						"Maintenance Event ID34",
						"Maintenance Event ID35",
						"Maintenance Event ID36",
						"Maintenance Event ID37",
						"Maintenance Event ID38",
						"Maintenance Event ID39",
						"Maintenance Event ID4",
						"Maintenance Event ID40",
						"Maintenance Event ID41",
						"Maintenance Event ID42",
						"Maintenance Event ID43",
						"Maintenance Event ID44",
						"Maintenance Event ID45",
						"Maintenance Event ID46",
						"Maintenance Event ID47",
						"Maintenance Event ID48",
						"Maintenance Event ID5",
						"Maintenance Event ID6",
						"Maintenance Event ID7",
						"Maintenance Event ID8",
						"Maintenance Event ID9",
						"PWM 1 input",
						"PWM 2 input",
						"PWM 3 input",
						"PWM Output 1",
						"PWM Output 2",
						"PWM Output 3",
						"Prime",
						"System Conditions OK"
					],
					"RIO L5B": [
						"Current Loop Fault",
						"Digital Inputs",
						"Digital Outputs",
						"Health Status",
						"Maintenance Event ID1",
						"Maintenance Event ID10",
						"Maintenance Event ID11",
						"Maintenance Event ID12",
						"Maintenance Event ID13",
						"Maintenance Event ID14",
						"Maintenance Event ID15",
						"Maintenance Event ID16",
						"Maintenance Event ID17",
						"Maintenance Event ID18",
						"Maintenance Event ID19",
						"Maintenance Event ID2",
						"Maintenance Event ID20",
						"Maintenance Event ID21",
						"Maintenance Event ID22",
						"Maintenance Event ID23",
						"Maintenance Event ID24",
						"Maintenance Event ID25",
						"Maintenance Event ID26",
						"Maintenance Event ID27",
						"Maintenance Event ID28",
						"Maintenance Event ID29",
						"Maintenance Event ID3",
						"Maintenance Event ID30",
						"Maintenance Event ID31",
						"Maintenance Event ID32",
						"Maintenance Event ID33",
						"Maintenance Event ID34",
						"Maintenance Event ID35",
						"Maintenance Event ID36",
						"Maintenance Event ID37",
						"Maintenance Event ID38",
						"Maintenance Event ID39",
						"Maintenance Event ID4",
						"Maintenance Event ID40",
						"Maintenance Event ID41",
						"Maintenance Event ID42",
						"Maintenance Event ID43",
						"Maintenance Event ID44",
						"Maintenance Event ID45",
						"Maintenance Event ID46",
						"Maintenance Event ID47",
						"Maintenance Event ID48",
						"Maintenance Event ID5",
						"Maintenance Event ID6",
						"Maintenance Event ID7",
						"Maintenance Event ID8",
						"Maintenance Event ID9",
						"PWM 1 input",
						"PWM 2 input",
						"PWM 3 input",
						"PWM Output 1",
						"PWM Output 2",
						"PWM Output 3",
						"Prime",
						"System Conditions OK"
					]
				},
				"SYSTEM": {
					"SYSTEM": [
						"Data Logger Average CAN Loading",
						"Data Logger Board Temperature",
						"Data Logger CH0 ACK",
						"Data Logger CH0 CPU write",
						"Data Logger CH0 CRC",
						"Data Logger CH0 RX Error Count",
						"Data Logger CH0 TX Error Count",
						"Data Logger CH0 bit0",
						"Data Logger CH0 bit1",
						"Data Logger CH0 form",
						"Data Logger CH0 none",
						"Data Logger CH0 stuff",
						"Data Logger CH1 ACK",
						"Data Logger CH1 CPU write",
						"Data Logger CH1 CRC",
						"Data Logger CH1 RX Error Count",
						"Data Logger CH1 TX Error Count",
						"Data Logger CH1 bit0",
						"Data Logger CH1 bit1",
						"Data Logger CH1 form",
						"Data Logger CH1 none",
						"Data Logger CH1 stuff",
						"Data Logger CH2 ACK",
						"Data Logger CH2 CPU write",
						"Data Logger CH2 CRC",
						"Data Logger CH2 RX Error Count",
						"Data Logger CH2 TX Error Count",
						"Data Logger CH2 bit0",
						"Data Logger CH2 bit1",
						"Data Logger CH2 form",
						"Data Logger CH2 none",
						"Data Logger CH2 stuff",
						"Data Logger CH3 ACK",
						"Data Logger CH3 CPU write",
						"Data Logger CH3 CRC",
						"Data Logger CH3 RX Error Count",
						"Data Logger CH3 TX Error Count",
						"Data Logger CH3 bit0",
						"Data Logger CH3 bit1",
						"Data Logger CH3 form",
						"Data Logger CH3 none",
						"Data Logger CH3 stuff",
						"Data Logger CPU Temperature",
						"Data Logger Current CAN Loading",
						"Data Logger Peak CAN Loading"
					]
				}
			}
		}
		
		if (result.code === 2000) {
			if (!result.data || Object.keys(result.data).length === 0) {
				ElMessage.warning('当前所选时间范围内没有数据');
				return;
			}

			nodeData.value = result.data;
			// 设置默认值
			const firstType = Object.keys(result.data)[0] || '';
			const firstNode = Object.keys((result.data as any)[firstType] || {})[0] || '';
			const firstSignal = (result.data as any)[firstType]?.[firstNode]?.[0] || '';
			
			nodeTypeVal.value = firstType;
			nodeNameVal.value = firstNode;
			signalVal.value = firstSignal;
		}
	} catch (error: any) {
		console.error('获取项目信息错误详情:', error);
		ElMessage.error(`获取项目信息失败: ${error.message || '未知错误'}`);
	}
};

// 时间范围变化时重新获取节点数据
const handleTimeRangeConfirm = () => {
	getInfluxdbInfo();
};

// 获取 influxdb 数据并更新图表
const updateEchart = async () => {
	if (!timeRange.value || timeRange.value.length !== 2) {
		ElMessage.warning('请选择时间范围');
		return;
	}

	if (!finalSelectedSignals.value.length) {
		ElMessage.warning('请先选择要查询的信号');
		return;
	}

	// 修改时间格式处理
	const formatDate = (dateStr: string) => {
		// 将字符串转换为 Date 对象
		const date = new Date(dateStr);
		return date.toISOString().split('.')[0] + 'Z';
	};

	const start_time = formatDate(timeRange.value[0]);
	const end_time = formatDate(timeRange.value[1]);
	
	try {
		// 优先使用 fileData 中的参数，如果没有则使用 store 中的值
		const binfile_Postgresql_id = (props.fileData && props.isFromClick && props.fileData.file_Postgresql_id)
			? props.fileData.file_Postgresql_id
			: store.$state.influxdbInfo.binfile_Postgresql_id;

		const binfile_Minio_version_id = (props.fileData && props.isFromClick && props.fileData.file_Minio_version_id)
			? props.fileData.file_Minio_version_id
			: store.$state.influxdbInfo.binfile_Minio_version_id;

		const influx_ubique_tagId = (props.fileData && props.isFromClick && props.fileData.influx_ubique_tagId)
			? props.fileData.influx_ubique_tagId
			: store.$state.influxdbInfo.influx_ubique_tagId;

		// 准备批量查询的信号数据
		const signals = finalSelectedSignals.value.map(signal => ({
			signal_name: signal.signal,
			node_name: signal.nodeName,
			node_type: signal.nodeType
		}));

		// 发送批量请求
		const response = await fetch(
			`${baseBackendUrl}api/data_pipeline/file/influxdb/query/`,
			{
				method: 'POST',
				credentials: 'include',
				headers: {
					"Authorization": 'JWT ' + Session.get('token'),
					"Content-Type": 'application/json'
				},
				body: JSON.stringify({
					project_name: project_name.value,
					project_type: project_type.value,
					start_time: start_time,
					end_time: end_time,
					signals: signals,
					binfile_Postgresql_id: binfile_Postgresql_id,
					binfile_Minio_version_id: binfile_Minio_version_id,
					influx_ubique_tagId: influx_ubique_tagId
				})
			}
		);

		if (!response.ok) {
			throw new Error(`请求失败，状态码：${response.status}`);
		}

		const result = await response.json();
		// 获取缓存ID
		cache_id.value = result.cache_id;

		if (result.code === 2000) {
			console.log('Echart allData = ', result.data);
			updateEchartFigure(result.data);
			handleCalculatedMethodChange(calculatedMethod.value)
		} else {
			ElMessage.error(result.msg || '查询失败');
		}
	} catch (error) {
		console.error('查询失败:', error);
		ElMessage.error('获取数据失败');
	}
};

// echart 折线的颜色
const deepColors = [
    '#1f77b4', // 深蓝
    '#d62728', // 深红
    '#2ca02c', // 深绿
    '#ff7f0e', // 橙色
    '#9467bd'  // 紫色
];

// 更新图表
const updateEchartFigure = (allData: any[]) => {
    if (!myChart) {
        myChart = echarts.init(document.getElementById('echartContainer'));
    }

    myChart.clear();

    // 计算采样周期 T（单位：ms）
    if (allData[0]?.data?.length < 2) {
        console.warn('数据不足，无法绘制');
        return;
    }

    const firstTwoPoints = allData[allData.length - 1].data.slice(0, 2);
    const T = new Date(firstTwoPoints[1].time).getTime() - new Date(firstTwoPoints[0].time).getTime();

    // 数据预处理：插入 null 分隔大跨度区域 若连续5个时间点都没有数据, 则视为中断区, 此部分不用折线显示
    function processData(dataArray: any[]) {
        const processed: any[] = [];
        for (let i = 0; i < dataArray.length - 1; i++) {
            processed.push([
                new Date(dataArray[i].time.replace(' ', 'T')),
                dataArray[i].value
            ]);

            const currTime = new Date(dataArray[i].time.replace(' ', 'T'));
            const nextTime = new Date(dataArray[i + 1].time.replace(' ', 'T'));
            const diff = nextTime.getTime() - currTime.getTime();

            if (diff > 5 * T) {
                processed.push(null); // 插入 null 表示断开
            }
        }

        // 添加最后一个点
        processed.push([
            new Date(dataArray[dataArray.length - 1].time.replace(' ', 'T')),
            dataArray[dataArray.length - 1].value
        ]);

        return processed;
    }

    const option = {
        title: {
            text: '多信号数据趋势',
            left: 'center',
            top: 0
        },
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'line'
			},
			formatter: (params: any) => {
				if (!params || !Array.isArray(params) || params.length === 0) return '';

				// const time = params[0].axisValueLabel || new Date(params[0].value[0]).toLocaleString();

				// 自定义格式化时间函数（含毫秒）
				const formatTimeWithMs = (timestamp: any) => {
					const date = new Date(timestamp);
					const hours = String(date.getHours()).padStart(2, '0');
					const minutes = String(date.getMinutes()).padStart(2, '0');
					const seconds = String(date.getSeconds()).padStart(2, '0');
					const milliseconds = String(date.getMilliseconds()).padStart(3, '0');
					return `${date.toLocaleDateString()} ${hours}:${minutes}:${seconds}.${milliseconds}`;
				};

				// const time = params[0].axisValueLabel || formatTimeWithMs(params[0].value[0]);
				const time = formatTimeWithMs(params[0].value[0]);


				let tooltipHtml = `<div style="padding: 10px; background: #fff; border: 1px solid #ccc; border-radius: 4px;">`;
				tooltipHtml += `<strong>${time}</strong><br/>`;

				params.forEach(item => {
					const value = item.value[1];
					const displayValue = value !== null && value !== undefined
						? Number(value).toFixed(2)
						: '-';
					tooltipHtml += `
						<span style="display:inline-block;margin:2px 0;">
							${item.marker} ${item.seriesName}: <b>${displayValue}</b>
						</span><br/>
					`;
				});

				tooltipHtml += '</div>';
				return tooltipHtml;
			}
		},
        legend: {
            type: 'scroll',
            orient: 'horizontal',
            top: 25,
            left: 'center',
            width: '90%',
            height: 60,
            pageButtonPosition: 'end',
            data: allData.map(item => item.name),
            selector: false,
            padding: [5, 10],
            itemGap: 10,
            itemHeight: 14,
            itemWidth: 25,
            textStyle: {
                fontSize: 12
            },
        },
        grid: {
            top: '25%',
            left: '3%',
            right: '3%',
            bottom: '8%',
            containLabel: true
        },
        dataZoom: [
            {
                type: 'inside',
                xAxisIndex: 0,
                filterMode: 'filter'
            },
            {
                type: 'slider',
                xAxisIndex: 0,
                filterMode: 'filter',
                height: 10,
                bottom: 15,
                handleIcon: 'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
                handleSize: '80%',
                handleStyle: {
                    color: '#fff',
                    shadowBlur: 3,
                    shadowColor: 'rgba(0, 0, 0, 0.6)',
                    shadowOffsetX: 2,
                    shadowOffsetY: 5
                }
            }
        ],
        // brush: {
        //     toolbox: ['rect', 'polygon', 'lineX', 'lineY', 'keep', 'clear'],
        //     xAxisIndex: 0,
        //     brushLink: 'all',
        //     outOfBrush: {
        //         colorAlpha: 0.1
        //     },
        //     brushStyle: {
        //         borderWidth: 1,
        //         color: 'rgba(120,140,180,0.3)',
        //         borderColor: 'rgba(120,140,180,0.8)'
        //     },
        //     emphasis: {
        //         brushStyle: {
        //             borderWidth: 2,
        //             color: 'rgba(120,140,180,0.4)',
        //             borderColor: 'rgba(120,140,180,1)'
        //         }
        //     }
        // },
        toolbox: {
            feature: {
                // brush: {
                //     type: ['lineX', 'clear']
                // },
                // dataZoom: {
                //     yAxisIndex: false
                // },
                // restore: {},
                saveAsImage: {}
            },
            right: 20,
            top: 20
        },
        xAxis: {
            type: 'time',
            axisLabel: {
                showMaxLabel: true
            }
        },
        yAxis: {
            type: 'value',
            splitLine: {
                lineStyle: {
                    type: 'dashed'
                }
            }
        },
        series: allData.map(item => ({
            name: item.name,
            type: 'line',
            showSymbol: false,
            connectNulls: false, // 不连接 null 值
			emphasis: {
				focus: 'self'
			},
			lineStyle: {
				width: 3,
			},
            data: processData(item.data)
        }))
    };

    // 图例分三行显示
    const legendData = allData.map(item => item.name);
    const itemsPerRow = Math.ceil(legendData.length / 3);
    option.legend.data = legendData;

    myChart.setOption(option);

    // 添加brush选择事件监听
    myChart.off('brushSelected');
    myChart.on('brushSelected', (params: any) => {
        if (params.batch && params.batch.length > 0) {
            const brushComponent = params.batch[0];
            if (brushComponent.selected && brushComponent.selected.length > 0) {
                const selected = brushComponent.selected[0];
                if (selected.dataIndex && selected.dataIndex.length >= 2) {
                    // 获取选择范围的起始和结束索引
                    const startIndex = Math.min(...selected.dataIndex);
                    const endIndex = Math.max(...selected.dataIndex);

                    // 从第一个系列的数据中获取时间范围
                    const firstSeriesData = allData[0].data;
                    if (firstSeriesData && firstSeriesData.length > endIndex) {
                        const startTime = firstSeriesData[startIndex].time;
                        const endTime = firstSeriesData[endIndex].time;

                        // 更新时间选择器的值
                        timeRange.value = [startTime, endTime];

                        // 提示用户选择的时间范围
                        ElMessage.success(`已选择时间范围: ${startTime} 至 ${endTime}`);
                    }
                }
            }
        }
    });
};

// 弹窗相关的方法
const showNodeData = () => {
	dialogVisible.value = true;
}

const handleClose = () => {
	dialogVisible.value = false;
}

const handleTypeSelect = (type: string) => {
	selectedType.value = type;
	selectedNode.value = '';
	// 清空批量选择
	selectedSignals.value = [];
}

const handleNodeSelect = (node: string) => {
	selectedNode.value = node;
	// 清空批量选择
	selectedSignals.value = [];
}

const handleSignalSelect = (signal: string, event?: MouseEvent) => {
	const index = selectedSignals.value.indexOf(signal);

	if (event && event.ctrlKey) {
		// Ctrl+点击：多选模式，切换选择状态
		if (index > -1) {
			// 如果已选中，则取消选择
			selectedSignals.value.splice(index, 1);
		} else {
			// 如果未选中，则添加到选择列表
			selectedSignals.value.push(signal);
		}
	} else {
		// 普通点击：单选模式（视为只有一个元素的多选）
		if (index > -1) {
			// 如果已选中，则取消选择
			selectedSignals.value = [];
		} else {
			// 如果未选中，则设置为唯一选中项
			selectedSignals.value = [signal];
		}
	}
}

const handleFinalSignalSelect = (item: any, event?: MouseEvent) => {
	const index = selectedFinalSignals.value.indexOf(item);

	if (event && event.ctrlKey) {
		// Ctrl+点击：多选模式，切换选择状态
		if (index > -1) {
			// 如果已选中，则取消选择
			selectedFinalSignals.value.splice(index, 1);
		} else {
			// 如果未选中，则添加到选择列表
			selectedFinalSignals.value.push(item);
		}
	} else {
		// 普通点击：单选模式（视为只有一个元素的多选）
		if (index > -1) {
			// 如果已选中，则取消选择
			selectedFinalSignals.value = [];
		} else {
			// 如果未选中，则设置为唯一选中项
			selectedFinalSignals.value = [item];
		}
	}
}

const addToFinal = () => {
	if (!selectedType.value || !selectedNode.value) {
		ElMessage.warning('请先选择节点类型和节点名称');
		return;
	}

	if (selectedSignals.value.length === 0) {
		ElMessage.warning('请先选择要添加的信号');
		return;
	}

	// 统一处理多选（包括单选视为只有一个元素的多选）
	const newSignals = selectedSignals.value.map(signal => ({
		nodeType: selectedType.value,
		nodeName: selectedNode.value,
		signal: signal
	}));

	// 过滤掉已存在的信号
	const signalsToAdd = newSignals.filter(newSignal =>
		!finalSelectedSignals.value.some(item =>
			item.nodeType === newSignal.nodeType &&
			item.nodeName === newSignal.nodeName &&
			item.signal === newSignal.signal
		)
	);

	if (signalsToAdd.length > 0) {
		finalSelectedSignals.value.push(...signalsToAdd);
		const message = signalsToAdd.length === 1
			? '成功添加信号'
			: `成功添加 ${signalsToAdd.length} 个信号`;
		ElMessage.success(message);
		// 保持选中状态，不清空选择
	} else {
		ElMessage.warning('所选信号已存在');
	}
}

const removeFromFinal = () => {
	if (selectedFinalSignals.value.length === 0) {
		ElMessage.warning('请先选择要删除的信号');
		return;
	}

	// 统一处理多选（包括单选视为只有一个元素的多选）
	const countToRemove = selectedFinalSignals.value.length;
	finalSelectedSignals.value = finalSelectedSignals.value.filter(item =>
		!selectedFinalSignals.value.includes(item)
	);

	const message = countToRemove === 1
		? '成功删除信号'
		: `成功删除 ${countToRemove} 个信号`;
	ElMessage.success(message);

	// 清空选择
	selectedFinalSignals.value = [];
}

const handleConfirm = () => {
	console.log('finalSelectedSignals === ', finalSelectedSignals.value)
	dialogVisible.value = false;
}

// 添加导出Excel功能
const exportToExcel = () => {
    if (!myChart || !myChart.getOption()) {
        ElMessage.warning('暂无数据可导出');
        return;
    }

    try {
        const option = myChart.getOption();
        const series = option.series;

		console.log('series ================', series)

        // 收集所有时间点（去重并排序）
        const timeMap = new Map(); // 使用Map来更好地处理时间点和数据的对应关系

        series.forEach((s: any, seriesIndex: number) => {
            s.data.forEach((d: any) => {
                // 过滤掉null值，d: [time, value] - 新的数据结构
                if (d !== null && Array.isArray(d) && d.length >= 2) {
                    const timeKey = d[0].getTime ? d[0].getTime() : d[0]; // 使用时间戳作为key确保精确匹配

                    if (!timeMap.has(timeKey)) {
                        timeMap.set(timeKey, {
                            time: d[0],
                            values: new Array(series.length).fill('') // 初始化所有信号的值为空
                        });
                    }

                    // 在对应的信号位置设置值
                    timeMap.get(timeKey).values[seriesIndex] = d[1];
                }
            });
        });

        // 按时间排序
        const sortedTimeEntries = Array.from(timeMap.entries()).sort((a, b) => {
            const timeA = a[1].time.getTime ? a[1].time.getTime() : a[1].time;
            const timeB = b[1].time.getTime ? b[1].time.getTime() : b[1].time;
            return timeA - timeB;
        });

        // 格式化时间到毫秒精度的函数
        const formatTimeToMilliseconds = (time: any) => {
            if (time instanceof Date) {
                // 格式化为 YYYY-MM-DD HH:mm:ss.SSS
                const year = time.getFullYear();
                const month = String(time.getMonth() + 1).padStart(2, '0');
                const day = String(time.getDate()).padStart(2, '0');
                const hours = String(time.getHours()).padStart(2, '0');
                const minutes = String(time.getMinutes()).padStart(2, '0');
                const seconds = String(time.getSeconds()).padStart(2, '0');
                const milliseconds = String(time.getMilliseconds()).padStart(3, '0');
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
            }
            return time;
        };

        // 构建表头
        const excelData = [['Absolute Time', ...series.map((s: any) => s.name)]];

        // 填充每一行 - 确保同一时间点的所有信号数据在同一行
        sortedTimeEntries.forEach(([timeKey, timeData]) => {
            const formattedTime = formatTimeToMilliseconds(timeData.time);
            const row = [formattedTime, ...timeData.values];
            excelData.push(row);
        });

        // 创建工作簿
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.aoa_to_sheet(excelData);

        // 设置时间列的列宽
        const colWidths = [{ wch: 25 }]; // 时间列宽度
        for (let i = 1; i < excelData[0].length; i++) {
            colWidths.push({ wch: 15 }); // 数据列宽度
        }
        ws['!cols'] = colWidths;

        // 添加工作表到工作簿
        XLSX.utils.book_append_sheet(wb, ws, '数据趋势');

        // 使用更安全的下载方式，避免HTTPS警告
        const fileName = `数据趋势_${new Date().toLocaleDateString().replace(/\//g, '-')}_${Date.now()}.xlsx`;

        // 生成二进制数据
        const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });

        // 创建Blob对象
        const blob = new Blob([wbout], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';

        // 触发下载
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        ElMessage.success('导出成功');
    } catch (error) {
        console.error('导出Excel失败:', error);
        ElMessage.error('导出失败');
    }
};

const project_name = ref('')
const project_type = ref('')
const getProInfo = () => {
	project_name.value = store.$state.projectInfo.project_name
	project_type.value = store.$state.projectInfo.project_type
}

// 监听 fileData 的变化，当点击不同行时重新获取数据
watch(() => props.fileData, async (newFileData: any) => {
	if (newFileData && props.isFromClick) {
		console.log('检测到 fileData 变化，重新获取数据:', newFileData);

		// 更新时间范围为选中行的时间
		if (newFileData.start_time && newFileData.end_time) {
			timeRange.value = [newFileData.start_time, newFileData.end_time];
			console.log('更新时间范围:', timeRange.value);
		}

		// 更新 pinia 中的文件相关信息，用于后续查询
		if (newFileData.file_Postgresql_id && newFileData.file_Minio_version_id && newFileData.influx_ubique_tagId) {
			store.$state.influxdbInfo.binfile_Postgresql_id = newFileData.file_Postgresql_id;
			store.$state.influxdbInfo.binfile_Minio_version_id = newFileData.file_Minio_version_id;
			store.$state.influxdbInfo.influx_ubique_tagId = newFileData.influx_ubique_tagId;
			console.log('更新文件ID信息:', {
				binfile_Postgresql_id: newFileData.file_Postgresql_id,
				binfile_Minio_version_id: newFileData.file_Minio_version_id,
				influx_ubique_tagId: newFileData.influx_ubique_tagId,
			});
		}

		getProInfo(); // 从pinia里获取当前的基本项目信息, 用于api请求
		await getInfluxdbInfo(); // 重新获取节点数据
	}
}, { deep: true });

onMounted(async () => {
	// 如果是从点击进入且有文件数据，先更新相关信息
	if (props.fileData && props.isFromClick) {
		// 更新时间范围
		if (props.fileData.start_time && props.fileData.end_time) {
			timeRange.value = [props.fileData.start_time, props.fileData.end_time];
		}

		// 更新文件ID信息到 pinia
		if (props.fileData.file_Postgresql_id && props.fileData.file_Minio_version_id && props.fileData.influx_ubique_tagId) {
			store.$state.influxdbInfo.binfile_Postgresql_id = props.fileData.file_Postgresql_id;
			store.$state.influxdbInfo.binfile_Minio_version_id = props.fileData.file_Minio_version_id;
			store.$state.influxdbInfo.influx_ubique_tagId = props.fileData.influx_ubique_tagId;
		}
	}

	getProInfo() // 从pinia里获取当前的基本项目信息, 用于api请求
	await getInfluxdbInfo(); // 初始化时获取节点数据，确保弹窗能正常显示
});

// 小波变换尺度信息
const waveletScaleInfo = ref<WaveletSignalInfo[]>([]);

// 保存原始系列数据，用于恢复可见性
const originalSeriesData = ref<any[]>([]);

// 频率统计信息
const frequencyStatistics = ref<FrequencyStats[]>([]);
</script>

<style scoped>
.signal-selection-dialog :deep(.el-dialog__body) {
	padding: 20px 24px;
}

.section-header {
	@apply mb-3 pb-2 border-b border-gray-200;
}

.signal-list {
	@apply space-y-1;
}

.signal-item {
	@apply px-4 py-2.5 cursor-pointer hover:bg-gray-50 rounded-lg transition-colors duration-200 text-gray-600;
}

.signal-item.selected {
	@apply bg-blue-50 text-blue-600 font-medium;
}

.signal-item.multi-selected {
	@apply bg-green-50 text-green-600 font-medium border-l-4 border-green-400;
}

.final-signal {
	@apply border border-gray-100;
}

.signal-info {
	@apply flex flex-col gap-0.5;
}

.signal-type {
	@apply text-sm font-medium;
}

.signal-name, .signal-value {
	@apply text-sm text-gray-500;
}

.transfer-buttons {
	@apply absolute right-[-16px] top-1/2 transform -translate-y-1/2 flex flex-col gap-2;
}

.transfer-btn {
	@apply h-6 w-6 flex items-center justify-center cursor-pointer text-gray-600 hover:text-blue-600 transition-colors duration-200 select-none;
}

.custom-scrollbar :deep(.el-scrollbar__thumb) {
	@apply bg-gray-300;
}

.dialog-footer {
	@apply flex justify-end gap-3;
}

.echart-container {
	@apply rounded-lg bg-gradient-to-br from-white to-gray-50;
	box-shadow: 
		0 8px 16px -4px rgba(0, 0, 0, 0.08),  /* 外部主阴影 */
		0 4px 8px -2px rgba(0, 0, 0, 0.06),   /* 外部次阴影 */
		inset 0 2px 4px rgba(255, 255, 255, 0.9),  /* 顶部内阴影-亮 */
		inset 0 -2px 4px rgba(0, 0, 0, 0.05);      /* 底部内阴影-暗 */
	backdrop-filter: blur(8px);
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.echart-container:hover {
	transform: translateY(-3px);
	box-shadow: 
		0 12px 24px -6px rgba(0, 0, 0, 0.12),  /* 外部主阴影加深 */
		0 6px 12px -3px rgba(0, 0, 0, 0.08),   /* 外部次阴影加深 */
		inset 0 2px 4px rgba(255, 255, 255, 0.9),  /* 保持内阴影 */
		inset 0 -2px 4px rgba(0, 0, 0, 0.05);
}

.el-form--inline .el-form-item {
	margin-right: 0px !important;
}
</style>
