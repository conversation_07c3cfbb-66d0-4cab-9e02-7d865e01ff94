<template>
  <el-input
    v-model="searchValue"
    :placeholder="placeholder"
    class="w-64"
    clearable
    prefix-icon="Search"
    @input="handleInput"
  />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  placeholder: {
    type: String,
    default: 'Search...'
  },
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

const searchValue = ref(props.modelValue)

watch(() => props.modelValue, (newValue) => {
  searchValue.value = newValue
})

const handleInput = () => {
  emit('update:modelValue', searchValue.value)
}
</script> 