<template>
  <div v-if="showResult" class="bg-white rounded-xl p-8 shadow-lg border border-gray-100">
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-medium">Analysis Results</h3>
    </div>
    <!-- 筛选条件 -->
    <div class="grid grid-cols-4 gap-4 mb-6">
      <div>
        <label class="block text-sm text-gray-600 mb-2">Time Range</label>
        <el-date-picker
          v-model="timeRange"
          type="daterange"
          range-separator="-"
          start-placeholder="Start Date"
          end-placeholder="End Date"
          class="w-full !rounded-button"
        />
      </div>
      <div>
        <label class="block text-sm text-gray-600 mb-2">Node Type</label>
        <el-select v-model="selectedNodeType" class="w-full !rounded-button">
          <el-option v-for="type in nodeTypes" :key="type" :label="type" :value="type" />
        </el-select>
      </div>
      <div>
        <label class="block text-sm text-gray-600 mb-2">Node</label>
        <el-select v-model="selectedNode" class="w-full !rounded-button">
          <el-option v-for="node in nodes" :key="node" :label="node" :value="node" />
        </el-select>
      </div>
      <div>
        <label class="block text-sm text-gray-600 mb-2">Signal</label>
        <el-select v-model="selectedSignal" class="w-full !rounded-button">
          <el-option v-for="signal in signals" :key="signal" :label="signal" :value="signal" />
        </el-select>
      </div>
    </div>
    <!-- 数据概览图表 -->
    <div class="grid grid-cols-2 gap-6 mb-6">
      <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6">
        <h4 class="text-gray-600 mb-4">Data Distribution</h4>
        <div class="h-64" ref="distributionChart"></div>
      </div>
      <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6">
        <h4 class="text-gray-600 mb-4">Time Trend</h4>
        <div class="h-64" ref="trendChart"></div>
      </div>
    </div>
    <!-- 数据明细表格 -->
    <div class="bg-white rounded-xl">
      <el-table :data="analysisResults" style="width: 100%">
        <el-table-column prop="timestamp" label="Time" width="180" />
        <el-table-column prop="value" label="Value" width="120" />
        <el-table-column prop="status" label="Status">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'normal' ? 'success' : 'danger'" class="!rounded-button">
              {{ scope.row.status === 'normal' ? 'Normal' : 'Abnormal' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="Description" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps<{
  showResult: boolean
}>()

const timeRange = ref([])
const selectedNodeType = ref('')
const selectedNode = ref('')
const selectedSignal = ref('')

const nodeTypes = ref(['Process Node', 'Control Node', 'Sensor Node'])
const nodes = ref(['Node-001', 'Node-002', 'Node-003', 'Node-004'])
const signals = ref(['Temperature', 'Pressure', 'Flow Rate', 'Vibration'])

const analysisResults = ref([
  {
    timestamp: '2024-03-01 14:30:00',
    value: '98.5',
    status: 'normal',
    description: 'Equipment running normally',
  },
  {
    timestamp: '2024-03-01 14:35:00',
    value: '97.8',
    status: 'normal',
    description: 'Equipment running normally',
  },
  {
    timestamp: '2024-03-01 14:40:00',
    value: '85.2',
    status: 'warning',
    description: 'Value below threshold',
  },
  {
    timestamp: '2024-03-01 14:45:00',
    value: '99.1',
    status: 'normal',
    description: 'Equipment running normally',
  },
])

const distributionChart = ref<HTMLElement | null>(null)
const trendChart = ref<HTMLElement | null>(null)

onMounted(() => {
  initCharts()
})

const initCharts = () => {
  // 初始化分布图表
  const distribution = echarts.init(distributionChart.value!)
  distribution.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
    },
    grid: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 40,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: ['Normal', 'Warning', 'Error', 'Others'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [320, 120, 23, 15],
        type: 'bar',
        barWidth: '40%',
        itemStyle: {
          borderRadius: [4, 4, 0, 0],
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: '#60a5fa',
            },
            {
              offset: 1,
              color: '#3730a3',
            },
          ]),
        },
      },
    ],
  })

  // 初始化趋势图表
  const trend = echarts.init(trendChart.value!)
  trend.setOption({
    animation: false,
    tooltip: {
      trigger: 'axis',
    },
    grid: {
      top: 20,
      right: 20,
      bottom: 20,
      left: 40,
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['14:30', '14:35', '14:40', '14:45', '14:50', '14:55'],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [98.5, 97.8, 85.2, 99.1, 96.8, 97.3],
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: '#60a5fa',
        },
        itemStyle: {
          color: '#60a5fa',
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(96, 165, 250, 0.3)',
            },
            {
              offset: 1,
              color: 'rgba(96, 165, 250, 0.1)',
            },
          ]),
        },
      },
    ],
  })
}

watch([timeRange, selectedNodeType, selectedNode, selectedSignal], () => {
  if (timeRange.value && selectedNodeType.value && selectedNode.value && selectedSignal.value) {
    updateCharts()
  }
})

const updateCharts = () => {
  // 更新图表数据的逻辑
  const distribution = echarts.init(distributionChart.value!)
  const trend = echarts.init(trendChart.value!)
  // 实际应用中这里需要根据筛选条件重新获取数据并更新图表
}
</script>

<style scoped>
.el-table {
  --el-table-border-color: #e5e7eb;
  --el-table-header-bg-color: #f8faff;
  --el-table-row-hover-bg-color: #f0f7ff;
  border-radius: 0.75rem;
  overflow: hidden;
}
</style>
