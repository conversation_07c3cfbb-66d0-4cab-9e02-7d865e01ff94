<template>
  <el-dropdown @command="handleCommand" trigger="click">
    <span class="el-dropdown-link cursor-pointer flex items-center">
      <el-icon class="mr-1"><Sort /></el-icon>
      <span class="text-sm">Sort</span>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item 
          v-for="option in options" 
          :key="`${option.key}-${option.order}`"
          :command="`${option.key}-${option.order}`"
          :class="{ 'font-bold': currentKey === option.key && currentOrder === option.order }"
        >
          {{ option.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { Sort } from '@element-plus/icons-vue'

interface SortOption {
  key: string
  label: string
  order: 'asc' | 'desc'
}

const props = defineProps({
  options: {
    type: Array as () => SortOption[],
    required: true
  },
  currentKey: {
    type: String,
    required: true
  },
  currentOrder: {
    type: String as () => 'asc' | 'desc',
    required: true
  }
})

const emit = defineEmits(['sort'])

const handleCommand = (command: string) => {
  const [key, order] = command.split('-')
  emit('sort', { key, order })
}
</script> 