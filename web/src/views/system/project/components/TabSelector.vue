<template>
  <div class="flex border-b border-gray-200">
    <div
      v-for="tab in tabs"
      :key="tab.value"
      class="cursor-pointer px-4 py-2 mr-2 transition-colors"
      :class="{ 
        'border-b-2 font-medium': modelValue === tab.value,
        'text-gray-800': modelValue === tab.value,
        'text-gray-500 hover:text-gray-700': modelValue !== tab.value,
        'border-gray-500': tab.value === -1
      }"
      :style="{
        'border-color': modelValue === tab.value && tab.color ? tab.color : '',
      }"
      @click="$emit('update:modelValue', tab.value)"
    >
      {{ tab.label }}
    </div>
  </div>
</template>

<script setup lang="ts">
interface Tab {
  label: string
  value: number
  color: string
}

interface Props {
  modelValue: number
  tabs: Tab[]
}

defineProps<Props>()
defineEmits<{
  (e: 'update:modelValue', value: number): void
}>()
</script> 