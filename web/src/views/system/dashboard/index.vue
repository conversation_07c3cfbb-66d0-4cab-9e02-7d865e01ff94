<template>
	<div class="home-container project-home">
		<!-- Welcome Section -->
		<div class="welcome-section">
			<h1 class="welcome-title">Data Dashboard</h1>
			<p class="welcome-desc">This ia dashboard</p>
		</div>
	</div>
</template>

<script setup lang="ts">

</script>

<style scoped lang="scss">
.home-container {
	padding: 40px;
	background: linear-gradient(135deg, #f8f9fd 0%, #f1f4f9 100%);
	min-height: 100vh;
}

.project-home {
	max-width: 1600px;
	margin: 0 auto;
}

.welcome-section {
	text-align: center;
	margin-bottom: 60px;
	flex-shrink: 0;
	
	.welcome-title {
		font-size: 36px;
		color: #1a1f36;
		margin-bottom: 12px;
		font-weight: 600;
		letter-spacing: 1.8px;
		text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
		background: linear-gradient(
			135deg,
			#1a2b4b 0%,
			#2d5af7 25%,
			#2b4c8f 50%,
			#2d5af7 75%,
			#1a2b4b 100%
		);
		-webkit-background-clip: text;
		background-clip: text;
		-webkit-text-fill-color: transparent;
		position: relative;
		
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(
				90deg,
				transparent 0%,
				rgba(255, 255, 255, 0.2) 15%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0.2) 85%,
				transparent 100%
			);
			-webkit-background-clip: text;
			background-clip: text;
			transform: translateX(-100%);
			animation: shine 5s infinite;
		}
		
		&::after {
			background: linear-gradient(
				90deg,
				rgba(45, 90, 247, 0.1) 0%,
				rgba(45, 90, 247, 0.6) 50%,
				rgba(45, 90, 247, 0.1) 100%
			);
			box-shadow: 0 0 15px rgba(45, 90, 247, 0.3);
		}
	}
	
	.welcome-desc {
		font-size: 15px;
		color: #445668;
		line-height: 1.6;
		font-weight: 400;
		letter-spacing: 0.5px;
		opacity: 0.85;
		font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		background: linear-gradient(
			90deg,
			#34495e 0%,
			#3498db 30%,
			#2980b9 70%,
			#34495e 100%
		);
		-webkit-background-clip: text;
		background-clip: text;
		-webkit-text-fill-color: transparent;
		position: relative;
		
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(
				90deg,
				transparent 0%,
				rgba(255, 255, 255, 0.1) 25%,
				rgba(255, 255, 255, 0.2) 50%,
				rgba(255, 255, 255, 0.1) 75%,
				transparent 100%
			);
			-webkit-background-clip: text;
			background-clip: text;
			transform: translateX(-100%);
			animation: shine 7s infinite;
		}
		
		max-width: 600px;
		margin: 0 auto;
	}
}

.steps-section {
	background: rgba(255, 255, 255, 0.95);
	padding: 40px;
	border-radius: 16px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
	margin-bottom: 30px;
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.8);
}

.step-content {
	background: rgba(255, 255, 255, 0.95);
	padding: 40px;
	border-radius: 16px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.8);
}

.panel-header {
	display: flex;
	align-items: center;
	margin-bottom: 30px;
	
	i {
		font-size: 28px;
		margin-right: 16px;
		color: #2d5af7;
	}
	
	h3 {
		font-size: 24px;
		color: #1a1f36;
		margin: 0;
		font-weight: 600;
		letter-spacing: 0.5px;
	}
}

.panel-desc {
	color: #4a5568;
	margin-bottom: 30px;
	line-height: 1.8;
	font-size: 16px;
}

.upload-area {
	background: #f8fafd;
	border-radius: 12px;
	padding: 40px;
	border: 2px dashed #e2e8f0;
	transition: all 0.3s ease;
	
	&:hover {
		border-color: #2d5af7;
		background: #f5f8ff;
	}
	
	.el-card {
		margin-bottom: 20px;
		border: none;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
		
		&:hover {
			transform: translateY(-2px);
			box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
		}
	}
}

.review-container {
	display: grid;
	grid-template-columns: 1.2fr 0.8fr;
	gap: 30px;
	margin-bottom: 40px;
}

.review-preview, .review-rules {
	background: #f8fafd;
	border-radius: 12px;
	padding: 10px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.preview-header, .rules-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 20px;
	padding-bottom: 15px;
	border-bottom: 2px solid #edf2f7;
	
	h4 {
		font-size: 18px;
		color: #1a1f36;
		margin: 0;
		font-weight: 600;
	}
}

.preview-content {
	height: 700px;
	
	.preview-wrapper {
		height: 100%;
		
		.file-info {
			padding: 30px;
		}
	}
}

.review-progress {
	background: #f8f9fb;
	border-radius: 6px;
	padding: 20px;
	margin-top: 24px;
	
	h4 {
		font-size: 16px;
		color: #2c3e50;
		margin-bottom: 16px;
	}
}

.progress-item {
	margin-bottom: 16px;
	
	.progress-header {
		display: flex;
		justify-content: space-between;
		margin-bottom: 8px;
		color: #5e6d82;
	}
}

.step-actions {
	margin-top: 40px;
	display: flex;
	justify-content: flex-end;
	gap: 16px;
	
	.el-button {
		padding: 12px 24px;
		font-size: 16px;
		font-weight: 500;
		
		&--primary {
			background: #2d5af7;
			border-color: #2d5af7;
			
			&:hover {
				background: #1e3fd1;
				border-color: #1e3fd1;
			}
		}
	}
}

// 添加精致的动画效果
.el-step {
	transition: all 0.3s ease;
	
	&:hover {
		transform: translateY(-2px);
	}
}

.el-step__title {
	font-weight: 500;
	transition: color 0.3s ease;
}

.el-step__description {
	font-size: 14px;
	color: #718096;
}

// 响应式布局优化
@media (max-width: 1600px) {
	.project-home {
		max-width: 95%;
	}
}

@media (max-width: 1200px) {
	.review-container {
		grid-template-columns: 1fr;
	}
	
	.preview-content {
		height: 500px;
	}
}

@media (max-width: 768px) {
	.home-container {
		padding: 20px;
	}
	
	.step-panel {
		padding: 20px;
	}
	
	.preview-content {
		height: 400px;
	}
	
	.welcome-section {
		.welcome-title {
			font-size: 28px;
		}
		
		.welcome-desc {
			font-size: 14px;
		}
	}
}

.file-info-content {
	display: flex;
	align-items: center;
	gap: 12px;
	padding: 20px;
	background: #f8fafd;
	border-radius: 8px;
	
	.file-icon {
		font-size: 24px;
		color: #409EFF;
	}
	
	.file-name {
		flex: 1;
		font-size: 14px;
		color: #606266;
	}
}

// 光泽动画
@keyframes shine {
	0% {
		transform: translateX(-100%);
	}
	20%, 100% {
		transform: translateX(100%);
	}
}

.basic-info-form {
	background: #f8fafd;
	border-radius: 12px;
	padding: 30px;
	border: 2px solid #e2e8f0;
	transition: all 0.3s ease;

	.el-form-item {
		margin-bottom: 20px;
	}

	.el-radio-group {
		width: 100%;
		display: flex;
		justify-content: flex-start;
		gap: 30px;
	}

	&:hover {
		border-color: #2d5af7;
		background: #f5f8ff;
	}
}

:deep(.el-radio) {
	margin-right: 0;
	padding: 4px 0;
}
</style>
