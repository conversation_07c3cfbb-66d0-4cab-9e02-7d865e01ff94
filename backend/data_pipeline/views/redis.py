# 数据存入redis
from rest_framework.views import APIView
from rest_framework.response import Response
from django.core.cache import cache


class RedisView(APIView):
    def post(self, request):
        # 获取请求数据
        data = request.data
        # 将数据存入redis
        cache.set('test', data)
        return Response({'message': '数据存入redis成功'})

    def get(self, request):
        # 从redis中获取数据
        data = cache.get('test')
        return Response({'message': '数据取出redis成功', 'data': data})

    def delete(self, request):
        # 从redis中删除数据
        cache.delete('test')
        return Response({'message': '数据删除redis成功'})

    def put(self, request):
        # 更新redis中的数据
        cache.set('test', request.data)
        return Response({'message': '数据更新redis成功'})
        
