# 傅立叶分析功能说明

## 功能概述

本模块实现了对时间序列信号数据的傅立叶变换分析功能，可以将时域信号转换为频域表示，便于分析信号的频率特性。**现在支持完整的频率谱计算，包括正频率、零频率和负频率部分。**

## 文件结构

- `calculation.py`: 包含 `FourierAnalyzer` 类，实现傅立叶变换分析
- `influxdb.py`: 修改了 `get_data_statistics` 方法，集成傅立叶分析功能
- `test_fourier.py`: 测试脚本，验证傅立叶分析功能
- `DataVisualization.vue`: 前端组件，显示傅立叶变换结果

## 使用方法

### 1. 后端API调用

通过 `influxdb.py` 中的 `get_data_statistics` 方法进行傅立叶分析：

```python
# POST 请求到 /api/data_pipeline/file/influxdb/getStatistics/
{
    "calculated_method": "FFT",
    "cache_id": "your_cache_id"
}
```

### 2. 前端调用

在前端 `DataVisualization.vue` 中，选择计算方法后会自动调用分析功能：

```javascript
const handleCalculatedMethodChange = async (value: string) => {
    const data = {
        "calculated_method": value,
        "cache_id": cache_id.value
    }
    // 发送请求并显示结果
}
```

### 3. 数据结构

#### 输入数据格式
```json
[
    {
        "name": "信号名称",
        "data": [
            {"time": "2025-03-16 11:20:00.000", "value": 75.29},
            {"time": "2025-03-16 11:20:01.000", "value": 0},
            // ... 更多数据点
        ]
    }
]
```

#### 输出数据格式（完整频率谱）
```json
[
    {
        "name": "信号名称 - FFT",
        "data": [
            {"time": "-0.500000", "value": 376.47, "phase": 0.1234},
            {"time": "-0.400000", "value": 0.00, "phase": 1.5678},
            {"time": "0.000000", "value": 752.94, "phase": 0.0000},
            {"time": "0.100000", "value": 0.00, "phase": -1.5678},
            {"time": "0.500000", "value": 376.47, "phase": -0.1234}
            // ... 完整频率域数据点（负频率 -> 零频率 -> 正频率）
        ]
    }
]
```

## 功能特性

### 1. 完整频率谱计算
- **负频率部分**: 包含所有负频率成分
- **零频率部分**: 信号的直流分量
- **正频率部分**: 包含所有正频率成分
- **频率重排序**: 按照 负频率 → 零频率 → 正频率 的顺序排列

### 2. 自动采样率计算
- 自动分析时间序列的采样间隔
- 处理不均匀采样时间
- 支持毫秒级时间精度

### 3. 频域分析
- 计算幅度谱（magnitude spectrum）
- 计算相位谱（phase spectrum）
- 提供完整的复数域信息

### 4. 数据兼容性
- 保持与原始数据相同的结构格式
- 支持多个信号同时分析
- 错误处理和异常恢复

### 5. 前端可视化增强
- 使用 ECharts 显示完整频域结果
- 支持缩放和交互
- 显示频率-幅度关系图
- **零频率标记线**: 红色虚线标识零频率位置
- **统计信息面板**: 显示频率分布统计

## 频率谱特性

### 频率排列
傅立叶变换的结果按照以下顺序排列：
1. **负频率部分**: 从最低负频率到接近零
2. **零频率**: 信号的直流分量
3. **正频率部分**: 从接近零到最高正频率

### 对称性
对于实信号，频域具有共轭对称性：
- 负频率的幅度 = 正频率的幅度
- 负频率的相位 = -正频率的相位

### 应用场景
- **信号完整性分析**: 检查信号的频率成分
- **噪声分析**: 识别噪声的频率特征
- **滤波器设计**: 了解信号的频域特性
- **系统响应分析**: 分析系统的频率响应

## 测试

运行测试脚本验证功能：

```bash
cd metisgrid/backend/data_pipeline/views/
python test_fourier.py
```

测试输出将显示：
- 负频率、零频率、正频率的点数统计
- 各频率范围的具体数值
- 前10个和后10个数据点的详细信息

## 依赖包

确保安装以下Python包：
```bash
pip install numpy scipy pandas
```

## 扩展功能

可以轻松扩展支持其他分析方法：
- 短时傅里叶变换 (STFT)
- 小波变换 (WT)
- 希尔伯特-黄变换 (HHT)

只需在 `FourierAnalyzer` 类中添加相应的方法，并在 `influxdb.py` 中添加对应的处理逻辑即可。

## 技术细节

### 频率重排序算法
```python
# 重新排列频率：负频率 -> 零频率 -> 正频率
n = len(frequencies)
mid = n // 2
reordered_freqs = np.concatenate([frequencies[mid:], frequencies[:mid]])
```

### 采样率计算
```python
# 计算平均采样率
avg_interval = np.mean(time_diffs) / len(time_diffs)
sampling_rate = 1.0 / avg_interval if avg_interval > 0 else 1.0
```

### 频率分辨率
频率分辨率 = 采样率 / 数据点数
更高的数据点数提供更好的频率分辨率。 