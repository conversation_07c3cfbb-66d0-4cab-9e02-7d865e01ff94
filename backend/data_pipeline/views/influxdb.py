from rest_framework.viewsets import ViewSet
from rest_framework.response import Response
from rest_framework import status
from ..utils.influx_client import InfluxDBManager
from ..utils.calculation import FourierAnalyzer, STFTAnalyzer, WaveletAnalyzer, HilbertHuangAnalyzer
# from .parse import startParse
from ..models import DataPipelineProject
import os
import shutil
from django.core.cache import cache
import json
from rest_framework.decorators import action
import uuid


class InfluxDBQueryViewSet(ViewSet):
    """InfluxDB 数据查询（POST）"""

    @action(methods=['POST'], detail=False)
    def influxdb_query_data(self, request, *args, **kwargs):
        try:
            query_type = request.data.get('type', 'data')
            project_name = request.data.get('project_name')
            project_type = request.data.get('project_type')
            start_time = request.data.get('start_time')
            end_time = request.data.get('end_time')
            binfile_Postgresql_id = request.data.get('binfile_Postgresql_id')
            binfile_Minio_version_id = request.data.get('binfile_Minio_version_id')
            influx_ubique_tagId = request.data.get('influx_ubique_tagId')
            
            if query_type == 'info':
                influx_manager = InfluxDBManager()
                project_info = influx_manager.get_project_info(project_name, project_type, start_time, end_time, binfile_Postgresql_id, binfile_Minio_version_id)
                influx_manager.close()
                return Response({"code": 2000, "msg": "查询成功", "data": project_info})
            else:
                # 检查是否有批量信号查询
                signals = request.data.get('signals')
                
                if signals:
                    # 批量查询多个信号
                    influx_manager = InfluxDBManager()
                    all_results = []
                    
                    for signal in signals:
                        signal_name = signal.get('signal_name')
                        node_name = signal.get('node_name')
                        node_type = signal.get('node_type')
                        
                        queryInfluxResult = influx_manager.query_signal_data(
                            project_name, project_type, start_time, end_time, signal_name,
                            node_name, node_type, binfile_Postgresql_id, binfile_Minio_version_id, influx_ubique_tagId
                        )
                        
                        all_results.append({
                            'name': f"{node_type} | {node_name} | {signal_name}",
                            'data': queryInfluxResult
                        })
                    
                    influx_manager.close()
                    
                    # 生成唯一ID
                    cache_id = str(uuid.uuid4())[:5]
                    cache.set(cache_id, all_results, timeout=600)
                    
                    return Response({
                        "code": 2000, 
                        "msg": "批量查询成功", 
                        "data": all_results,
                        "cache_id": cache_id
                    })
        except Exception as e:
            return Response({"code": 4000, "msg": f"查询失败: {str(e)}", "data": None}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(methods=['POST'], detail=False)
    def get_data_statistics(self, request, *args, **kwargs):
        try:
            cache_id = request.data.get('cache_id')
            calculated_method = request.data.get('calculated_method', 'FFT')
            
            cache_data = cache.get(cache_id)
            if not cache_data:
                return Response({"code": 4000, "msg": "缓存数据不存在", "data": None}, status=status.HTTP_404_NOT_FOUND)
            
            # 分析方法映射
            analyzer_mapping = {
                'FFT': (FourierAnalyzer, "傅立叶分析"),
                'STFT': (STFTAnalyzer, "短时傅里叶变换"),
                'WT': (WaveletAnalyzer, "小波变换"),
                'HHT': (HilbertHuangAnalyzer, "希尔伯特-黄变换")
            }
            
            if calculated_method in analyzer_mapping:
                analyzer_class, method_name = analyzer_mapping[calculated_method]
                analyzer = analyzer_class()
                analyzed_data = analyzer.analyze_multiple_signals(cache_data)
                msg = f"{method_name}分析完成" if analyzed_data else f"{method_name}分析失败"
            else:
                analyzed_data = []
                msg = "暂不支持该分析方法"
            
            return Response({
                "code": 2000, 
                "msg": msg, 
                "data": analyzed_data,
                "method": calculated_method
            })
            
        except Exception as e:
            return Response({
                "code": 4000, 
                "msg": f"数据分析失败: {str(e)}", 
                "data": None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
