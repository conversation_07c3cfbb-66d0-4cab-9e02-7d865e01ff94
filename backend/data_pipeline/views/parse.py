#!/usr/bin/env python3
"""
EP2002 Binary Log File Parser

This script parses EP2002 binary log files (.bin) and extracts the file header,
record headers, and signal data based on the EP2002LogPacketLayout.xml configuration.

Usage:
    python log_file_parser.py <bin_file> [xml_config_file]
    python log_file_parser.py --dir <directory> --xml <xml_file> --output <output_dir>
"""

import os
import sys
import struct
import datetime
import xml.etree.ElementTree as ET
from collections import namedtuple, defaultdict
import argparse
import json
import glob
import csv
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from ..models import DataPipelineProject
from django.conf import settings
import billiard as mp
import asyncio
import random
from django.http import StreamingHttpResponse
from asyncio import Queue
import json
import subprocess
import json
import tempfile

# Constants from the C# code
EP2002_LOG_FILE_ID = 0xDEAD
EP2002_LOG_PACKET_SIZE = 2048
EP2002_SECONDS_PER_SAMPLE = 2
EP2002_MILLISECONDS_PER_SAMPLE = EP2002_SECONDS_PER_SAMPLE * 1000

# Define structures for the file header and record header
FileHeader = namedtuple('FileHeader', [
    'block_id', 'file_flags', 'sw_version_major', 'sw_version_minor',
    'sw_version_stroke', 'timestamp', 'sequence', 'data_size',
    'dallas_id', 'session_id'
])

RecordHeader = namedtuple('RecordHeader', [
    'record_id', 'packet_version_major', 'packet_version_minor',
    'sample_cycles', 'session_sequence', 'timestamp'
])

# Signal definition from XML
Signal = namedtuple('Signal', [
    'name', 'message_type', 'node_types', 'node_count', 'bits_per_item',
    'storage_period', 'byte_offset', 'bit_offset', 'scaling', 'unit',
    'element_count'
])

def epoch_to_datetime(seconds):
    """Convert UNIX/Epoch time to a datetime object."""
    return datetime.datetime(1970, 1, 1) + datetime.timedelta(seconds=seconds)

def parse_xml_config(xml_file):
    """Parse the EP2002LogPacketLayout.xml file to get signal definitions."""
    print('执行 parse_xml_config()')
    signals = []
    
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # Handle namespace in the XML
        ns = {'df': 'http://www.Knorr-Bremse.com/EP2002DataLoggerFileFormat'}
        
        # Get packet version
        packet_version = root.find('.//df:PacketLayout/df:Version', ns).text
        
        # Get all data items
        for data_item in root.findall('.//df:PacketLayout/df:DataItem', ns):
            # Skip placeholders and commented out signals
            name = data_item.find('df:Name', ns).text
            if name.startswith('#'):
                continue
            
            # Get node types
            node_types = []
            for node_type in data_item.findall('df:NodeTypes/df:NodeType', ns):
                node_types.append(node_type.text)
            
            # Get message type
            message_type_elem = data_item.find('df:MessageType', ns)
            message_type = message_type_elem.text if message_type_elem is not None else ""
            
            # Get other properties
            node_count = int(data_item.find('df:NodeCount', ns).text)
            bits_per_item = int(data_item.find('df:BitsPerItem', ns).text)
            storage_period = int(data_item.find('df:StoragePeriod', ns).text)
            byte_offset = int(data_item.find('df:ByteOffset', ns).text)
            
            bit_offset_elem = data_item.find('df:BitOffset', ns)
            bit_offset = int(bit_offset_elem.text) if bit_offset_elem is not None else 0
            
            scaling_elem = data_item.find('df:Scaling', ns)
            scaling = float(scaling_elem.text) if scaling_elem is not None else 1.0
            
            unit_elem = data_item.find('df:Unit', ns)
            unit = unit_elem.text if unit_elem is not None else ""
            
            element_count_elem = data_item.find('df:ElementCount', ns)
            element_count = int(element_count_elem.text) if element_count_elem is not None else 1
            
            # Calculate overall bit offset
            overall_bit_offset = byte_offset * 8 + bit_offset
            
            for node_type in node_types:
                signal = Signal(
                    name=name,
                    message_type=message_type,
                    node_types=[node_type],
                    node_count=node_count,
                    bits_per_item=bits_per_item,
                    storage_period=storage_period,
                    byte_offset=byte_offset,
                    bit_offset=bit_offset,
                    scaling=scaling,
                    unit=unit,
                    element_count=element_count
                )
                signals.append(signal)
        
        return signals, packet_version
    
    except Exception as e:
        print(f"Error parsing XML config: {e}")
        return [], "unknown"

def calculate_packet_count(bin_file):
    """计算文件中的数据包总数"""
    import os
    file_size = os.path.getsize(bin_file)
    if file_size < 64 + EP2002_LOG_PACKET_SIZE:
        return 0
    actual_data_size = file_size - 64  # 减去文件头
    packet_count = actual_data_size // EP2002_LOG_PACKET_SIZE
    return packet_count

def process_file_segment_with_c_extension(bin_file, start_offset, packet_count, signals_list, verbose):
    """处理文件片段 - 调用C扩展处理指定偏移量和数量的数据包，增加内存监控"""
    import time
    import psutil
    import os
    
    # 获取当前进程信息
    process = psutil.Process(os.getpid())
    
    # 监控开始时的内存
    start_memory = process.memory_info()
    start_rss = start_memory.rss / 1024 / 1024  # 转换为MB
    start_vms = start_memory.vms / 1024 / 1024  # 转换为MB
    
    process_start = time.time()
    
    print(f'子进程开始: PID={os.getpid()}, offset={start_offset}, count={packet_count}')
    print(f'  开始内存: RSS={start_rss:.2f}MB, VMS={start_vms:.2f}MB')
    
    try:
        import sys
        import os
        
        # 添加C扩展模块路径
        c_files_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '../c_files')
        if c_files_dir not in sys.path:
            sys.path.insert(0, c_files_dir)
        
        import binfile_parser
        
        # 调用修改后的C扩展函数
        result = binfile_parser.parse_bin_file_segment(bin_file, start_offset, packet_count, signals_list, verbose)
        
        # 监控结束时的内存
        end_memory = process.memory_info()
        end_rss = end_memory.rss / 1024 / 1024  # 转换为MB
        end_vms = end_memory.vms / 1024 / 1024  # 转换为MB
        
        # 计算内存变化
        rss_change = end_rss - start_rss
        vms_change = end_vms - start_vms
        
        process_end = time.time()
        process_time = process_end - process_start
        
        print(f'子进程完成: PID={os.getpid()}, offset={start_offset}')
        print(f'  结束内存: RSS={end_rss:.2f}MB, VMS={end_vms:.2f}MB')
        print(f'  内存变化: RSS={rss_change:+.2f}MB, VMS={vms_change:+.2f}MB')
        print(f'  处理耗时: {process_time:.3f}秒')
        
        return result
        
    except Exception as e:
        # 即使出错也要监控内存
        error_memory = process.memory_info()
        error_rss = error_memory.rss / 1024 / 1024
        error_vms = error_memory.vms / 1024 / 1024
        
        print(f"子进程处理失败: PID={os.getpid()}, 错误: {e}")
        print(f'  错误时内存: RSS={error_rss:.2f}MB, VMS={error_vms:.2f}MB')
        
        return None

def merge_parse_results(results):
    """合并多个解析结果"""
    if not results or len(results) == 0:
        return None
    
    # 过滤掉None结果
    valid_results = [r for r in results if r is not None]
    if not valid_results:
        return None
    
    # 使用第一个结果作为基础
    merged_result = {
        'header': valid_results[0]['header'],  # 文件头都相同，取第一个
        'records': [],
        'signal_data': {}
    }
    
    # 合并所有记录
    for result in valid_results:
        if 'records' in result:
            merged_result['records'].extend(result['records'])
    
    # 合并信号数据
    for result in valid_results:
        if 'signal_data' in result:
            for signal_name, timestamps in result['signal_data'].items():
                if signal_name not in merged_result['signal_data']:
                    merged_result['signal_data'][signal_name] = {}
                merged_result['signal_data'][signal_name].update(timestamps)
    
    print(f"合并完成: 总记录数={len(merged_result['records'])}, 信号数={len(merged_result['signal_data'])}")
    return merged_result

def parse_bin_file(bin_file, signals=None, verbose=True):
    """Parse an EP2002 binary log file using optimized implementations."""
    print('执行 parse_bin_file() - 混合多进程+C扩展版本')
    
    import time
    import os
    import gc
    start_time = time.time()
    
    # 检查文件大小
    file_size = os.path.getsize(bin_file)
    print(f"文件大小: {file_size / 1024:.2f} KB")
    
    # 文件大小限制
    if file_size > 20 * 1024 * 1024:  # 大于20MB
        print("错误: 文件过大，超出处理能力")
        return {"error": "文件过大，请联系管理员"}
    elif file_size > 15 * 1024 * 1024:  # 大于15MB
        print("警告: 大文件处理，内存使用可能很高")
        gc.collect()
    
    try:
        # 转换signals为Python列表格式
        signals_list = None
        if signals:
            signals_list = []
            for signal in signals:
                signal_dict = {
                    "name": signal.name,
                    "message_type": signal.message_type,
                    "node_types": signal.node_types,
                    "node_count": signal.node_count,
                    "bits_per_item": signal.bits_per_item,
                    "storage_period": signal.storage_period,
                    "byte_offset": signal.byte_offset,
                    "bit_offset": signal.bit_offset,
                    "scaling": signal.scaling,
                    "unit": signal.unit,
                    "element_count": signal.element_count
                }
                signals_list.append(signal_dict)
        
        print(f"使用混合方案解析，信号数量: {len(signals_list) if signals_list else 0}")
        
        # 计算数据包总数和分片策略
        total_packet_count = calculate_packet_count(bin_file)
        if total_packet_count <= 0:
            print("错误: 文件太小或格式错误")
            return {"error": "文件太小或格式错误"}
        
        print(f"总数据包数量: {total_packet_count}")
        
        # 动态计算最优进程数
        def calculate_optimal_process_count(file_size_bytes, available_memory_mb):
            """根据文件大小和可用内存动态调整进程数"""
            # 每个进程预估内存使用 300MB (基于监控数据)
            estimated_memory_per_process = 300  # MB
            max_processes_by_memory = int(available_memory_mb // estimated_memory_per_process)
            
            # 根据文件大小计算合理的进程数
            file_size_mb = file_size_bytes / 1024 / 1024
            mb_per_process = 4  # 每个进程处理2MB数据比较合理
            max_processes_by_file = max(1, int(file_size_mb // mb_per_process))
            
            # 取较小值，并限制最大进程数
            optimal_processes = min(max_processes_by_memory, max_processes_by_file, 4)
            return max(1, optimal_processes)
        
        # 获取系统可用内存
        import psutil
        available_memory = psutil.virtual_memory().available / 1024 / 1024  # MB
        usable_memory = available_memory * 0.7  # 只使用70%可用内存，留出缓冲
        
        # 计算优化后的进程数
        num_cores = calculate_optimal_process_count(file_size, usable_memory)
        print(f"系统可用内存: {available_memory:.2f} MB, 准备使用70%内存: {usable_memory} MB")
        print(f"文件大小: {file_size/1024/1024:.2f} MB")
        print(f"可用内存: {available_memory:.2f} MB, 使用限制: {usable_memory:.2f} MB") 
        print(f"优化后进程数: {num_cores} (基于内存和文件大小计算)")
        
        if file_size > 50 * 1024 * 1024:  # 大于50MB
            print("警告: 大文件处理，内存使用可能很高")
        
        pool_start_time = time.time()
        pool = mp.Pool(num_cores)
        
        # 计算每个进程处理的数据包数量
        packets_per_process = max(1, total_packet_count // num_cores)
        
        async_results = []
        for i in range(num_cores):
            start_packet = i * packets_per_process
            
            # 最后一个进程处理剩余的所有数据包
            if i == num_cores - 1:
                packet_count = total_packet_count - start_packet
            else:
                packet_count = packets_per_process
            
            # 计算文件偏移量（64字节文件头 + 数据包偏移）
            start_offset = 64 + start_packet * EP2002_LOG_PACKET_SIZE
            
            print(f"进程{i}: 起始数据包={start_packet}, 数量={packet_count}, 偏移量={start_offset}")
            
            async_result = pool.apply_async(
                process_file_segment_with_c_extension,
                args=(bin_file, start_offset, packet_count, signals_list, 1 if verbose else 0)
            )
            async_results.append(async_result)
        
        # 关闭进程池
        pool.close()
        pool.join()
        
        pool_end_time = time.time()
        print(f"所有进程完成，耗时: {pool_end_time - pool_start_time:.2f}秒")
        
        # 收集结果
        results = []
        for i, async_result in enumerate(async_results):
            try:
                result = async_result.get()
                if result:
                    results.append(result)
                    print(f"进程{i}结果: 记录数={len(result.get('records', []))}")
                else:
                    print(f"进程{i}返回空结果")
            except Exception as e:
                print(f"获取进程{i}结果失败: {e}")
        
        # 合并结果
        if not results:
            return {"error": "所有进程都处理失败"}
        
        final_result = merge_parse_results(results)
        
        # 转换结果格式以兼容现有代码
        if final_result and 'header' in final_result:
            # 重构dallas_id
            header = final_result['header']
            if 'dallas_id_high' in header and 'dallas_id_low' in header:
                dallas_id = (header['dallas_id_high'] << 32) | header['dallas_id_low']
                header['dallas_id'] = dallas_id
            
            # 包装header为对象（如果需要）
            class DotDict:
                def __init__(self, data):
                    for key, value in data.items():
                        if isinstance(value, dict):
                            value = DotDict(value)
                        setattr(self, key, value)
            
            final_result['header'] = DotDict(final_result['header'])
            
            # 转换records格式
            if 'records' in final_result:
                for record in final_result['records']:
                    if 'header' in record:
                        record['header'] = DotDict(record['header'])
                        record['data'] = None  # 保持兼容性
        
        end_time = time.time()
        print(f'混合方案解析完成，总耗时: {end_time - start_time:.2f}秒')
        return final_result
        
    except Exception as e:
        print(f"混合方案执行失败: {e}")
        gc.collect()
        return {"error": f"处理失败: {str(e)}"}

def parse_node_types(xml_file):
    """解析 EP2002NodeTypes.xml 文件以获取节点映射关系"""
    print('执行 parse_node_types()')
    node_mappings = {}  # {node_type: {index: node_name}}
    
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # 处理 XML 中的命名空间
        ns = {'nt': 'http://www.Knorr-Bremse.com/EP2002NodeTypes'}
        
        # 获取所有节点类型
        for node_type in root.findall('.//nt:NodeType', ns):
            type_name = node_type.find('nt:Name', ns).text
            node_mappings[type_name] = {}
            
            # 获取该类型下的所有节点
            for node in node_type.findall('nt:Node', ns):
                node_name = node.find('nt:Name', ns).text
                node_index = int(node.find('nt:Index', ns).text)
                node_mappings[type_name][node_index] = node_name
    
    except Exception as e:
        print(f"解析节点类型 XML 配置时出错: {e}")
        return {}
    
    return node_mappings

def reconstruct_dallas_id(header):
    if hasattr(header, 'dallas_id_high') and hasattr(header, 'dallas_id_low'):
        # 重构64位整数
        dallas_id = (int(header.dallas_id_high) << 32) | int(header.dallas_id_low)
        header.dallas_id = dallas_id
    return header