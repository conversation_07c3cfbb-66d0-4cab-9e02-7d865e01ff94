from dvadmin.utils.viewset import CustomModelViewSet
from dvadmin.utils.serializers import CustomModelSerializer
from data_pipeline.models import DataPipelineProject
from dvadmin.utils.filters import CoreModelFilterBankend
from rest_framework.permissions import IsAuthenticated


# ================================================= #
# ************** 序列化器 ************** #
# ================================================= #

class DataPipelineProjectSerializer(CustomModelSerializer):
    """
    数据管道项目-序列化器
    """
    class Meta:
        model = DataPipelineProject
        fields = "__all__"
        read_only_fields = ["id"]


# ================================================= #
# ************** 视图集 ************** #
# ================================================= #

class DataPipelineProjectModelViewSet(CustomModelViewSet):
    """
    数据管道项目管理接口
    list:查询
    create:新增
    update:修改
    retrieve:单例
    destroy:删除
    """
    queryset = DataPipelineProject.objects.all()
    serializer_class = DataPipelineProjectSerializer
    permission_classes = [IsAuthenticated]  # 添加认证要求
    # 移除数据权限过滤器，只保留核心模型过滤器
    extra_filter_class = [CoreModelFilterBankend]
    filter_fields = ['project_number', 'project_name', 'customer_name', 'template_type']
    search_fields = ['project_number', 'project_name', 'customer_name'] 