SSE连接建立，tagId: H7pgd
成功引入C拓展: binfile_parser
成功引入C拓展: binfile_parser
成功引入C拓展: binfile_parser
成功引入C拓展: binfile_parser
独立进程2解析完成: 记录数=50, 信号数=226
独立进程0解析完成: 记录数=50, 信号数=226
独立进程3解析完成: 记录数=52, 信号数=226
独立进程1解析完成: 记录数=50, 信号数=226
主循环结束: 完成进程数 4/4
所有任务完成，开始清理进程池 (完成进程数: 4/4)
等待进程退出: 剩余 1 个进程活跃
所有进程已正常完成
成功引入C拓展: binfile_parser
成功引入C拓展: binfile_parser
成功引入C拓展: binfile_parser
成功引入C拓展: binfile_parser
独立进程1解析完成: 记录数=50, 信号数=226
独立进程0解析完成: 记录数=50, 信号数=226
独立进程2解析完成: 记录数=50, 信号数=226
独立进程3解析完成: 记录数=52, 信号数=226
SSE Influx connection cancelled
开始清理tagId H7pgd 的所有相关进程
清理活跃进程池 (tagId: H7pgd)
开始强制清理进程池 (tagId: H7pgd)
主循环结束: 完成进程数 4/4
所有任务完成，开始清理进程池 (完成进程数: 4/4)
所有进程已正常完成
进程清理完成: 杀死 0 个进程, 剩余 0 个进程
进程池清理完成: 杀死 0 个进程, 剩余 0 个进程
tagId H7pgd 的所有进程清理完成