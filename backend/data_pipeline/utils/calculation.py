import numpy as np
from scipy.fft import fft, fftfreq
from scipy.signal import stft
from scipy import signal
import pywt
import json
from typing import List, Dict, Any
import pandas as pd
from datetime import datetime

class FourierAnalyzer:
    """
    傅立叶分析类，用于对时间序列数据进行傅立叶变换
    """
    
    def __init__(self):
        self.sampling_rate = 1.0  # 默认采样率
        
    def analyze_signal(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        对单个信号进行傅立叶变换分析
        
        Args:
            signal_data: 信号数据，格式为 {"name": "信号名", "data": [{"time": "时间", "value": 数值}]}
        
        Returns:
            分析结果，保持相同的数据结构
        """
        signal_name = signal_data["name"]
        time_series = signal_data["data"]
        
        # 提取时间和数值
        times, values = self._extract_time_series(time_series)
        
        if len(values) < 2:
            print(f"信号 {signal_name} 数据点不足，跳过处理")
            return {
                "name": f"{signal_name} - FFT",
                "data": []
            }
        
        # 计算采样率
        sampling_rate = self._calculate_sampling_rate(times)
        
        # 执行傅立叶变换
        fft_result = fft(values)
        frequencies = fftfreq(len(values), 1/sampling_rate)
        
        # 计算幅度谱和相位谱
        magnitude_spectrum = np.abs(fft_result)
        phase_spectrum = np.angle(fft_result)
        
        # 计算完整的频率范围（包括正负频率）
        # 重新排列频率，使负频率在前，正频率在后
        n = len(frequencies)
        mid = n // 2
        
        # 重新排列频率：负频率 -> 零频率 -> 正频率
        reordered_freqs = np.concatenate([frequencies[mid:], frequencies[:mid]])
        reordered_magnitudes = np.concatenate([magnitude_spectrum[mid:], magnitude_spectrum[:mid]])
        reordered_phases = np.concatenate([phase_spectrum[mid:], phase_spectrum[:mid]])
        
        # 创建变换后的数据，保持与原始数据相似的结构
        transformed_data = []
        for i in range(len(reordered_freqs)):
            transformed_data.append({
                "time": f"{reordered_freqs[i]:.6f}",  # 使用频率作为时间轴
                "value": float(reordered_magnitudes[i]),  # 使用幅度作为值
                "phase": float(reordered_phases[i])  # 添加相位信息
            })
        
        # 按频率排序，确保数据按正确的顺序排列
        transformed_data.sort(key=lambda x: float(x["time"]))
        
        return {
            "name": f"{signal_name} - FFT",
            "data": transformed_data
        }
    
    def analyze_multiple_signals(self, signals_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对多个信号进行傅立叶变换分析
        
        Args:
            signals_data: 信号数据列表，格式为 [{"name": "信号名", "data": [{"time": "时间", "value": 数值}]}]
        
        Returns:
            分析结果列表，保持相同的数据结构
        """
        result = []
        
        for signal in signals_data:
            try:
                analyzed_signal = self.analyze_signal(signal)
                result.append(analyzed_signal)
                print(f"信号 {signal['name']} 傅立叶变换完成")
            except Exception as e:
                print(f"信号 {signal['name']} 处理失败: {str(e)}")
                # 返回空数据，保持结构一致
                result.append({
                    "name": f"{signal['name']} - FFT",
                    "data": []
                })
        
        return result
    
    def _extract_time_series(self, time_series: List[Dict[str, Any]]) -> tuple:
        """
        从时间序列数据中提取时间和数值
        
        Args:
            time_series: 时间序列数据
            
        Returns:
            (times, values): 时间列表和数值列表
        """
        times = []
        values = []
        
        for point in time_series:
            time_str = point["time"]
            try:
                # 解析时间格式
                if '.' in time_str:
                    dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                else:
                    dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                times.append(dt)
                values.append(float(point["value"]))
            except ValueError as e:
                print(f"时间解析错误: {time_str}, 错误: {e}")
                continue
        
        return times, values
    
    def _calculate_sampling_rate(self, times: List[datetime]) -> float:
        """
        计算采样率
        
        Args:
            times: 时间列表
            
        Returns:
            采样率 (Hz)
        """
        if len(times) < 2:
            return 1.0
        
        # 计算时间间隔
        time_diffs = []
        for i in range(1, len(times)):
            diff = (times[i] - times[0]).total_seconds()
            time_diffs.append(diff)
        
        # 检查时间间隔是否均匀
        if len(set(time_diffs)) > 1:
            print("时间间隔不均匀，使用索引作为时间轴")
            return 1.0
        
        # 计算平均采样率
        if len(time_diffs) > 0:
            avg_interval = np.mean(time_diffs) / len(time_diffs)
            sampling_rate = 1.0 / avg_interval if avg_interval > 0 else 1.0
        else:
            sampling_rate = 1.0
        
        return sampling_rate


class STFTAnalyzer:
    """
    短时傅里叶变换分析类，用于对时间序列数据进行时频分析
    """
    
    def __init__(self):
        self.sampling_rate = 1.0
        self.nperseg = 256  # 窗口长度
        self.noverlap = 128  # 重叠长度
        
    def analyze_signal(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        对单个信号进行短时傅里叶变换分析
        
        Args:
            signal_data: 信号数据，格式为 {"name": "信号名", "data": [{"time": "时间", "value": 数值}]}
        
        Returns:
            分析结果，保持相同的数据结构
        """
        signal_name = signal_data["name"]
        time_series = signal_data["data"]
        
        # 提取时间和数值
        times, values = self._extract_time_series(time_series)
        
        if len(values) < self.nperseg:
            print(f"信号 {signal_name} 数据点不足，跳过处理")
            return {
                "name": f"{signal_name} - STFT",
                "data": []
            }
        
        # 计算采样率
        sampling_rate = self._calculate_sampling_rate(times)
        
        # 执行短时傅里叶变换
        f, t, Zxx = stft(values, sampling_rate, nperseg=self.nperseg, noverlap=self.noverlap)
        
        # 计算幅度谱
        magnitude_spectrum = np.abs(Zxx)
        
        # 创建变换后的数据
        transformed_data = []
        for i, freq in enumerate(f):
            for j, time_idx in enumerate(t):
                transformed_data.append({
                    "time": f"{time_idx:.6f}",  # 时间
                    "value": float(magnitude_spectrum[i, j]),  # 幅度
                    "frequency": float(freq)  # 频率
                })
        
        # 按时间排序，确保数据按正确的顺序排列
        transformed_data.sort(key=lambda x: float(x["time"]))
        
        return {
            "name": f"{signal_name} - STFT",
            "data": transformed_data
        }
    
    def analyze_multiple_signals(self, signals_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对多个信号进行短时傅里叶变换分析
        
        Args:
            signals_data: 信号数据列表
        
        Returns:
            分析结果列表
        """
        result = []
        
        for signal in signals_data:
            try:
                analyzed_signal = self.analyze_signal(signal)
                result.append(analyzed_signal)
                print(f"信号 {signal['name']} 短时傅里叶变换完成")
            except Exception as e:
                print(f"信号 {signal['name']} 处理失败: {str(e)}")
                result.append({
                    "name": f"{signal['name']} - STFT",
                    "data": []
                })
        
        return result
    
    def _extract_time_series(self, time_series: List[Dict[str, Any]]) -> tuple:
        """从时间序列数据中提取时间和数值"""
        times = []
        values = []
        
        for point in time_series:
            time_str = point["time"]
            try:
                if '.' in time_str:
                    dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                else:
                    dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                times.append(dt)
                values.append(float(point["value"]))
            except ValueError as e:
                print(f"时间解析错误: {time_str}, 错误: {e}")
                continue
        
        return times, values
    
    def _calculate_sampling_rate(self, times: List[datetime]) -> float:
        """计算采样率"""
        if len(times) < 2:
            return 1.0
        
        time_diffs = []
        for i in range(1, len(times)):
            diff = (times[i] - times[0]).total_seconds()
            time_diffs.append(diff)
        
        if len(set(time_diffs)) > 1:
            print("时间间隔不均匀，使用索引作为时间轴")
            return 1.0
        
        if len(time_diffs) > 0:
            avg_interval = np.mean(time_diffs) / len(time_diffs)
            sampling_rate = 1.0 / avg_interval if avg_interval > 0 else 1.0
        else:
            sampling_rate = 1.0
        
        return sampling_rate


class WaveletAnalyzer:
    """
    小波变换分析类，用于对时间序列数据进行小波分析
    """
    
    def __init__(self):
        self.sampling_rate = 1.0
        self.wavelet = 'cmor1.5-1.0'  # 使用复Morlet小波，更适合连续小波变换
        self.scales = np.arange(1, 64)  # 减少尺度范围以提高性能
        
    def analyze_signal(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        对单个信号进行小波变换分析
        
        Args:
            signal_data: 信号数据，格式为 {"name": "信号名", "data": [{"time": "时间", "value": 数值}]}
        
        Returns:
            分析结果，按尺度分组的数据结构
        """
        signal_name = signal_data["name"]
        time_series = signal_data["data"]
        
        # 提取时间和数值
        times, values = self._extract_time_series(time_series)
        
        if len(values) < 2:
            print(f"信号 {signal_name} 数据点不足，跳过处理")
            return {
                "name": f"{signal_name} - WT",
                "data": []
            }
        
        # 计算采样率
        sampling_rate = self._calculate_sampling_rate(times)
        
        try:
            # 执行连续小波变换
            coefficients, frequencies = pywt.cwt(values, self.scales, self.wavelet, sampling_period=1/sampling_rate)
            
            # 计算小波功率谱
            power_spectrum = np.abs(coefficients) ** 2
            
            # 按尺度分组创建变换后的数据
            scale_groups = {}
            for i, scale in enumerate(self.scales):
                scale_key = f"scale_{scale:.2f}"
                scale_groups[scale_key] = {
                    "name": f"{signal_name} - Scale {scale:.2f}",
                    "scale": float(scale),
                    "frequency": float(frequencies[i]) if i < len(frequencies) else 0.0,
                    "data": []
                }
                
                # 为当前尺度添加所有时间点的数据
                for j, time_idx in enumerate(range(len(values))):
                    scale_groups[scale_key]["data"].append({
                        "time": f"{time_idx:.6f}",  # 时间
                        "value": float(power_spectrum[i, j]),  # 功率
                        "scale": float(scale),  # 尺度
                        "frequency": float(frequencies[i]) if i < len(frequencies) else 0.0  # 频率
                    })
            
            # 返回按尺度分组的数据
            return {
                "name": f"{signal_name} - WT",
                "scale_groups": scale_groups,
                "data": []  # 保持向后兼容性
            }
            
        except Exception as e:
            print(f"小波变换失败: {str(e)}")
            # 如果连续小波变换失败，尝试使用离散小波变换
            try:
                return self._fallback_dwt_analysis(values, signal_name)
            except Exception as e2:
                print(f"离散小波变换也失败: {str(e2)}")
                return {
                    "name": f"{signal_name} - WT",
                    "data": []
                }
    
    def _fallback_dwt_analysis(self, values: List[float], signal_name: str) -> Dict[str, Any]:
        """
        离散小波变换作为备选方案
        """
        # 使用离散小波变换
        coeffs = pywt.wavedec(values, 'db4', level=3)
        
        # 重构各层系数
        transformed_data = []
        for i, coeff in enumerate(coeffs):
            for j, value in enumerate(coeff):
                transformed_data.append({
                    "time": f"{j:.6f}",
                    "value": float(abs(value)),
                    "level": i,
                    "coefficient_type": "approximation" if i == 0 else "detail"
                })
        
        return {
            "name": f"{signal_name} - WT (DWT)",
            "data": transformed_data
        }
    
    def analyze_multiple_signals(self, signals_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对多个信号进行小波变换分析
        
        Args:
            signals_data: 信号数据列表
        
        Returns:
            分析结果列表
        """
        result = []
        
        for signal in signals_data:
            try:
                analyzed_signal = self.analyze_signal(signal)
                result.append(analyzed_signal)
                print(f"信号 {signal['name']} 小波变换完成")
            except Exception as e:
                print(f"信号 {signal['name']} 处理失败: {str(e)}")
                result.append({
                    "name": f"{signal['name']} - WT",
                    "data": []
                })
        
        return result
    
    def _extract_time_series(self, time_series: List[Dict[str, Any]]) -> tuple:
        """从时间序列数据中提取时间和数值"""
        times = []
        values = []
        
        for point in time_series:
            time_str = point["time"]
            try:
                if '.' in time_str:
                    dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                else:
                    dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                times.append(dt)
                values.append(float(point["value"]))
            except ValueError as e:
                print(f"时间解析错误: {time_str}, 错误: {e}")
                continue
        
        return times, values
    
    def _calculate_sampling_rate(self, times: List[datetime]) -> float:
        """计算采样率"""
        if len(times) < 2:
            return 1.0
        
        time_diffs = []
        for i in range(1, len(times)):
            diff = (times[i] - times[0]).total_seconds()
            time_diffs.append(diff)
        
        if len(set(time_diffs)) > 1:
            print("时间间隔不均匀，使用索引作为时间轴")
            return 1.0
        
        if len(time_diffs) > 0:
            avg_interval = np.mean(time_diffs) / len(time_diffs)
            sampling_rate = 1.0 / avg_interval if avg_interval > 0 else 1.0
        else:
            sampling_rate = 1.0
        
        return sampling_rate


class HilbertHuangAnalyzer:
    """
    希尔伯特-黄变换分析类，用于对时间序列数据进行经验模态分解和希尔伯特谱分析
    """
    
    def __init__(self):
        self.sampling_rate = 1.0
        
    def analyze_signal(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        对单个信号进行希尔伯特-黄变换分析
        
        Args:
            signal_data: 信号数据，格式为 {"name": "信号名", "data": [{"time": "时间", "value": 数值}]}
        
        Returns:
            分析结果，保持相同的数据结构
        """
        signal_name = signal_data["name"]
        time_series = signal_data["data"]
        
        # 提取时间和数值
        times, values = self._extract_time_series(time_series)
        
        if len(values) < 10:  # HHT需要更多数据点
            print(f"信号 {signal_name} 数据点不足，跳过处理")
            return {
                "name": f"{signal_name} - HHT",
                "data": []
            }
        
        # 计算采样率
        sampling_rate = self._calculate_sampling_rate(times)
        
        # 简化的HHT分析（实际应用中可能需要更复杂的EMD实现）
        # 这里使用希尔伯特变换作为简化版本
        try:
            # 应用希尔伯特变换
            analytic_signal = signal.hilbert(values)
            amplitude_envelope = np.abs(analytic_signal)
            instantaneous_phase = np.unwrap(np.angle(analytic_signal))
            instantaneous_frequency = np.diff(instantaneous_phase) / (2.0 * np.pi) * sampling_rate
            
            # 创建变换后的数据
            transformed_data = []
            for i in range(len(values)):
                data_point = {
                    "time": f"{i/sampling_rate:.6f}",  # 时间
                    "value": float(amplitude_envelope[i]),  # 包络
                    "phase": float(instantaneous_phase[i])  # 相位
                }
                
                # 添加瞬时频率（除了最后一个点）
                if i < len(instantaneous_frequency):
                    data_point["frequency"] = float(instantaneous_frequency[i])
                else:
                    data_point["frequency"] = 0.0
                
                transformed_data.append(data_point)
            
            return {
                "name": f"{signal_name} - HHT",
                "data": transformed_data
            }
            
        except Exception as e:
            print(f"HHT分析失败: {str(e)}")
            return {
                "name": f"{signal_name} - HHT",
                "data": []
            }
    
    def analyze_multiple_signals(self, signals_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        对多个信号进行希尔伯特-黄变换分析
        
        Args:
            signals_data: 信号数据列表
        
        Returns:
            分析结果列表
        """
        result = []
        
        for signal in signals_data:
            try:
                analyzed_signal = self.analyze_signal(signal)
                result.append(analyzed_signal)
                print(f"信号 {signal['name']} 希尔伯特-黄变换完成")
            except Exception as e:
                print(f"信号 {signal['name']} 处理失败: {str(e)}")
                result.append({
                    "name": f"{signal['name']} - HHT",
                    "data": []
                })
        
        return result
    
    def _extract_time_series(self, time_series: List[Dict[str, Any]]) -> tuple:
        """从时间序列数据中提取时间和数值"""
        times = []
        values = []
        
        for point in time_series:
            time_str = point["time"]
            try:
                if '.' in time_str:
                    dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
                else:
                    dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                times.append(dt)
                values.append(float(point["value"]))
            except ValueError as e:
                print(f"时间解析错误: {time_str}, 错误: {e}")
                continue
        
        return times, values
    
    def _calculate_sampling_rate(self, times: List[datetime]) -> float:
        """计算采样率"""
        if len(times) < 2:
            return 1.0
        
        time_diffs = []
        for i in range(1, len(times)):
            diff = (times[i] - times[0]).total_seconds()
            time_diffs.append(diff)
        
        if len(set(time_diffs)) > 1:
            print("时间间隔不均匀，使用索引作为时间轴")
            return 1.0
        
        if len(time_diffs) > 0:
            avg_interval = np.mean(time_diffs) / len(time_diffs)
            sampling_rate = 1.0 / avg_interval if avg_interval > 0 else 1.0
        else:
            sampling_rate = 1.0
        
        return sampling_rate

# 保持向后兼容的函数
def parse_time_series_data(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    对每个信号进行傅立叶变换处理（向后兼容函数）
    
    Args:
        data: 原始数据列表，格式为 [{"name": "信号名", "data": [{"time": "时间", "value": 数值}]}]
    
    Returns:
        处理后的数据列表，保持相同的数据结构
    """
    analyzer = FourierAnalyzer()
    return analyzer.analyze_multiple_signals(data)

def save_transformed_data(transformed_data: List[Dict[str, Any]], output_file: str = "fourier_transformed_data.json"):
    """
    保存变换后的数据到文件
    
    Args:
        transformed_data: 变换后的数据
        output_file: 输出文件名
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(transformed_data, f, indent=2, ensure_ascii=False)
    print(f"变换后的数据已保存到: {output_file}")

def main():
    """
    主函数：读取原始数据并进行傅立叶变换
    """
    # 原始数据（从queryData.py中复制）
    data = [
        {
            "name": "RIO-logical | RIO L1A | PWM 1 input",
            "data": [
                {
                    "time": "2025-03-16 11:20:00.000",
                    "value": 75.2941176470592
                },
                {
                    "time": "2025-03-16 11:20:01.000",
                    "value": 0
                },
                {
                    "time": "2025-03-16 11:20:02.000",
                    "value": 75.2941176470592
                },
                {
                    "time": "2025-03-16 11:20:03.000",
                    "value": 0
                },
                {
                    "time": "2025-03-16 11:20:04.000",
                    "value": 75.2941176470592
                },
                {
                    "time": "2025-03-16 11:20:05.000",
                    "value": 0
                }
            ]
        }
    ]
    
    # 进行傅立叶变换
    print("开始进行傅立叶变换...")
    transformed_data = parse_time_series_data(data)
    
    # 保存结果
    save_transformed_data(transformed_data)
    
    # 打印结果摘要
    print("\n傅立叶变换完成！")
    for signal in transformed_data:
        print(f"信号: {signal['name']}")
        print(f"  频率点数: {len(signal['data'])}")
        if signal['data']:
            print(f"  频率范围: {signal['data'][0]['time']:.4f} - {signal['data'][-1]['time']:.4f} Hz")
            print(f"  最大幅度: {max([d['value'] for d in signal['data']]):.4f}")
        print()

if __name__ == "__main__":
    main() 