# 解析信号/数据有点问题，数据总理很大但差了十几个数据点， 一个是解析的问题
# 多进程 - 每个进程解析+写入influxdb ok. 对于5057kb文件: 原始的for循环influxdb写入耗时144.78s, 现在 解析+写入 耗时49.58s. 待优化: SSE进度条正确格式推到前端 
# SSE推送完成 -- 文件+进程的总体进度, 问题是: 多进程基本执行完的时候 (进程0完成: success) 才得到进度更新, 也就是最后几秒快速从0%变为100% 
# 修改 tag/field 结构, 大幅减少写入influxdb时间
# 部分 如 Brake Mode 是0.2s, 但保存到influx的只有秒s
from influxdb_client import InfluxDBClient, Point, WritePrecision
from influxdb_client.client.write_api import ASYNCHRONOUS, WriteOptions
from data_pipeline.models import DataPipelineFile

from django.conf import settings
import datetime
from datetime import datetime, timezone, timedelta
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time
import os
import random
from django.http import StreamingHttpResponse
from asyncio import Queue
import json
import xml.etree.ElementTree as ET
from collections import namedtuple, defaultdict
import struct
import billiard as mp
import psutil
import gc
import sys
import random
import string

# 配置日志记录
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 创建一个全局队列来存储进度条进度更新
progress_queue = Queue()

# 全局SSE连接状态标志{influx_ubique_tagId}
sse_connection_status = {}

# 全局活跃进程池跟踪
active_process_pools = {}

# 全局清理状态跟踪（防止重复清理）
cleanup_in_progress = set()

# 新增常量定义
EP2002_LOG_FILE_ID = 0xDEAD
EP2002_LOG_PACKET_SIZE = 2048
EP2002_SECONDS_PER_SAMPLE = 2
EP2002_MILLISECONDS_PER_SAMPLE = EP2002_SECONDS_PER_SAMPLE * 1000
binFiles_Global_timeRange = {}
binfile_Postgresql_id_list = []

def force_cleanup_processes(pool, influx_ubique_tagId):
    """强制清理进程池中的所有进程"""
    print(f"开始强制清理进程池 (tagId: {influx_ubique_tagId})")

    try:
        # 立即终止进程池
        pool.terminate()

        # 获取所有子进程PID并强制杀死
        import signal
        import os
        import time
        killed_count = 0

        for process in pool._pool:
            if process.is_alive():
                try:
                    pid = process.pid
                    print(f"强制杀死进程 PID: {pid}")

                    # 使用SIGKILL强制杀死进程
                    os.kill(pid, signal.SIGKILL)
                    killed_count += 1

                except (ProcessLookupError, OSError) as e:
                    print(f"进程 {process.pid} 已经不存在或无法杀死: {e}")
                except Exception as e:
                    print(f"杀死进程 {process.pid} 时出错: {e}")

        # 等待一小段时间让进程真正退出
        time.sleep(0.1)

        # 不调用pool.join()，因为billiard的join可能会有问题
        # 直接检查进程状态
        remaining = 0
        for process in pool._pool:
            if process.is_alive():
                remaining += 1
                try:
                    # 再次尝试杀死顽固进程
                    os.kill(process.pid, signal.SIGKILL)
                    print(f"二次杀死顽固进程 PID: {process.pid}")
                except:
                    pass

        print(f"进程清理完成: 杀死 {killed_count} 个进程, 剩余 {remaining} 个进程")

        return killed_count, remaining

    except Exception as e:
        print(f"强制清理进程时出错: {e}")
        try:
            return 0, len(pool._pool)
        except:
            return 0, 0

def cleanup_all_processes_for_tag(influx_ubique_tagId):
    """清理指定tagId的所有相关进程"""
    global active_process_pools, sse_connection_status, cleanup_in_progress

    # 检查是否已经在清理中，避免重复清理
    if influx_ubique_tagId in cleanup_in_progress:
        print(f"tagId {influx_ubique_tagId} 正在清理中，跳过重复清理")
        return

    # 标记为清理中
    cleanup_in_progress.add(influx_ubique_tagId)

    try:
        print(f"开始清理tagId {influx_ubique_tagId} 的所有相关进程")

        # 设置连接状态为断开
        sse_connection_status[influx_ubique_tagId] = False

        # 清理活跃的进程池
        if influx_ubique_tagId in active_process_pools:
            pool = active_process_pools[influx_ubique_tagId]
            try:
                print(f"清理活跃进程池 (tagId: {influx_ubique_tagId})")
                killed_count, remaining = force_cleanup_processes(pool, influx_ubique_tagId)
                print(f"进程池清理完成: 杀死 {killed_count} 个进程, 剩余 {remaining} 个进程")
            except Exception as e:
                print(f"清理进程池时出错: {e}")
            finally:
                # 安全地从活跃列表中移除
                try:
                    del active_process_pools[influx_ubique_tagId]
                except KeyError:
                    pass  # 已经被其他地方删除了，忽略
        else:
            print(f"tagId {influx_ubique_tagId} 的进程池已经被清理或不存在")

        # 清理连接状态
        if influx_ubique_tagId in sse_connection_status:
            try:
                del sse_connection_status[influx_ubique_tagId]
            except KeyError:
                pass  # 已经被其他地方删除了，忽略

        print(f"tagId {influx_ubique_tagId} 的所有进程清理完成")

    finally:
        # 无论如何都要从清理进度中移除
        cleanup_in_progress.discard(influx_ubique_tagId)

# 新增结构定义
FileHeader = namedtuple('FileHeader', [
    'block_id', 'file_flags', 'sw_version_major', 'sw_version_minor',
    'sw_version_stroke', 'timestamp', 'sequence', 'data_size',
    'dallas_id', 'session_id'
])

RecordHeader = namedtuple('RecordHeader', [
    'record_id', 'packet_version_major', 'packet_version_minor',
    'sample_cycles', 'session_sequence', 'timestamp'
])

Signal = namedtuple('Signal', [
    'name', 'message_type', 'node_types', 'node_count', 'bits_per_item',
    'storage_period', 'byte_offset', 'bit_offset', 'scaling', 'unit',
    'element_count'
])

def _process_file_segment_with_influx_write(bin_file, start_offset, packet_count, signals_list,
                                          project_name, project_type, filesPostgresqlInfo, files_Minio_info, process_id, progress_queue, verbose, node_mappings, influx_ubique_tagId, folderName):
    """处理文件片段并直接写入InfluxDB - 独立进程版本"""

    # 检查SSE连接状态
    global sse_connection_status
    if not sse_connection_status.get(influx_ubique_tagId, True):
        print('SSE连接已断开 --- ')
        return {'process_id': process_id, 'status': 'cancelled', 'error': 'SSE连接已断开'}

    process = psutil.Process(os.getpid())
    start_memory = process.memory_info()
    start_rss = start_memory.rss / 1024 / 1024
    process_start = time.time()

    # print(f'独立进程{process_id}开始: PID={os.getpid()}, offset={start_offset}, count={packet_count}')
    # print(f'  开始内存: RSS={start_rss:.2f}MB')

    # 建立独立的InfluxDB连接
    influx_client = None
    try:
        # 创建独立的InfluxDB管理器
        influx_client = InfluxDBManager(bucket=project_type, binFilesPostgresqlInfo=filesPostgresqlInfo, binFiles_Minio_info=files_Minio_info)
        influx_client.batch_size = 5000  # 优化批次大小
    
        # 在调用C扩展前再次检查SSE连接状态
        if not sse_connection_status.get(influx_ubique_tagId, True):
            return {'process_id': process_id, 'status': 'cancelled', 'error': 'SSE连接已断开，停止C扩展调用'}

        # 调用C扩展解析数据 - 确保参数顺序正确
        c_files_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '../c_files')
        if c_files_dir not in sys.path:
            sys.path.insert(0, c_files_dir)

        import binfile_parser
        # 参数顺序：bin_file, start_offset, packet_count, signals_list, verbose
        result = binfile_parser.parse_bin_file_segment(bin_file, start_offset, packet_count, signals_list, verbose)

        # C扩展执行完成后立即检查SSE连接状态
        if not sse_connection_status.get(influx_ubique_tagId, True):
            return {'process_id': process_id, 'status': 'cancelled', 'error': 'SSE连接已断开，停止数据处理'}
        
        if not result or 'error' in result:
            return {'process_id': process_id, 'status': 'failed', 'error': result.get('error', '解析失败')}
        
        # 直接处理和写入数据
        success, timerange = _process_and_write_segment_data(
            result, signals_list, influx_client, 
            project_name, project_type, filesPostgresqlInfo, files_Minio_info, bin_file, process_id, progress_queue, node_mappings, influx_ubique_tagId, folderName
        )
        
        end_memory = process.memory_info()
        end_rss = end_memory.rss / 1024 / 1024
        process_time = time.time() - process_start
        
        # print(f'独立进程{process_id}完成: 耗时{process_time:.3f}秒, 内存变化{end_rss-start_rss:+.2f}MB')
        
        return {
            'process_id': process_id, 
            'status': 'success' if success else 'failed',
            'records_count': len(result.get('records', [])),
            'signals_count': len(result.get('signal_data', {})),
            'timerange': timerange
        }
        
    except Exception as e:
        print(f"独立进程{process_id}处理失败: {e}")
        return {'process_id': process_id, 'status': 'failed', 'error': str(e)}
    
    finally:
        if influx_client:
            influx_client.close()

def _process_and_write_segment_data(result, signals_list, influx_client,
                                  project_name, project_type, filesPostgresqlInfo, files_Minio_info, bin_file, process_id, progress_queue, node_mappings, influx_ubique_tagId, folderName):
    """处理解析结果并写入InfluxDB（优化：同一nodeType-nodeName-timestamp下多个signal合并为一个Point，多个field）"""

    # 检查SSE连接状态
    global sse_connection_status
    if not sse_connection_status.get(influx_ubique_tagId, True):
        print(f'进程{process_id}: SSE连接已断开，停止处理')
        return False, None

    if not result.get('signal_data'):
        print(f'进程{process_id}: 无信号数据')
        return True, None

    total_signals = len(result['signal_data'])
    processed_signals = 0

    # 获取文件基本信息
    header = result.get('header', {})
    if hasattr(header, 'sw_version_major'):
        software_version = f"{header.sw_version_major}.{header.sw_version_minor}.{header.sw_version_stroke}"
        session_id = str(header.session_id)
        dallas_id = f"{header.dallas_id:016X}"
    else:
        software_version = f"{header.get('sw_version_major', 0)}.{header.get('sw_version_minor', 0)}.{header.get('sw_version_stroke', 0)}"
        session_id = str(header.get('session_id', 'unknown'))
        dallas_id = f"{header.get('dallas_id', 0):016X}"

    # 新增：聚合结构 {(nodeType, nodeName, timestamp): {signal1: value1, signal2: value2, ...}}
    point_dict = defaultdict(dict)
    time_dict = {}  # {(nodeType, nodeName, timestamp): datetime对象}

    # 遍历所有信号，聚合到 point_dict
    for signal_name, timestamps in result['signal_data'].items():
        # 检查SSE连接状态
        if not sse_connection_status.get(influx_ubique_tagId, True):
            print(f'进程{process_id}: SSE连接已断开，停止信号处理')
            return False, None

        # print(f'\n {"*"*8}')

        # 查找信号配置
        signal_config = None
        for signal in signals_list:
            if signal['name'] == signal_name:
                signal_config = signal
                break
        if not signal_config:
            continue

        node_type = signal_config['node_types'][0] if signal_config['node_types'] else 'unknown'
        node_mapping = node_mappings.get(node_type, {})

        for timestamp, samples_list in timestamps.items():
            for samples in samples_list:
                for node_index, value in enumerate(samples):
                    mapped_name = node_mapping.get(node_index)
                    node_name = mapped_name if mapped_name else f"Node_{node_index}"

                    # 统一时间戳为datetime对象，并确保毫秒为 .000
                    if isinstance(timestamp, str):
                        try:
                            # 检查时间戳是否包含无效的年份（通常是数据损坏的标志）
                            if '-' in timestamp:
                                year_part = timestamp.split('-')[0]
                                try:
                                    year = int(year_part)
                                    # 检查年份是否在合理范围内（1900-2100）
                                    if year < 1900 or year > 2100:
                                        print(f'进程{process_id}: 跳过无效时间戳（年份超出范围）: {timestamp}')
                                        continue
                                except ValueError:
                                    print(f'进程{process_id}: 跳过无效时间戳（年份格式错误）: {timestamp}')
                                    continue

                            if '.' in timestamp:
                                # 已有小数点，可能是毫秒或更长的微秒
                                parts = timestamp.split('.')
                                main_part = parts[0]
                                microsecond_part = parts[1][:3].ljust(3, '0')  # 取前三位并补零至三位
                                timestamp_str = f"{main_part}.{microsecond_part}"
                                timestamp_dt = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S.%f")
                            else:
                                # 没有毫秒，补充 .000
                                timestamp_dt = datetime.strptime(timestamp + '.000', "%Y-%m-%d %H:%M:%S.%f")
                        except Exception as e:
                            print(f'进程{process_id}: 时间戳解析失败: {e}，跳过时间戳: {timestamp}')
                            continue
                    elif isinstance(timestamp, datetime):
                        # 若已是 datetime 对象，将微秒设为 0（即 .000）
                        timestamp_dt = timestamp.replace(microsecond=(timestamp.microsecond // 1000) * 1000)
                    else:
                        print(f'进程{process_id}: 不支持的时间戳类型')
                        continue

                    key = (node_type, node_name, timestamp)
                    # signal_name 作为 field key
                    point_dict[key][signal_name] = float(value)
                    # 只需存一次时间
                    if key not in time_dict:
                        time_dict[key] = timestamp_dt

        # 更新进度
        processed_signals += 1
        progress = min((processed_signals / total_signals) * 100, 99)
        if processed_signals % 10 == 0 or processed_signals == total_signals:
            progress_data = {
                'process_id': process_id,
                'progress': round(progress, 2),
                'processed_signals': processed_signals,
                'total_signals': total_signals,
                'status': 'processing'
            }
            try:
                progress_queue.put_nowait(progress_data)
            except:
                pass


    if time_dict:
        min_time = min(time_dict.values())
        max_time = max(time_dict.values())
    else:
        min_time = max_time = datetime.now()  # 防止无数据时报错

    # 构造 Point 列表 --- 有分表    
    all_points = []
    
    # 1. 提取文件名 LOG00760.BIN
    binFile_name = os.path.basename(bin_file)

    # 2. 在 files_Minio_info 中查找 object_name 匹配的项
    match = next((item for item in files_Minio_info if item['object_name'].endswith(binFile_name)), None)
    
    for (node_type, node_name, timestamp), fields in point_dict.items():
        point = Point(project_name) \
            .time(time_dict[(node_type, node_name, timestamp)], WritePrecision.MS) \
            .tag("nodeType_nodeName", f"{node_type}_{node_name}") \
            .tag("influx_ubique_tagId", influx_ubique_tagId)
            
        # 所有信号作为 field
        for signal, value in fields.items():
            point = point.field(signal, value)
               
        all_points.append(point)

    # 批量写入所有数据点
    if all_points:
        try:
            write_start = time.time()
            batch_size = influx_client.batch_size
            successful_batches = 0
            total_batches = (len(all_points) + batch_size - 1) // batch_size

            for i in range(0, len(all_points), batch_size):
                batch = all_points[i:i + batch_size]
                try:
                    influx_client.write_api.write(
                        bucket=influx_client.bucket,
                        org=influx_client.org,
                        record=batch,
                        timeout=60_000
                    )
                    successful_batches += 1
                except Exception as batch_error:
                    print(f'进程{process_id}: 批次写入失败 ({i//batch_size + 1}/{total_batches}): {batch_error}')

            write_time = time.time() - write_start
            # print(f'进程{process_id}: 写入完成，成功批次: {successful_batches}/{total_batches}，耗时{write_time:.2f}秒')

            final_progress = {
                'process_id': process_id,
                'progress': 99,
                'processed_signals': total_signals,
                'total_signals': total_signals,
                'status': 'Processing',
                'points_written': len(all_points),
                'successful_batches': successful_batches,
                'total_batches': total_batches
            }
            try:
                progress_queue.put_nowait(final_progress)
            except:
                pass

            timeRange = {
                'min_time': min_time if min_time else None,
                'max_time': max_time if max_time else None
            }
            return successful_batches == total_batches,  timeRange

        except Exception as e:
            print(f'进程{process_id}: 写入失败 - {e}')
            error_progress = {
                'process_id': process_id,
                'status': 'error',
                'error': str(e)
            }
            try:
                progress_queue.put_nowait(error_progress)
            except:
                pass
            return False
    else:
        print(f'进程{process_id}: 没有数据点需要写入')
        timeRange = {
            'min_time': min_time if min_time else None,
            'max_time': max_time if max_time else None
        }
        return True, timeRange

def _convert_to_global_progress(filesPostgresqlInfo, files_Minio_info, filename, process_progress, current_file_index, total_files,
                               completed_processes, total_processes_current_file, global_timeRange_input, is_last_file=False, influx_ubique_tagId=None, folderName=None):
    """
    计算全局进度
    :param process_progress: 子进程进度数据
    :param current_file_index: 当前文件索引 (0-based)
    :param total_files: 总文件数量
    :param completed_processes: 当前文件已完成的进程数量（可以是小数，用于估算）
    :param total_processes_current_file: 当前文件的总进程数量
    :param is_last_file: 是否为最后一个文件
    """
    from datetime import datetime, timedelta
    from data_pipeline.models import DataPipelineFile

    global binFiles_Global_timeRange
    global binfile_Postgresql_id_list

    try:
        # 1. 每个文件的总体进度占比
        file_weight = 1.0 / total_files if total_files > 0 else 1.0

        # 2. 已完成的文件级别进度
        completed_files_progress = current_file_index * file_weight

        # 3. 正在执行文件的进程进度（支持小数值用于估算）
        current_file_process_progress = completed_processes / total_processes_current_file if total_processes_current_file > 0 else 0

        # 4. 计算总体进度
        total_progress = completed_files_progress + (file_weight * current_file_process_progress)

        # 5. 确保进度在合理范围内
        total_progress = max(0.0, min(total_progress, 1.0))

        # 6. 如果是最后一个文件，限制最大进度为99%，直到所有进程完成
        if is_last_file and completed_processes < total_processes_current_file:
            total_progress = min(total_progress, 0.99)

        # 转换为百分比，确保至少显示1%（除非真的是0）
        if total_progress > 0 and total_progress < 0.01:
            progress_percentage = 1.0
        else:
            progress_percentage = round(total_progress * 100, 2)
        
        # 修复状态判断逻辑：只有当是最后一个文件且所有进程都完成时才设置为"Completed"
        if is_last_file and completed_processes >= total_processes_current_file:
            status = "Completed"
        elif process_progress.get('status') == 'Starting':
            status = "Starting"
        elif 'estimated' in process_progress.get('status', ''):
            status = "Processing (estimated)"
        else:
            status = "Processing"

        # 安全地处理时间格式化
        try:
            binFile_startTime = global_timeRange_input['start_time'].strftime('%Y-%m-%d %H:%M:%S')
            binFile_endTime = global_timeRange_input['end_time'].strftime('%Y-%m-%d %H:%M:%S')
        except (KeyError, AttributeError):
            # 如果时间范围不可用，使用当前时间
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            binFile_startTime = current_time
            binFile_endTime = current_time

        # 当进度100%, 状态 Completed， 更新数据库信息
        file_id = 0
        file_Postgresql_id = None
        file_Minio_version_id = None
        if round(current_file_process_progress * 100, 2) == 100:            
            # 每次 .bin文件解析进度100%, 把起始时间范围保存到 global binFiles_Global_timeRange, 后续从里面筛选 全局时间范围
            key = str(current_file_index)
            
            binFiles_Global_timeRange[key] = {
                'binFile_startTime': binFile_startTime,
                'binFile_endTime': binFile_endTime
            }
        
            # 根据 filename 在 filesPostgresqlInfo 里筛选出对应的 id
            matched_Postgresql_files = [f for f in filesPostgresqlInfo if f['name'] == filename]
            matched_minio_files = [f for f in files_Minio_info if os.path.basename(f['object_name']) == filename]
            
            if matched_Postgresql_files and matched_minio_files:
                file_Postgresql_id = matched_Postgresql_files[0]['id']
                # 当前.bin文件解析进度100时, 保存到 binfile_Postgresql_id_list
                binfile_Postgresql_id_list.append(file_Postgresql_id)
                
                file_Minio_version_id = matched_minio_files[0]['version_id']
                file_Minio_path = matched_minio_files[0]['object_name']

                # 使用 datetime.strptime 解析时间字符串
                try:
                    file_start_time = datetime.strptime(binFile_startTime, '%Y-%m-%d %H:%M:%S')
                    file_end_time = datetime.strptime(binFile_endTime, '%Y-%m-%d %H:%M:%S')
                except ValueError as e:
                    raise ValueError(f"时间格式错误: {e}，请使用 'YYYY-MM-DD HH:MM:SS.%f' 格式")

                # *******************************
                # ✅ 新增：减去 8 小时（模拟 UTC+8 转换为 UTC 时间）
                # *******************************
                timezone_offset = timedelta(hours=8)
                start_time_utc = file_start_time - timezone_offset
                end_time_utc = file_end_time - timezone_offset

                # 转换为时间戳（秒）
                start_timestamp = int(start_time_utc.timestamp())
                end_timestamp = int(end_time_utc.timestamp())

                # 更新数据库记录 - 将时间戳转换为带时区的datetime对象保存到数据库
                try:
                    # 将时间戳转换为带UTC时区的datetime对象用于数据库存储
                    from datetime import timezone
                    start_time_for_db = datetime.fromtimestamp(start_timestamp, tz=timezone.utc)
                    end_time_for_db = datetime.fromtimestamp(end_timestamp, tz=timezone.utc)

                    # 如果是文件, 把文件的信息保存进去, 所属文件夹的信息等所有文件解析完/找到全局 开始/结束时间 再保存pgsql数据库
                    DataPipelineFile.objects.filter(id=file_Postgresql_id).update(
                        start_time=file_start_time,
                        end_time=file_end_time,
                        file_Minio_version_id=file_Minio_version_id,
                        file_Minio_path=file_Minio_path,
                        influx_ubique_tagId=influx_ubique_tagId,
                    )
                except Exception as e:
                    print(f"数据库更新失败: {str(e)}")
                    # 不返回ErrorResponse，继续执行后续逻辑
            else:
                print("未找到匹配的文件")
        
        # 时间格式含毫秒
        time_format = "%Y-%m-%d %H:%M:%S"

        # 检查 binFiles_Global_timeRange 是否为空，如果为空则使用当前文件的时间范围
        if binFiles_Global_timeRange:
            # 提取所有 start_time 和 end_time，并解析为 datetime 对象
            start_times = [datetime.strptime(v['binFile_startTime'], time_format) for v in binFiles_Global_timeRange.values()]
            end_times = [datetime.strptime(v['binFile_endTime'], time_format) for v in binFiles_Global_timeRange.values()]

            # 找到全局最早开始时间和最晚结束时间
            binFile_startTime = min(start_times).strftime(time_format)
            binFile_endTime = max(end_times).strftime(time_format)
            
            # 从 binfile_Postgresql_id_list 遍历, 更新bin文件的 文件夹信息
            for binFile_Pgsql_Id in binfile_Postgresql_id_list:
                # if folderName != "noFolderFileList":
                try:
                    # 如果是文件, 把文件的信息保存进去, 所属文件夹的信息等所有文件解析完/找到全局 开始/结束时间 再保存pgsql数据库
                    DataPipelineFile.objects.filter(id=binFile_Pgsql_Id).update(
                        fileFolder_name=folderName,
                        fileFolder_startTime=binFile_startTime,
                        fileFolder_endTime=binFile_endTime,
                    )
                except Exception as e:
                    print(f"数据库更新失败: {str(e)}")
        else:
            # 如果 binFiles_Global_timeRange 为空，使用当前输入的时间范围
            # binFile_startTime 和 binFile_endTime 已经在前面设置过了，保持不变
            pass
        
        if status == 'Completed':
            binFiles_Global_timeRange = {}
            binfile_Postgresql_id_list = []
            
            
        return {
            'progress': progress_percentage,
            'current_file_index': current_file_index + 1,  # 1-based for display
            'total_files': total_files,
            'completed_files': current_file_index,
            'current_file_completed_processes': completed_processes,
            'current_file_total_processes': total_processes_current_file,
            'file_weight': round(file_weight * 100, 2),
            'completed_files_progress': round(completed_files_progress * 100, 2),
            'current_file_process_progress': round(current_file_process_progress * 100, 2),
            'status': status,  # 使用修正后的状态
            'timestamp': time.time(),
            'process_id': process_progress.get('process_id', 0),
            'current': current_file_index + 1,
            'total': total_files,
            'binFile_startTime': binFile_startTime,
            'binFile_endTime': binFile_endTime,
            'binfile_Postgresql_id': file_Postgresql_id,
            'binfile_Minio_version_id': file_Minio_version_id,
            'influx_ubique_tagId': influx_ubique_tagId,
            'fileFolder_name': folderName,
            'fileFolder_startTime': binFile_startTime,
            'fileFolder_endTime': binFile_endTime,
        }
        
    except Exception as e:
        print(f"进度转换失败: {e}")
        return None

async def sse_event_stream(queue, param):
    project_name = param['project_name']
    project_type = param['project_type']
    filesPostgresqlInfo = param['binFilesPostgresqlInfo']
    files_Minio_info = param['binFiles_Minio_info']

    sseInflux = InfluxDBManager(bucket=project_type, binFilesPostgresqlInfo=filesPostgresqlInfo, binFiles_Minio_info=files_Minio_info)

    # 设置SSE连接状态为活跃
    global sse_connection_status
    influx_ubique_tagId = sseInflux.influx_ubique_tagId
    print(f"SSE连接建立，tagId: {influx_ubique_tagId}")
    sse_connection_status[influx_ubique_tagId] = True
    
    try:
        # 清空队列中的旧数据
        while not queue.empty():
            try:
                queue.get_nowait()
            except:
                pass

        # 确保方法正确调用
        parseInflux_task = asyncio.create_task(
            asyncio.to_thread(
                sseInflux.startParse,
                project_name,
                project_type,
                filesPostgresqlInfo,
                files_Minio_info,
            )
        )
        
        # 等待进度更新直到上传完成
        try:
            while True:  # 修改循环条件
                try:
                    data = await asyncio.wait_for(queue.get(), timeout=30.0)  # 添加超时
                    yield f"data: {json.dumps(data)}\n\n"
                    
                    # 只有在确实完成时才发送完成消息
                    if parseInflux_task.done() and data['progress'] >= 100:
                        yield f"data: {json.dumps({'status': 'completed'})}\n\n"
                        yield "event: close\ndata: close\n\n"
                        break
                except asyncio.TimeoutError:
                    # 检查任务是否完成
                    if parseInflux_task.done():
                        break
                    continue
                
        except asyncio.CancelledError:
            print("SSE Influx connection cancelled")

            # 使用全局清理函数清理所有相关进程
            cleanup_all_processes_for_tag(influx_ubique_tagId)
            sseInflux.stop_upload()

            # 强制取消解析任务
            if not parseInflux_task.done():
                parseInflux_task.cancel()
                try:
                    await parseInflux_task
                except asyncio.CancelledError:
                    pass

            yield f"data: {json.dumps({'status': 'cancelled'})}\n\n"
            yield "event: close\ndata: close\n\n"
            raise
        finally:
            # 确保连接状态被清理
            if influx_ubique_tagId in sse_connection_status:
                sse_connection_status[influx_ubique_tagId] = False

            if not parseInflux_task.done():
                sseInflux.stop_upload()
                parseInflux_task.cancel()
                try:
                    await parseInflux_task
                except asyncio.CancelledError:
                    pass

    except Exception as e:
        print(f"SSE Influx stream error: {e}")

        # 使用全局清理函数清理所有相关进程
        cleanup_all_processes_for_tag(influx_ubique_tagId)
        sseInflux.stop_upload()

        # 强制取消解析任务
        if 'parseInflux_task' in locals() and not parseInflux_task.done():
            parseInflux_task.cancel()
            try:
                await parseInflux_task
            except asyncio.CancelledError:
                pass

        yield f"data: {json.dumps({'status': 'error', 'message': str(e)})}\n\n"
        yield "event: close\ndata: close\n\n"
        raise
 
async def sse_progress_view_influxdb(request):
    param_str = request.GET.get('param', '')
    try:
        param = json.loads(param_str)
        project_name = param['project_name']
        project_type = param['project_type']
    except json.JSONDecodeError:
        logger.error("JSON解析错误")
        return StreamingHttpResponse(status=400)
    except KeyError:
        logger.error("缺少必要的参数")
        return StreamingHttpResponse(status=400)

    response = StreamingHttpResponse(
        sse_event_stream(progress_queue, param),
        content_type='text/event-stream'
    )
    response['Cache-Control'] = 'no-cache'
    response['Connection'] = 'keep-alive'
    return response

class InfluxDBManager:
    def __init__(self, bucket=str, binFilesPostgresqlInfo=list, binFiles_Minio_info=list):
        self.client = InfluxDBClient(
            url=settings.INFLUXDB_CONFIG['url'],
            token=settings.INFLUXDB_CONFIG['token'],
            org=settings.INFLUXDB_CONFIG['org'],
            timeout=60_000  # 增加默认超时时间到60秒
        )
        # 重新优化写入选项，降低批次大小但增加并发
        # write_options = WriteOptions(
        #     batch_size=10_000,           # 批次大小
        #     flush_interval=10_000,       # 每10秒刷一次（让批次攒满）
        #     jitter_interval=1_000,       # 抖动1秒，防并发高峰
        #     retry_interval=5_000,        # 初始重试间隔
        #     max_retries=5,               # 最多重试5次
        #     max_retry_delay=30_000,      # 最大延迟30秒
        #     exponential_base=2,
        #     max_close_wait=30_000,
        # )
        
        # self.write_api = self.client.write_api(write_options=write_options)
        self.write_api = self.client.write_api(write_options=ASYNCHRONOUS)
        # self.bucket = settings.INFLUXDB_CONFIG['bucket']
        self.bucket = bucket  # 强制 bucket 始终为 project_name
        self.org = settings.INFLUXDB_CONFIG['org']
        self.executor = ThreadPoolExecutor(max_workers=4)
        self._stop_upload = False
        self._progress_sent = False
        self.num_writeApi = 0  # 改为实例属性
        self.batch_size = 5000
        self.binFilesPostgresqlInfo = binFilesPostgresqlInfo
        self.binFiles_Minio_info = binFiles_Minio_info
        self.binFiles_Global_timeRange = {}
        self.influx_ubique_tagId = self.generate_random_5char_string()  # 在初始化时就生成
        
    def stop_upload(self):
        """设置停止标志"""
        self._stop_upload = True

    def close(self):
        """安全关闭所有连接"""
        try:
            if self.executor:
                self.executor.shutdown(wait=True)
            if self.write_api:
                self.write_api.close()
            if self.client:
                self.client.close()
        except Exception as e:
            logger.error(f"关闭连接时出错: {str(e)}")

    def __del__(self):
        """析构函数，确保资源正确释放"""
        self.close()

    def _validate_file_exists(self, file_path, file_type):
        """验证文件是否存在"""
        if not os.path.isfile(file_path):
            error_msg = f"错误: 未找到{file_type}文件 {file_path}"
            print(error_msg)
            return False, error_msg
        return True, ""

    def _parse_xml_configuration(self, xml_file):
        """Parse the EP2002LogPacketLayout.xml file to get signal definitions."""
        signals = []
        
        try:
            tree = ET.parse(xml_file)
            root = tree.getroot()
            
            # Handle namespace in the XML
            ns = {'df': 'http://www.Knorr-Bremse.com/EP2002DataLoggerFileFormat'}
            
            # Get packet version
            packet_version = root.find('.//df:PacketLayout/df:Version', ns).text
            
            # Get all data items
            for data_item in root.findall('.//df:PacketLayout/df:DataItem', ns):
                # Skip placeholders and commented out signals
                name = data_item.find('df:Name', ns).text
                if name.startswith('#'):
                    continue
                
                # Get node types
                node_types = []
                for node_type in data_item.findall('df:NodeTypes/df:NodeType', ns):
                    node_types.append(node_type.text)
                
                # Get message type
                message_type_elem = data_item.find('df:MessageType', ns)
                message_type = message_type_elem.text if message_type_elem is not None else ""
                
                # Get other properties
                node_count = int(data_item.find('df:NodeCount', ns).text)
                bits_per_item = int(data_item.find('df:BitsPerItem', ns).text)
                storage_period = int(data_item.find('df:StoragePeriod', ns).text)
                byte_offset = int(data_item.find('df:ByteOffset', ns).text)
                
                bit_offset_elem = data_item.find('df:BitOffset', ns)
                bit_offset = int(bit_offset_elem.text) if bit_offset_elem is not None else 0
                
                scaling_elem = data_item.find('df:Scaling', ns)
                scaling = float(scaling_elem.text) if scaling_elem is not None else 1.0
                
                unit_elem = data_item.find('df:Unit', ns)
                unit = unit_elem.text if unit_elem is not None else ""
                
                element_count_elem = data_item.find('df:ElementCount', ns)
                element_count = int(element_count_elem.text) if element_count_elem is not None else 1
                
                # Calculate overall bit offset
                overall_bit_offset = byte_offset * 8 + bit_offset
                
                for node_type in node_types:
                    signal = Signal(
                        name=name,
                        message_type=message_type,
                        node_types=[node_type],
                        node_count=node_count,
                        bits_per_item=bits_per_item,
                        storage_period=storage_period,
                        byte_offset=byte_offset,
                        bit_offset=bit_offset,
                        scaling=scaling,
                        unit=unit,
                        element_count=element_count
                    )
                    signals.append(signal)
            
            return signals, packet_version
        
        except Exception as e:
            print(f"Error parsing XML config: {e}")
            return [], "unknown"

    def _parse_node_type_mappings(self, xml_file):
        """解析 EP2002NodeTypes.xml 文件以获取节点映射关系"""
        node_mappings = {}  # {node_type: {index: node_name}}
        
        try:
            tree = ET.parse(xml_file)
            root = tree.getroot()
            
            # 处理 XML 中的命名空间
            ns = {'nt': 'http://www.Knorr-Bremse.com/EP2002NodeTypes'}
            
            # 获取所有节点类型
            for node_type in root.findall('.//nt:NodeType', ns):
                type_name = node_type.find('nt:Name', ns).text
                node_mappings[type_name] = {}
                
                # 获取该类型下的所有节点
                for node in node_type.findall('nt:Node', ns):
                    node_name = node.find('nt:Name', ns).text
                    node_index = int(node.find('nt:Index', ns).text)
                    node_mappings[type_name][node_index] = node_name
        
        except Exception as e:
            print(f"解析节点类型 XML 配置时出错: {e}")
            return {}
        
        return node_mappings

    def _calculate_packet_count(self, bin_file):
        """计算文件中的数据包总数"""
        file_size = os.path.getsize(bin_file)
        if file_size < 64 + EP2002_LOG_PACKET_SIZE:
            return 0
        actual_data_size = file_size - 64  # 减去文件头
        packet_count = actual_data_size // EP2002_LOG_PACKET_SIZE
        return packet_count

    def _parse_binary_file(self, bin_file, filename, signals=None, node_mappings=None, verbose=True, 
                        project_name=None, project_type=None, filesPostgresqlInfo=None, files_Minio_info=None, current_file_index=0, total_files=1, influx_ubique_tagId=None, folderName=None):
        """Parse an EP2002 binary log file using independent process with direct InfluxDB write."""
        start_time = time.time()
        file_size = os.path.getsize(bin_file)
        # 文件大小检查
        if file_size > 20 * 1024 * 1024:
            print("错误: 文件过大，超出处理能力")
            return {"error": "文件过大，请联系管理员"}
        
        try:
            # 转换signals格式
            signals_list = None
            if signals:
                signals_list = []
                for signal in signals:
                    signal_dict = {
                        "name": signal.name,
                        "message_type": signal.message_type,
                        "node_types": signal.node_types,
                        "node_count": signal.node_count,
                        "bits_per_item": signal.bits_per_item,
                        "storage_period": signal.storage_period,
                        "byte_offset": signal.byte_offset,
                        "bit_offset": signal.bit_offset,
                        "scaling": signal.scaling,
                        "unit": signal.unit,
                        "element_count": signal.element_count
                    }
                    signals_list.append(signal_dict)
            
            # 计算数据包和进程数
            total_packet_count = self._calculate_packet_count(bin_file)
            if total_packet_count <= 0:
                return {"error": "文件太小或格式错误"}
            
            # 限制并发连接数：最多3个进程
            available_memory = psutil.virtual_memory().available / 1024 / 1024
            max_processes_by_memory = max(1, int(available_memory // 600))  # 每进程预估600MB
            num_cores = min(max_processes_by_memory, 4, mp.cpu_count())  # 限制最大4个进程

            # 创建进程间通信队列
            try:
                manager = mp.Manager()
                progress_queue_mp = manager.Queue()
            except OSError as e:
                if "No space left on device" in str(e):
                    print(f"磁盘空间不足，无法创建进程管理器: {e}")
                    return {"error": "磁盘空间不足，请清理磁盘空间后重试"}
                else:
                    print(f"创建进程管理器失败: {e}")
                    return {"error": f"创建进程管理器失败: {str(e)}"}
            
            # 启动进程池
            try:
                pool = mp.Pool(num_cores)
                # 注册活跃进程池
                global active_process_pools
                active_process_pools[influx_ubique_tagId] = pool
            except OSError as e:
                if "No space left on device" in str(e):
                    print(f"磁盘空间不足，无法创建进程池: {e}")
                    return {"error": "磁盘空间不足，请清理磁盘空间后重试"}
                else:
                    print(f"创建进程池失败: {e}")
                    return {"error": f"创建进程池失败: {str(e)}"}

            packets_per_process = max(1, total_packet_count // num_cores)
            
            async_results = []
            for i in range(num_cores):
                start_packet = i * packets_per_process
                if i == num_cores - 1:
                    packet_count = total_packet_count - start_packet
                else:
                    packet_count = packets_per_process
                
                start_offset = 64 + start_packet * EP2002_LOG_PACKET_SIZE
                
                # 直接使用传入的node_mappings，不要重复解析
                async_result = pool.apply_async(
                    _process_file_segment_with_influx_write,
                    args=(bin_file, start_offset, packet_count, signals_list, 
                        project_name, project_type, filesPostgresqlInfo, files_Minio_info, i, progress_queue_mp, verbose, node_mappings, influx_ubique_tagId, folderName)
                )
                async_results.append(async_result)
            
            pool.close()
            
            # 进程状态跟踪
            process_status = {}
            completed_processes = 0
            is_last_file = (current_file_index == total_files - 1)
            
            single_binFile_timeRange = {}
            
            def calculate_global_timeRange(input_timeRange):
                # 筛选.bin文件最终的 起始/结束 时间
                min_times = [details['min_time'] for details in input_timeRange.values() if details.get('min_time') is not None]
                max_times = [details['max_time'] for details in input_timeRange.values() if details.get('max_time') is not None]

                if not min_times or not max_times:
                    print("未能找到有效的时间范围")
                    return None, None
                
                # 使用 min 和 max 函数获取全局最小和最大时间
                global_min = min(min_times)
                global_max = max(max_times)
                
                single_binFile_time_range = {
                    'start_time': global_min,
                    'end_time': global_max
                }
                return single_binFile_time_range
            
            # 立即发送初始进度
            initial_progress = _convert_to_global_progress(
                filesPostgresqlInfo, files_Minio_info, filename, {'status': 'Starting'},
                current_file_index, total_files, 0, num_cores,
                {'start_time': datetime.now(), 'end_time': datetime.now()},
                is_last_file, influx_ubique_tagId, folderName
            )
            if initial_progress:
                try:
                    asyncio.run(progress_queue.put(initial_progress))
                except:
                    pass

            # 记录开始时间用于估算进度
            progress_start_time = time.time()
            last_progress_time = progress_start_time

            while completed_processes < num_cores:
                # 检查SSE连接状态
                if not sse_connection_status.get(influx_ubique_tagId, True):
                    print(f"SSE连接已断开，立即终止进程池")

                    # 使用专门的清理函数
                    killed_count, remaining = force_cleanup_processes(pool, influx_ubique_tagId)

                    # 从活跃进程池列表中移除
                    if influx_ubique_tagId in active_process_pools:
                        del active_process_pools[influx_ubique_tagId]

                    if remaining > 0:
                        print(f"警告: 仍有 {remaining} 个进程未能完全清理")

                    return {"error": "SSE连接已断开，解析任务被取消"}

                current_time = time.time()

                # 检查进程完成状态
                for i, async_result in enumerate(async_results):
                    if async_result.ready() and i not in process_status:
                        try:
                            result = async_result.get()
                            process_status[i] = result
                            completed_processes += 1
                            # print(f"进程{i}完成: {result.get('status', 'unknown')}")

                            # 筛选 .bin文件 单个进程处理得到的时间范围: 开始时间 - 结束时间
                            single_binFile_timeRange[i] = result.get('timerange')

                            # 筛选 .bin文件 的全局 min_time / max_time
                            global_timeRange = calculate_global_timeRange(single_binFile_timeRange)


                            # 发送文件级别的进度更新
                            file_progress = {
                                'process_id': i,
                                'status': 'Processing'
                            }
                            global_progress = _convert_to_global_progress(
                                filesPostgresqlInfo, files_Minio_info, filename, file_progress, current_file_index, total_files,
                                completed_processes, num_cores, global_timeRange, is_last_file, influx_ubique_tagId, folderName
                            )
                            if global_progress:
                                try:
                                    asyncio.run(progress_queue.put(global_progress))
                                except:
                                    pass
                            last_progress_time = current_time

                        except Exception as e:
                            process_status[i] = {'status': 'failed', 'error': str(e)}
                            completed_processes += 1

                # 每2秒发送一次估算进度（即使没有进程完成）
                if current_time - last_progress_time >= 2.0:
                    # 基于时间估算进度
                    elapsed_time = current_time - progress_start_time
                    # 假设平均每个文件需要30秒处理时间，可以根据实际情况调整
                    estimated_file_time = 50.0
                    estimated_progress = min(elapsed_time / estimated_file_time, 0.95)  # 最多估算到95%

                    # 创建估算的时间范围
                    estimated_timerange = {
                        'start_time': datetime.now() - timedelta(seconds=elapsed_time),
                        'end_time': datetime.now()
                    }

                    estimated_global_progress = _convert_to_global_progress(
                        filesPostgresqlInfo, files_Minio_info, filename,
                        {'status': 'Processing (estimated)'},
                        current_file_index, total_files,
                        estimated_progress * num_cores,  # 估算已完成的进程数
                        num_cores, estimated_timerange, is_last_file, influx_ubique_tagId, folderName
                    )
                    if estimated_global_progress:
                        try:
                            asyncio.run(progress_queue.put(estimated_global_progress))
                        except:
                            pass
                    last_progress_time = current_time

                # 处理进度队列中的消息 (子进程级别的进度，现在可以忽略或用于调试)
                try:
                    while not progress_queue_mp.empty():
                        # 可以选择忽略子进程级别的进度，只使用文件级别进度
                        progress_data = progress_queue_mp.get_nowait()
                        pass
                except:
                    pass

                time.sleep(0.1)
            
            # 正常完成时也确保进程池被正确清理
            try:
                pool.close()  # 先关闭进程池，不接受新任务

                # 使用简单的方式等待进程完成
                max_wait_time = 3  # 最多等待3秒
                wait_start = time.time()

                while time.time() - wait_start < max_wait_time:
                    remaining_processes = [p for p in pool._pool if p.is_alive()]
                    if not remaining_processes:
                        print("所有进程已正常完成")
                        break
                    time.sleep(0.1)
                else:
                    # 超时后强制清理
                    remaining_processes = [p for p in pool._pool if p.is_alive()]
                    if remaining_processes:
                        print(f"等待超时，仍有 {len(remaining_processes)} 个进程活跃，强制清理")
                        force_cleanup_processes(pool, influx_ubique_tagId)

                # 最后调用join清理资源
                try:
                    pool.join()
                except:
                    pass  # 忽略join的错误

                # 从活跃进程池列表中移除
                if influx_ubique_tagId in active_process_pools:
                    del active_process_pools[influx_ubique_tagId]

            except Exception as e:
                print(f"进程池清理时出错: {e}")
                # 如果正常清理失败，使用强制清理
                force_cleanup_processes(pool, influx_ubique_tagId)
            
            
            # 筛选.bin文件最终的 起始/结束 时间 ------------
            min_times = [details['min_time'] for details in single_binFile_timeRange.values() if details.get('min_time') is not None]
            max_times = [details['max_time'] for details in single_binFile_timeRange.values() if details.get('max_time') is not None]

            if not min_times or not max_times:
                print("未能找到有效的时间范围")
                return None, None
            
            # 使用 min 和 max 函数获取全局最小和最大时间
            global_min = min(min_times)
            global_max = max(max_times)

            # 打印结果
            
            single_binFile_time_range = {
                'start_time': global_min,
                'end_time': global_max
            }
            
            # 统计结果
            successful_processes = sum(1 for status in process_status.values() 
                                    if status.get('status') == 'success')
            total_records = sum(status.get('records_count', 0) for status in process_status.values())
            total_signals = sum(status.get('signals_count', 0) for status in process_status.values())
            
            end_time = time.time()
            # print(f'独立进程处理完成: {successful_processes}/{num_cores}个进程成功, '
            #       f'总记录数={total_records}, 总信号数={total_signals}, 耗时: {end_time - start_time:.2f}秒')
            
            # 筛选.bin文件最终的 起始/结束 时间 结束 ------------
            
            return {
                'success': successful_processes == num_cores,
                'processes': process_status,
                'total_records': total_records,
                'total_signals': total_signals,
                'processing_time': end_time - start_time,
                'single_binFile_time_range': single_binFile_time_range
            }
            
        except Exception as e:
            print(f"独立进程方案执行失败: {e}")

            # 异常情况下也要确保进程池被清理
            try:
                if 'pool' in locals():
                    print("异常情况下强制清理进程池")
                    force_cleanup_processes(pool, influx_ubique_tagId)
                    # 从活跃进程池列表中移除
                    if influx_ubique_tagId in active_process_pools:
                        del active_process_pools[influx_ubique_tagId]
            except Exception as cleanup_error:
                print(f"异常清理进程池时出错: {cleanup_error}")

            return {"error": f"处理失败: {str(e)}"}

    def query_signal_data(self, projectName, projectType, start_time, end_time, signal_name, node_name, node_type, binfile_Postgresql_id, binfile_Minio_version_id, influx_ubique_tagId):
        from datetime import datetime, timedelta
        file_Minio_versionID = binfile_Minio_version_id[:5]

        try:
            # 解析时间并加 8 小时
            def add_timezone_and_shift(time_str):
                # 替换 Z 为 +00:00 来兼容 fromisoformat
                time_str = time_str.replace('Z', '+00:00')
                dt = datetime.fromisoformat(time_str)  # 现在可以正确解析
                dt += timedelta(hours=8)              # 加上 8 小时
                return dt.strftime('%Y-%m-%dT%H:%M:%S') + 'Z'

            adjusted_start = add_timezone_and_shift(start_time)
            adjusted_end = add_timezone_and_shift(end_time)
            query = f'''
            from(bucket: "{projectType}")
                |> range(start: {adjusted_start}, stop: {adjusted_end})
                |> filter(fn: (r) => r["_measurement"] == "{projectName}")
                |> filter(fn: (r) => r["nodeType_nodeName"] == "{node_type}_{node_name}")
                |> filter(fn: (r) => r["influx_ubique_tagId"] == "{influx_ubique_tagId}")
                |> filter(fn: (r) => r["_field"] == "{signal_name}")
                |> yield(name: "mean")
            '''

            result = self.client.query_api().query(query, org=self.org)
            
            data_points = []
            for table in result:
                for record in table.records:
                    data_points.append({
                        'time': (record.get_time()).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                        'value': record.get_value()
                    })
            # print('查询到的数据点:', data_points)
            return data_points
            
        except Exception as e:
            logger.error(f"查询 InfluxDB 失败: {str(e)}")
            raise Exception(f"查询 InfluxDB 失败: {str(e)}")

    def get_project_info(self, projectName, projectType, start_time, end_time, binfile_Postgresql_id, binfile_Minio_version_id):
        """获取项目的节点类型、节点名称和信号列表"""
        # 时间格式 |> range(start: 2025-03-10T03:00:00Z, stop: 2025-03-20T04:00:00Z)
        try:
            # 1. 首先查询所有节点类型
            query = f'''
            from(bucket: "{projectType}")
                |> range(start: {start_time}, stop: {end_time})
                |> filter(fn: (r) => r["_measurement"] == "{projectName}")
                |> filter(fn: (r) => exists r["nodeType"])
                |> keep(columns: ["nodeType"])
                |> group()
                |> distinct(column: "nodeType")
            '''
            result = self.client.query_api().query(query, org=self.org)
            
            # 创建最终的数据结构
            response_data = {}
            
            # 对每个节点类型进行处理
            for table in result:
                for record in table.records:
                    if "_value" not in record.values:
                        continue
                        
                    node_type = record.values["_value"]
                    response_data[node_type] = {}
                    
                    # 2. 查询该节点类型下的所有节点名称
                    nodes_query = f'''
                    from(bucket: "{projectType}")
                        |> range(start: {start_time}, stop: {end_time})
                        |> filter(fn: (r) => r["_measurement"] == "{projectName}")
                        |> filter(fn: (r) => r["nodeType"] == "{node_type}")
                        |> filter(fn: (r) => exists r["nodeName"])
                        |> keep(columns: ["nodeName"])
                        |> group()
                        |> distinct(column: "nodeName")
                    '''
                    nodes_result = self.client.query_api().query(nodes_query, org=self.org)
                    
                    # 处理每个节点名称
                    for nodes_table in nodes_result:
                        for node_record in nodes_table.records:
                            if "_value" not in node_record.values:
                                continue
                                
                            node_name = node_record.values["_value"]
                            
                            # 3. 查询该节点下的所有信号
                            signals_query = f'''
                            from(bucket: "{projectType}")
                                |> range(start: {start_time}, stop: {end_time})
                                |> filter(fn: (r) => r["_measurement"] == "{projectName}")
                                |> filter(fn: (r) => r["nodeType"] == "{node_type}")
                                |> filter(fn: (r) => r["nodeName"] == "{node_name}")
                                |> filter(fn: (r) => exists r["signal"])
                                |> keep(columns: ["signal"])
                                |> group()
                                |> distinct(column: "signal")
                            '''
                            signals_result = self.client.query_api().query(signals_query, org=self.org)
                            
                            # 收集信号列表
                            signals = []
                            for signals_table in signals_result:
                                for signal_record in signals_table.records:
                                    if "_value" in signal_record.values:
                                        signals.append(signal_record.values["_value"])
                            
                            # 将排序后的信号列表添加到响应数据中
                            response_data[node_type][node_name] = sorted(signals)

            # print('response_data ======================= ', response_data)
            return response_data
            
        except Exception as e:
            logger.error(f"获取项目信息失败: {str(e)}")
            raise Exception(f"获取项目信息失败: {str(e)}") 

    def generate_random_5char_string(self):
        # 获取当前时间戳（毫秒级），取最后几位作为种子 目的: 随机生成一个5位字符串, 作为每一次上传文件(单文件/多文件List/文件夹等)-保存influxdb的唯一 tag
        timestamp = int(time.time() * 1000)
        
        # 使用时间戳初始化随机数生成器，保证每次调用时随机性不同
        random.seed(timestamp)
        
        # 定义可选字符集：大小写字母 + 数字
        characters = string.ascii_letters + string.digits  # a-zA-Z0-9
        
        # 随机选择 5 个字符
        random_string = ''.join(random.choices(characters, k=5))
        
        return random_string

    def startParse(self, project_name, project_type, filesPostgresqlInfo, files_Minio_info):
        """开始解析数据 - 使用独立进程方案"""
        self._progress_sent = False
        self._stop_upload = False
        self.bucket = project_name  # 确保每次解析时 bucket 都是 project_name
        # influx_ubique_tagId 已在初始化时生成，直接使用
        influx_ubique_tagId = self.influx_ubique_tagId

        # 设置SSE连接状态为活跃
        global sse_connection_status
        sse_connection_status[influx_ubique_tagId] = True
        
        try:
            # 获取指定路径 settings.UPLOAD_FILES_DIR 下所有文件夹的路径和名字
            folders = [f for f in os.listdir(settings.UPLOAD_FILES_DIR) if os.path.isdir(os.path.join(settings.UPLOAD_FILES_DIR, f))]
            folderPath = os.path.join(settings.UPLOAD_FILES_DIR, folders[0])
            # 获取文件夹名称
            folderName = os.path.basename(folderPath)
            
            # 修改文件查找逻辑，同时匹配 .bin 和 .BIN 文件
            fileList = [f for f in os.listdir(folderPath) if 
                        (f.endswith('.bin') or f.endswith('.BIN')) and 
                        os.path.isfile(os.path.join(folderPath, f))]

            if not fileList:
                return False, "未找到可解析的bin文件"

            totalNum = len(fileList)

            # 验证配置文件
            xml = os.path.join(settings.PARSE_CONF_DIR, 'EP2002LogPacketLayout.xml')
            node_types_xml = os.path.join(settings.PARSE_CONF_DIR, 'EP2002NodeTypes.xml')
            
            valid, error_msg = self._validate_file_exists(xml, "信号配置")
            if not valid:
                return False, error_msg
                
            valid, error_msg = self._validate_file_exists(node_types_xml, "节点类型配置")
            if not valid:
                return False, error_msg

            # 只解析一次配置文件
            signals, packet_version = self._parse_xml_configuration(xml)
            node_mappings = self._parse_node_type_mappings(node_types_xml)
            
            if not signals:
                return False, "解析信号配置失败"

            fileIndex = 0
            
            # 处理每个文件
            for filename in fileList:
                if self._stop_upload:
                    return False, '上传任务被取消'

                # 检查SSE连接状态
                if not sse_connection_status.get(influx_ubique_tagId, True):
                    print(f"SSE连接已断开，停止文件处理循环")
                    return False, 'SSE连接已断开，任务被取消'

                bin_file = os.path.join(folderPath, filename)
                fileIndex += 1
                valid, error_msg = self._validate_file_exists(bin_file, "二进制")
                if not valid:
                    continue

                # 使用多进程方案解析并写入，传递node_mappings
                result = self._parse_binary_file(bin_file, filename, signals, node_mappings, True, project_name, project_type, filesPostgresqlInfo, files_Minio_info, fileIndex - 1, totalNum, influx_ubique_tagId, folderName)
                # 清空异步队列中的进度数据（避免异步队列在同步代码中的使用问题）
                try:
                    while not progress_queue.empty():
                        try:
                            progress_queue.get_nowait()
                        except:
                            break
                except:
                    pass
    
                if not result or result.get('error'):
                    print(f"解析文件失败: {filename}")
                    continue
                
                if not result.get('success'):
                    print(f"文件处理未完全成功: {filename}")

            # 清理SSE连接状态
            if influx_ubique_tagId in sse_connection_status:
                del sse_connection_status[influx_ubique_tagId]

            return True, "处理完成", result

        except Exception as e:
            logger.error(f"解析过程发生错误: {str(e)}")
            # 清理SSE连接状态
            if influx_ubique_tagId in sse_connection_status:
                del sse_connection_status[influx_ubique_tagId]
            return False, f"解析失败: {str(e)}"