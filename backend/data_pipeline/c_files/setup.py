from setuptools import setup, Extension
import os

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(__file__))

# 定义C扩展模块
binfile_parser_module = Extension(
    'binfile_parser',
    sources=[os.path.join(current_dir, 'binfile_parse_module.c')],
    include_dirs=['/usr/include'],
    libraries=[],  # 不依赖cjson，直接使用Python C API
    extra_compile_args=[
        '-O3',                    # 最高优化级别
        '-march=native',          # 针对当前CPU优化
        '-flto',                  # 链接时优化
        '-funroll-loops',         # 循环展开
        '-finline-functions',     # 函数内联
        '-DNDEBUG',              # 关闭调试模式
        '-ffast-math',           # 快速数学运算
        '-std=c99'               # C99标准
    ],
    extra_link_args=['-flto', '-O3']
)

setup(
    name='binfile_parser',
    version='1.0.0',
    description='EP2002二进制文件解析器C扩展模块',
    long_description='高性能的EP2002二进制日志文件解析器，使用C实现以获得最佳性能',
    ext_modules=[binfile_parser_module],
    zip_safe=False,
    python_requires='>=3.6',
) 