[{"name": "Physical Car Code", "message_type": "InternalSignal", "node_types": ["CAR-logical"], "node_count": 5, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 17, "bit_offset": 0, "scaling": 1.0, "unit": "hex", "element_count": 1}, {"name": "Auxiliary Pressure 1", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 12, "storage_period": 2000, "byte_offset": 32, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "Auxiliary Pressure 1", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 12, "storage_period": 2000, "byte_offset": 32, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "Auxiliary Pressure 2", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 12, "storage_period": 2000, "byte_offset": 47, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "Auxiliary Pressure 2", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 12, "storage_period": 2000, "byte_offset": 47, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "ASP 1 Transducer Status", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 62, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "ASP 1 Transducer Status", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 62, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "ASP 2 Transducer Status", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 63, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "ASP 2 Transducer Status", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 63, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "ASP Errors", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 64, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "ASP Errors", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 64, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "AUX 1", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 67, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "AUX 1", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 67, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "AUX 2", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 68, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "AUX 2", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 68, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "ASP Fit", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 69, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "ASP Fit", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 69, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "AUX Fit", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 72, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "AUX Fit", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 72, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "WSP Selftest Status", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 74, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "WSP Selftest Status", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 74, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Emergency Selftest Status", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 77, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Emergency Selftest Status", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 77, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Service Brake Selftest Status", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 79, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Service Brake Selftest Status", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 79, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Emergency Jerk Timer", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 82, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Emergency Jerk Timer", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 82, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "BCP 1 Transducer Status", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 83, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "BCP 1 Transducer Status", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 83, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "BCP 2 Transducer Status", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 84, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "BCP 2 Transducer Status", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 84, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Low BSR", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 85, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Low BSR", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 85, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "VLCP", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 10, "storage_period": 2000, "byte_offset": 87, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "VLCP", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 10, "storage_period": 2000, "byte_offset": 87, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "BSRP", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 12, "storage_period": 2000, "byte_offset": 100, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "BSRP", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 12, "storage_period": 2000, "byte_offset": 100, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "Bogie Air Consumption", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 115, "bit_offset": 0, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "Bogie Air Consumption", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 115, "bit_offset": 0, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "Valve Temperature", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 125, "bit_offset": 0, "scaling": 1.0, "unit": "C", "element_count": 1}, {"name": "Valve Temperature", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 125, "bit_offset": 0, "scaling": 1.0, "unit": "C", "element_count": 1}, {"name": "RBX Selftest Status", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 135, "bit_offset": 0, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "RBX Selftest Status", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 135, "bit_offset": 0, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "VLCP Transducer Status", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 145, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "VLCP Transducer Status", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 145, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "BSRP Transducer Status", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 146, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "BSRP Transducer Status", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 146, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Valve temperature status", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 147, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Valve temperature status", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 147, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Heater Supply", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 148, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Heater Supply", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 148, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Heater Status", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 150, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Heater Status", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 150, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Fail to Release/Apply Mode", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 151, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Fail to Release/Apply Mode", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 151, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "VLCP Fitted", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 152, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "VLCP Fitted", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 152, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "BSRP Fitted", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 153, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "BSRP Fitted", "message_type": "RBX_GENERAL_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 153, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "evtMRI WSP TIMEOUT", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 0, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "evtMRI WSP TIMEOUT", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 0, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "evtMRI SW TIMEOUT", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 1, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "evtMRI SW TIMEOUT", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 1, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "evtMRI MISC", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 2, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "evtMRI MISC", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 2, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "sysMRI EPROM", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 3, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "sysMRI EPROM", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 3, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "sysMRI RAM", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 4, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "sysMRI RAM", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 4, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "sysMRI C167 DEVICE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 5, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "sysMRI C167 DEVICE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 5, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "sysMRI EXT DEVICE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 6, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "sysMRI EXT DEVICE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 6, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "sysMRI PROCESS", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 7, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "sysMRI PROCESS", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 7, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "senMRI TACHO 1", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 8, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "senMRI TACHO 1", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 8, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "senMRI TACHO 2", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 9, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "senMRI TACHO 2", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 9, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "senMRI BCP SENSORS", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 10, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "senMRI BCP SENSORS", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 10, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "senMRI PRESSURE SENSORS", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 11, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "senMRI PRESSURE SENSORS", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 11, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "senMRI VOLTAGE SENSORS", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 12, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "senMRI VOLTAGE SENSORS", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 12, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI WSP ENABLE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 13, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI WSP ENABLE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 13, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI BRAKES APPLIED", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 14, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI BRAKES APPLIED", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 14, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI HEATER", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 15, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI HEATER", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 15, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI VOLTAGE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 16, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI VOLTAGE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 16, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI TIMER 2 LATCH", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 24, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI TIMER 2 LATCH", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 24, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI LOW BSR", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 25, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI LOW BSR", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 25, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI PSU", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 26, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI PSU", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 26, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI SW TIMERS", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 27, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI SW TIMERS", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 27, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI MISC", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 28, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "elcMRI MISC", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 28, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI BCP", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 29, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI BCP", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 29, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI SVB HOLD1", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 30, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI SVB HOLD1", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 30, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI SVB HOLD2", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 31, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI SVB HOLD2", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 31, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI WSP HOLD1", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 32, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI WSP HOLD1", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 32, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI WSP HOLD2", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 33, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI WSP HOLD2", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 33, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI SVB VENT1", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 34, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI SVB VENT1", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 34, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI SVB VENT2", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 35, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI SVB VENT2", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 35, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI WSP VENT1", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 36, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI WSP VENT1", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 36, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI WSP VENT2", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 37, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI WSP VENT2", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 37, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI LINK VALVE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 38, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI LINK VALVE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 38, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI VLCP", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 39, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI VLCP", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 39, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI LEAKING", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 40, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI LEAKING", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 40, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI REMOTE RELE ASE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 41, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI REMOTE RELE ASE", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 41, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI DRAGGING BR", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 42, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "airMRI DRAGGING BR", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 155, "bit_offset": 42, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Time To Test", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 215, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Time To Test", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 215, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Axle 1 Tacho (MSB)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 216, "bit_offset": 2, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "Axle 1 Tacho (MSB)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 216, "bit_offset": 2, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "Axle 1 Tacho (LSB)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 226, "bit_offset": 2, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "Axle 1 Tacho (LSB)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 226, "bit_offset": 2, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "Axle 2 Tacho (MSB)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 236, "bit_offset": 2, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "Axle 2 Tacho (MSB)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 236, "bit_offset": 2, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "Axle 2 Tacho (LSB)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 246, "bit_offset": 2, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "Axle 2 Tacho (LSB)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 246, "bit_offset": 2, "scaling": 1.0, "unit": "int", "element_count": 1}, {"name": "Tacho Status", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 256, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Tacho Status", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 256, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Tacho Health", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 258, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Tacho Health", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 2, "storage_period": 2000, "byte_offset": 258, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Virtual WSP Segment", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 261, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Virtual WSP Segment", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 261, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "GS Master Fail Only", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 262, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "GS Master Fail Only", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 262, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Self-test Active", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 263, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Self-test Active", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 263, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Max Deceleration", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 265, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Max Deceleration", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 265, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Super Inlet", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 266, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Super Inlet", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 266, "bit_offset": 2, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "GS Secondary Master", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 267, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "GS Secondary Master", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 267, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "GS Test Master", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 268, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "GS Test Master", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 268, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "ROCOGS", "message_type": "RBX_GS_MASTER", "node_types": ["GSM"], "node_count": 4, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 270, "bit_offset": 0, "scaling": 1.0, "unit": "mm/s^2", "element_count": 1}, {"name": "Ground Speed", "message_type": "RBX_GS_MASTER", "node_types": ["GSM"], "node_count": 4, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 278, "bit_offset": 0, "scaling": 0.0072, "unit": "km/h", "element_count": 1}, {"name": "Odometer 1km", "message_type": "RBX_GS_MASTER", "node_types": ["GSM"], "node_count": 4, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 286, "bit_offset": 0, "scaling": 1.0, "unit": "km", "element_count": 1}, {"name": "Current Loop Fault", "message_type": "RIO_INPUTS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 286, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Current Loop Fault", "message_type": "RIO_INPUTS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 286, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Current Loop Output", "message_type": "RIO_SET_OUTPUTS", "node_types": ["BCU"], "node_count": 10, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 287, "bit_offset": 6, "scaling": 0.001, "unit": "mA", "element_count": 1}, {"name": "Maintenance Event ID1", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 7, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID1", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 7, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID2", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 8, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID2", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 8, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID3", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 9, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID3", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 9, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID4", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 10, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID4", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 10, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID5", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 11, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID5", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 11, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID6", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 12, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID6", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 12, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID7", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 13, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID7", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 13, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID8", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 14, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID8", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 14, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID9", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 15, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID9", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 15, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID10", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 16, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID10", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 16, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID11", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 17, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID11", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 17, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID12", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 18, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID12", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 18, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID13", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 19, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID13", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 19, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID14", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 20, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID14", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 20, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID15", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 21, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID15", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 21, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID16", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 22, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID16", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 22, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID17", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 23, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID17", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 23, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID18", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 24, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID18", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 24, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID19", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 25, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID19", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 25, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID20", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 26, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID20", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 26, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID21", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 27, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID21", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 27, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID22", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 28, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID22", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 28, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID23", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 29, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID23", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 29, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID24", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 30, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID24", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 30, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID25", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 31, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID25", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 31, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID26", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 32, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID26", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 32, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID27", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 33, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID27", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 33, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID28", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 34, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID28", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 34, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID29", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 35, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID29", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 35, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID30", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 36, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID30", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 36, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID31", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 37, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID31", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 37, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID32", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 38, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID32", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 38, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID33", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 39, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID33", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 39, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID34", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 40, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID34", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 40, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID35", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 41, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID35", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 41, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID36", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 42, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID36", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 42, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID37", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 43, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID37", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 43, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID38", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 44, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID38", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 44, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID39", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 45, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID39", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 45, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID40", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 46, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID40", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 46, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID41", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 47, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID41", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 47, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID42", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 48, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID42", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 48, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID43", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 49, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID43", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 49, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID44", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 50, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID44", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 50, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID45", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 51, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID45", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 51, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID46", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 52, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID46", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 52, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID47", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 53, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID47", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 53, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID48", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 54, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Maintenance Event ID48", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 307, "bit_offset": 54, "scaling": 1.0, "unit": "hex", "element_count": 48}, {"name": "Emergency Level Select", "message_type": "Not in CAN Link Specification", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 367, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Emergency Level Select", "message_type": "Not in CAN Link Specification", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 2000, "byte_offset": 367, "bit_offset": 6, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Data Logger Board Temperature", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 369, "bit_offset": 0, "scaling": 1.0, "unit": "C", "element_count": 1}, {"name": "Data Logger CPU Temperature", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 370, "bit_offset": 0, "scaling": 1.0, "unit": "C", "element_count": 1}, {"name": "ASP 1", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 10, "storage_period": 1000, "byte_offset": 400, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "ASP 1", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 10, "storage_period": 1000, "byte_offset": 400, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "ASP 2", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 10, "storage_period": 1000, "byte_offset": 425, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "ASP 2", "message_type": "RBX_ASP_AUXILIARY", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 10, "storage_period": 1000, "byte_offset": 425, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "Bogie (Axle 1) Target BCP", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 16, "storage_period": 1000, "byte_offset": 450, "bit_offset": 0, "scaling": 0.001, "unit": "bar", "element_count": 1}, {"name": "Bogie (Axle 1) Target BCP", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 16, "storage_period": 1000, "byte_offset": 450, "bit_offset": 0, "scaling": 0.001, "unit": "bar", "element_count": 1}, {"name": "Bogie (Axle 2) Target BCP", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 16, "storage_period": 1000, "byte_offset": 490, "bit_offset": 0, "scaling": 0.001, "unit": "bar", "element_count": 1}, {"name": "Bogie (Axle 2) Target BCP", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 16, "storage_period": 1000, "byte_offset": 490, "bit_offset": 0, "scaling": 0.001, "unit": "bar", "element_count": 1}, {"name": "Ramp Rate (Bogie/Axle 1)", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 530, "bit_offset": 0, "scaling": 0.1, "unit": "bar/s", "element_count": 1}, {"name": "Ramp Rate (Bogie/Axle 1)", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 530, "bit_offset": 0, "scaling": 0.1, "unit": "bar/s", "element_count": 1}, {"name": "Ramp Rate (Bogie/Axle 2)", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 550, "bit_offset": 0, "scaling": 0.1, "unit": "bar/s", "element_count": 1}, {"name": "Ramp Rate (Bogie/Axle 2)", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 550, "bit_offset": 0, "scaling": 0.1, "unit": "bar/s", "element_count": 1}, {"name": "WSP Inhibit", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 570, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "WSP Inhibit", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 570, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Bogie Control", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 572, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Bogie Control", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 572, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Slip Axle 1", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 575, "bit_offset": 0, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "Slip Axle 1", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 575, "bit_offset": 0, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "Slip Axle 2", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 595, "bit_offset": 0, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "Slip Axle 2", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 595, "bit_offset": 0, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "Wheel-slide Enabled", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 2, "storage_period": 1000, "byte_offset": 615, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Wheel-slide Enabled", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 2, "storage_period": 1000, "byte_offset": 615, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Residual Pressure Switches Axle 1", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 620, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Residual Pressure Switches Axle 1", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 620, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Residual Pressure Switches Axle 2", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 622, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Residual Pressure Switches Axle 2", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 622, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Remote Release Demand", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 625, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Remote Release Demand", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 625, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Axle Speed Out of Range (Axle 1)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 627, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Axle Speed Out of Range (Axle 1)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 627, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Axle Speed Out of Range (Axle 2)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 630, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Axle Speed Out of Range (Axle 2)", "message_type": "RBX_TACHO_FREQUENCY_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 630, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Valve Timeout Status", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 2, "storage_period": 1000, "byte_offset": 632, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Valve Timeout Status", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 2, "storage_period": 1000, "byte_offset": 632, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Wheel-slip Indicator", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 637, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Wheel-slip Indicator", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 637, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "MVB Interface Working", "message_type": "EXTENDED_BM_BRAKE_STATUS", "node_types": ["BCU"], "node_count": 2, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 640, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "PWM 1 input", "message_type": "RIO_INPUTS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 640, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "PWM 1 input", "message_type": "RIO_INPUTS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 640, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "PWM 2 input", "message_type": "RIO_INPUTS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 660, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "PWM 2 input", "message_type": "RIO_INPUTS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 660, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "PWM 3 input", "message_type": "RIO_INPUTS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 680, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "PWM 3 input", "message_type": "RIO_INPUTS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 680, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "PWM Output 1", "message_type": "RIO_SET_OUTPUTS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 700, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "PWM Output 1", "message_type": "RIO_SET_OUTPUTS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 700, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "PWM Output 2", "message_type": "RIO_SET_OUTPUTS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 720, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "PWM Output 2", "message_type": "RIO_SET_OUTPUTS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 720, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "PWM Output 3", "message_type": "RIO_SET_OUTPUTS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 740, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "PWM Output 3", "message_type": "RIO_SET_OUTPUTS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 1000, "byte_offset": 740, "bit_offset": 4, "scaling": 0.3921568627451, "unit": "%", "element_count": 1}, {"name": "System Conditions OK", "message_type": "RIO_SET_OUTPUTS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 760, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "System Conditions OK", "message_type": "RIO_SET_OUTPUTS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 760, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Prime", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 763, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Prime", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 763, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Emergency Level Confirmation", "message_type": "Not in CAN Link Specification", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 765, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Emergency Level Confirmation", "message_type": "Not in CAN Link Specification", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 1000, "byte_offset": 765, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Axle 1 Speed", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 16, "storage_period": 200, "byte_offset": 800, "bit_offset": 0, "scaling": 0.0072, "unit": "km/h", "element_count": 1}, {"name": "Axle 1 Speed", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 16, "storage_period": 200, "byte_offset": 800, "bit_offset": 0, "scaling": 0.0072, "unit": "km/h", "element_count": 1}, {"name": "Axle 2 Speed", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 16, "storage_period": 200, "byte_offset": 1000, "bit_offset": 0, "scaling": 0.0072, "unit": "km/h", "element_count": 1}, {"name": "Axle 2 Speed", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 16, "storage_period": 200, "byte_offset": 1000, "bit_offset": 0, "scaling": 0.0072, "unit": "km/h", "element_count": 1}, {"name": "RBX Valve States", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 5, "storage_period": 200, "byte_offset": 1200, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "RBX Valve States", "message_type": "RBX_WSP_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 5, "storage_period": 200, "byte_offset": 1200, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Brake Mode", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1262, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Brake Mode", "message_type": "RBX_BRAKE_DEMAND", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1262, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "BCP Axle 1", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 10, "storage_period": 200, "byte_offset": 1275, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "BCP Axle 1", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 10, "storage_period": 200, "byte_offset": 1275, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "BCP Axle 2", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 10, "storage_period": 200, "byte_offset": 1400, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "BCP Axle 2", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 10, "storage_period": 200, "byte_offset": 1400, "bit_offset": 0, "scaling": 0.01, "unit": "bar", "element_count": 1}, {"name": "Brakes <PERSON>", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1525, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Brakes <PERSON>", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1525, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Emergency Brak<PERSON>", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1537, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Emergency Brak<PERSON>", "message_type": "RBX_BRAKING_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1537, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "GS Test Status", "message_type": "RBX_GS_MASTER", "node_types": ["GSM"], "node_count": 4, "bits_per_item": 2, "storage_period": 200, "byte_offset": 1550, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Test Axle 1 status", "message_type": "RBX_GS_MASTER", "node_types": ["GSM"], "node_count": 4, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1560, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Test Axle 2 status", "message_type": "RBX_GS_MASTER", "node_types": ["GSM"], "node_count": 4, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1565, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Wheel-slip Indicator", "message_type": "RBX_GS_MASTER", "node_types": ["GSM"], "node_count": 4, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1570, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Odometer 100m", "message_type": "RBX_GS_MASTER", "node_types": ["GSM"], "node_count": 4, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1575, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "RBX Health Status", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-logical"], "node_count": 10, "bits_per_item": 3, "storage_period": 200, "byte_offset": 1580, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "RBX Health Status", "message_type": "RBX_MAINTENANCE_STATUS", "node_types": ["RBX-physical"], "node_count": 10, "bits_per_item": 3, "storage_period": 200, "byte_offset": 1580, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Prime Active", "message_type": "EXTENDED_BM_BRAKE_STATUS", "node_types": ["BCU"], "node_count": 2, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1617, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Auto-configuration Active", "message_type": "EXTENDED_BM_BRAKE_STATUS", "node_types": ["BCU"], "node_count": 2, "bits_per_item": 1, "storage_period": 200, "byte_offset": 1620, "bit_offset": 0, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Digital Inputs", "message_type": "RIO_INPUTS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 8, "storage_period": 200, "byte_offset": 1622, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Digital Inputs", "message_type": "RIO_INPUTS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 8, "storage_period": 200, "byte_offset": 1622, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Digital Outputs", "message_type": "RIO_SET_OUTPUTS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 4, "storage_period": 200, "byte_offset": 1722, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Digital Outputs", "message_type": "RIO_SET_OUTPUTS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 4, "storage_period": 200, "byte_offset": 1722, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Health Status", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-logical"], "node_count": 10, "bits_per_item": 3, "storage_period": 200, "byte_offset": 1772, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Health Status", "message_type": "RIO_MAINTENANCE_STATUS", "node_types": ["RIO-physical"], "node_count": 10, "bits_per_item": 3, "storage_period": 200, "byte_offset": 1772, "bit_offset": 4, "scaling": 1.0, "unit": "binary", "element_count": 1}, {"name": "Data Logger Calculated Ground Speed", "message_type": "(<PERSON> Logger)", "node_types": ["LOGGER"], "node_count": 1, "bits_per_item": 16, "storage_period": 200, "byte_offset": 1810, "bit_offset": 0, "scaling": 0.036, "unit": "km/h", "element_count": 1}, {"name": "Data Logger CH0 RX Error Count", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 32, "storage_period": 2000, "byte_offset": 1830, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH0 TX Error Count", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 32, "storage_period": 2000, "byte_offset": 1834, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH0 none", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1838, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH0 stuff", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1840, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH0 form", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1842, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH0 ACK", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1844, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH0 bit1", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1846, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH0 bit0", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1848, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH0 CRC", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1850, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH0 CPU write", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1852, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH1 RX Error Count", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 32, "storage_period": 2000, "byte_offset": 1854, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH1 TX Error Count", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 32, "storage_period": 2000, "byte_offset": 1858, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH1 none", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1862, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH1 stuff", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1864, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH1 form", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1866, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH1 ACK", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1868, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH1 bit1", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1870, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH1 bit0", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1872, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH1 CRC", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1874, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH1 CPU write", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1876, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH2 RX Error Count", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 32, "storage_period": 2000, "byte_offset": 1878, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH2 TX Error Count", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 32, "storage_period": 2000, "byte_offset": 1882, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH2 none", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1886, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH2 stuff", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1888, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH2 form", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1890, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH2 ACK", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1892, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH2 bit1", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1894, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH2 bit0", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1896, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH2 CRC", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1898, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH2 CPU write", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1900, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH3 RX Error Count", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 32, "storage_period": 2000, "byte_offset": 1902, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH3 TX Error Count", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 32, "storage_period": 2000, "byte_offset": 1906, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH3 none", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1910, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH3 stuff", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1912, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH3 form", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1914, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH3 ACK", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1916, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH3 bit1", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1918, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH3 bit0", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1920, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH3 CRC", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1922, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger CH3 CPU write", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 16, "storage_period": 2000, "byte_offset": 1924, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger Current CAN Loading", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 1926, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger Average CAN Loading", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 1927, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}, {"name": "Data Logger Peak CAN Loading", "message_type": "(<PERSON> Logger)", "node_types": ["SYSTEM"], "node_count": 1, "bits_per_item": 8, "storage_period": 2000, "byte_offset": 1928, "bit_offset": 0, "scaling": 1.0, "unit": "", "element_count": 1}]