#!/usr/bin/env python3
"""
C扩展模块单独测试脚本
用于测试binfile_parser.parse_bin_file()函数

使用方法:
    python3 test.py
"""

import os
import sys
import json
import time

def load_signals_from_tmpconf():
    """从tmpConf文件加载信号配置"""
    try:
        with open('tmpConf', 'r', encoding='utf-8') as f:
            signals_list = json.load(f)
        print(f"成功加载信号配置: {len(signals_list)} 个信号")
        return signals_list
    except Exception as e:
        print(f"加载信号配置失败: {e}")
        return None

def calculate_packet_count(bin_file):
    """计算文件中的数据包总数"""
    EP2002_LOG_PACKET_SIZE = 2048
    file_size = os.path.getsize(bin_file)
    if file_size < 64 + EP2002_LOG_PACKET_SIZE:
        return 0
    actual_data_size = file_size - 64  # 减去文件头
    packet_count = actual_data_size // EP2002_LOG_PACKET_SIZE
    return packet_count

def process_file_segment_with_c_extension(bin_file, start_offset, packet_count, signals_list, verbose):
    """处理文件片段 - 调用C扩展处理指定偏移量和数量的数据包"""
    import time
    process_start = time.time()
    
    print(f'子进程开始: offset={start_offset}, count={packet_count}')
    
    try:
        import binfile_parser
        
        # 调用修改后的C扩展函数
        result = binfile_parser.parse_bin_file_segment(bin_file, start_offset, packet_count, signals_list, verbose)
        
        process_end = time.time()
        print(f'子进程完成: offset={start_offset}, 耗时={process_end - process_start:.3f}秒')
        
        return result
        
    except Exception as e:
        print(f"子进程处理失败: {e}")
        return None

def merge_parse_results(results):
    """合并多个解析结果"""
    if not results or len(results) == 0:
        return None
    
    # 过滤掉None结果
    valid_results = [r for r in results if r is not None]
    if not valid_results:
        return None
    
    # 使用第一个结果作为基础
    merged_result = {
        'header': valid_results[0]['header'],  # 文件头都相同，取第一个
        'records': [],
        'signal_data': {}
    }
    
    # 合并所有记录
    for result in valid_results:
        if 'records' in result:
            merged_result['records'].extend(result['records'])
    
    # 合并信号数据
    for result in valid_results:
        if 'signal_data' in result:
            for signal_name, timestamps in result['signal_data'].items():
                if signal_name not in merged_result['signal_data']:
                    merged_result['signal_data'][signal_name] = {}
                merged_result['signal_data'][signal_name].update(timestamps)
    
    print(f"合并完成: 总记录数={len(merged_result['records'])}, 信号数={len(merged_result['signal_data'])}")
    return merged_result

def test_multiprocess_parsing():
    """测试混合多进程+C扩展解析"""
    print("\n" + "=" * 60)
    print("混合多进程+C扩展解析测试")
    print("=" * 60)
    
    bin_file = "./LOG00741.BIN"
    if not os.path.exists(bin_file):
        print(f"跳过多进程测试: 文件不存在 {bin_file}")
        return False
    
    # 加载信号配置
    signals_list = load_signals_from_tmpconf()
    if not signals_list:
        print("警告: 未能加载信号配置")
        return False
    
    try:
        import billiard as mp
        
        # 检查文件大小
        file_size = os.path.getsize(bin_file)
        print(f"测试文件: {bin_file}")
        print(f"文件大小: {file_size / 1024:.2f} KB")
        
        # 计算数据包总数和分片策略
        total_packet_count = calculate_packet_count(bin_file)
        if total_packet_count <= 0:
            print("错误: 文件太小或格式错误")
            return False
        
        print(f"总数据包数量: {total_packet_count}")
        
        # 创建进程池 - 使用4个进程进行测试
        num_cores = min(4, total_packet_count)  # 进程数不超过数据包数
        print(f'创建进程池，进程数: {num_cores}')
        
        pool_start_time = time.time()
        pool = mp.Pool(num_cores)
        
        # 计算每个进程处理的数据包数量
        packets_per_process = max(1, total_packet_count // num_cores)
        
        async_results = []
        for i in range(num_cores):
            start_packet = i * packets_per_process
            
            # 最后一个进程处理剩余的所有数据包
            if i == num_cores - 1:
                packet_count = total_packet_count - start_packet
            else:
                packet_count = packets_per_process
            
            # 计算文件偏移量（64字节文件头 + 数据包偏移）
            EP2002_LOG_PACKET_SIZE = 2048
            start_offset = 64 + start_packet * EP2002_LOG_PACKET_SIZE
            
            print(f"进程{i}: 起始数据包={start_packet}, 数量={packet_count}, 偏移量={start_offset}")
            
            async_result = pool.apply_async(
                process_file_segment_with_c_extension,
                args=(bin_file, start_offset, packet_count, signals_list, 0)  # verbose=0
            )
            async_results.append(async_result)
        
        # 关闭进程池
        pool.close()
        pool.join()
        
        pool_end_time = time.time()
        print(f"所有进程完成，耗时: {pool_end_time - pool_start_time:.2f}秒")
        
        # 收集结果
        results = []
        for i, async_result in enumerate(async_results):
            try:
                result = async_result.get()
                if result:
                    results.append(result)
                    print(f"进程{i}结果: 记录数={len(result.get('records', []))}, 信号数={len(result.get('signal_data', {}))}")
                    
                    # 打印第一个信号的详细信息
                    signal_data = result.get('signal_data', {})
                    if signal_data:
                        signal_names = list(signal_data.keys())
                        
                        # 查看多个信号，特别是可能变化的信号
                        signals_to_check = ['Physical Car Code', 'Auxiliary Pressure 1', 'Auxiliary Pressure 2']
                        
                        for signal_name in signals_to_check:
                            if signal_name in signal_data:
                                signal_timestamps = signal_data[signal_name]
                                print(f"  进程{i} - 信号 {signal_name}:")
                                print(f"    时间戳数量: {len(signal_timestamps)}")
                                
                                # 显示前2个时间戳的值
                                timestamps = list(signal_timestamps.keys())[:2]
                                for timestamp in timestamps:
                                    values = signal_timestamps[timestamp]
                                    print(f"    {timestamp}: {values}")
                                    
                                    # 特别关注数值信号的变化
                                    if isinstance(values, list) and len(values) > 0:
                                        if isinstance(values[0], list) and len(values[0]) > 0:
                                            first_values = values[0][:3]  # 只看前3个值
                                            print(f"      前3个数值: {first_values}")
                                print()
                else:
                    print(f"进程{i}返回空结果")
            except Exception as e:
                print(f"获取进程{i}结果失败: {e}")
        
        # 如果所有进程都有结果，合并并显示最终结果
        if results:
            print("\n=== 开始合并结果 ===")
            final_result = merge_parse_results(results)
            
            if final_result:
                print("\n=== 最终合并结果分析 ===")
                print(f"合并后总记录数: {len(final_result.get('records', []))}")
                print(f"合并后总信号数: {len(final_result.get('signal_data', {}))}")
                
                # 分析合并后的第一个信号
                merged_signal_data = final_result.get('signal_data', {})
                if merged_signal_data:
                    signal_names = list(merged_signal_data.keys())
                    if signal_names:
                        first_signal_name = signal_names[0]
                        # first_signal_data = merged_signal_data[first_signal_name]
                        # print(f"\n=== 合并后第一个信号详细信息 ===")
                        
                        first_signal_data = merged_signal_data["Brake Mode"]
                        # print(f"\n=== 合并后 信号Brake Mode 的详细信息 ===")
                        
                        print(f"信号名称: {first_signal_name}")
                        print(f"数据类型: {type(first_signal_data)}")
                        
                        if isinstance(first_signal_data, dict):
                            print(f"总时间戳数量: {len(first_signal_data)}")
                            
                            # 显示前5个时间戳
                            timestamps = list(first_signal_data.keys())[:5]
                            print(f"前5个时间戳: {timestamps}")
                            
                            for i, timestamp in enumerate(timestamps):
                                values = first_signal_data[timestamp]
                                print(f"[{i+1}] {timestamp}:")
                                print(f"    数据: {values}")
                                print(f"    类型: {type(values)}")
                                
                                if isinstance(values, list) and len(values) > 0:
                                    print(f"    数组长度: {len(values)}")
                                    print(f"    第一个元素: {values[0]}")
                                    if isinstance(values[0], list):
                                        print(f"    嵌套数组: {values[0]}")
                                        print(f"    嵌套长度: {len(values[0])}")
                                        # 显示每个节点的值
                                        for node_idx, node_value in enumerate(values[0]):
                                            print(f"      节点{node_idx}: {node_value}")
            
            print("✓ 多进程测试成功完成")
            return True
        else:
            print("✗ 所有进程都失败")
            return False
            
    except ImportError:
        print("✗ billiard模块未安装，跳过多进程测试")
        print("安装命令: pip install billiard")
        return False
    except Exception as e:
        print(f"✗ 多进程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_c_extension_segment_function():
    """测试C扩展的分片功能"""
    print("\n" + "=" * 60)
    print("C扩展分片功能测试")
    print("=" * 60)
    
    bin_file = "./LOG00741.BIN"
    if not os.path.exists(bin_file):
        print(f"跳过分片测试: 文件不存在 {bin_file}")
        return False
    
    signals_list = load_signals_from_tmpconf()
    
    try:
        import binfile_parser
        
        # 检查分片函数是否存在
        if not hasattr(binfile_parser, 'parse_bin_file_segment'):
            print("✗ parse_bin_file_segment函数不存在")
            return False
        
        print("✓ parse_bin_file_segment函数可用")
        
        # 计算总数据包数
        total_packets = calculate_packet_count(bin_file)
        print(f"总数据包数: {total_packets}")
        
        # 测试分片解析：取前一半数据包
        test_packets = min(total_packets // 2, 100)  # 最多100个数据包用于测试
        start_offset = 64  # 文件头之后
        
        print(f"测试分片: 偏移={start_offset}, 数据包数={test_packets}")
        
        start_time = time.time()
        result = binfile_parser.parse_bin_file_segment(
            bin_file, 
            start_offset, 
            test_packets, 
            signals_list, 
            1  # verbose
        )
        end_time = time.time()
        
        print(f"分片解析耗时: {end_time - start_time:.2f}秒")
        
        if result:
            print("✓ 分片解析成功")
            print(f"  记录数: {len(result.get('records', []))}")
            print(f"  信号数: {len(result.get('signal_data', {}))}")
            
            # 验证header信息
            if 'header' in result:
                header = result['header']
                print(f"  文件头: {type(header)}")
                if isinstance(header, dict) and 'sw_version_major' in header:
                    print(f"  软件版本: {header['sw_version_major']}.{header.get('sw_version_minor', 0)}")
            
            return True
        else:
            print("✗ 分片解析返回空结果")
            return False
            
    except Exception as e:
        print(f"✗ 分片测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_binfile_parser():
    """测试C扩展模块"""
    print("=" * 60)
    print("C扩展模块单独测试")
    print("=" * 60)
    
    # 检查文件是否存在
    bin_file = "./LOG00741.BIN"
    if not os.path.exists(bin_file):
        print(f"错误: 二进制文件不存在: {bin_file}")
        return False
    
    # 获取文件大小
    file_size = os.path.getsize(bin_file)
    print(f"测试文件: {bin_file}")
    print(f"文件大小: {file_size / 1024:.2f} KB")
    
    # 加载信号配置
    signals_list = load_signals_from_tmpconf()
    if not signals_list:
        print("警告: 未能加载信号配置，将使用空信号列表")
        signals_list = None
    
    try:
        # 导入C扩展模块
        print("导入C扩展模块...")
        import binfile_parser
        print("✓ C扩展模块导入成功")
        
        # 检查模块函数
        if hasattr(binfile_parser, 'parse_bin_file'):
            print("✓ parse_bin_file函数可用")
        else:
            print("✗ parse_bin_file函数不可用")
            return False
        
        # 开始测试
        print("\n开始解析测试...")
        start_time = time.time()
        
        # 调用C扩展模块
        result = binfile_parser.parse_bin_file(
            bin_file,           # 二进制文件路径
            signals_list,       # 信号配置列表
            1                   # verbose模式 (1=开启, 0=关闭)
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✓ 解析完成，耗时: {processing_time:.2f}秒")
        
        # 分析结果
        if result:
            print("\n解析结果分析:")
            print(f"结果类型: {type(result)}")
            
            if isinstance(result, dict):
                print(f"结果包含的键: {list(result.keys())}")
                
                # 检查header信息
                if 'header' in result:
                    header = result['header']
                    print(f"文件头信息: {type(header)}")
                    if hasattr(header, '__dict__'):
                        print(f"  属性: {list(header.__dict__.keys())}")
                    elif isinstance(header, dict):
                        print(f"  键: {list(header.keys())}")
                
                # 检查records
                if 'records' in result:
                    records = result['records']
                    print(f"记录数量: {len(records) if records else 0}")
                    if records and len(records) > 0:
                        print(f"第一个记录的键: {list(records[0].keys())}")
                
                # 检查signal_data
                if 'signal_data' in result:
                    signal_data = result['signal_data']
                    print(f"信号数据: {type(signal_data)}")
                    if isinstance(signal_data, dict):
                        print(f"信号数量: {len(signal_data)}")
                        # 显示前几个信号名称
                        signal_names = list(signal_data.keys())[:5]
                        print(f"前5个信号: {signal_names}")

                        # 显示第一个信号的数据值
                        if signal_names and len(signal_names) > 1:
                            first_signal_name = signal_names[1]
                            first_signal_data = signal_data[first_signal_name]
                            print(f"\n第一个信号详细数据: {first_signal_name}")
                            print(f"  数据类型: {type(first_signal_data)}")
                            
                            if isinstance(first_signal_data, dict):
                                print(f"  时间戳数量: {len(first_signal_data)}")
                                
                                # 显示前几个时间戳和对应的值
                                timestamps = list(first_signal_data.keys())[:3]
                                print(f"  前3个时间戳: {timestamps}")
                                
                                for i, timestamp in enumerate(timestamps):
                                    values = first_signal_data[timestamp]
                                    print(f"  [{i+1}] {timestamp}: {values} (类型: {type(values)})")
                                    
                                    # 如果是列表或数组，显示长度
                                    if isinstance(values, (list, tuple)):
                                        print(f"      节点数量: {len(values)}")
                                        if len(values) > 0:
                                            print(f"      第一个节点值: {values[0]}")
                            
                            elif isinstance(first_signal_data, (list, tuple)):
                                print(f"  数据点数量: {len(first_signal_data)}")
                                if len(first_signal_data) > 0:
                                    print(f"  第一个数据点: {first_signal_data[0]}")
                            
                            else:
                                print(f"  数据内容: {first_signal_data}")
            
            print("✓ 测试成功完成")
            return True
        else:
            print("✗ 解析返回空结果")
            return False
            
    except ImportError as e:
        print(f"✗ 导入C扩展模块失败: {e}")
        print("请确保已正确编译C扩展模块")
        return False
    except Exception as e:
        print(f"✗ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_monitoring():
    """测试内存监控功能"""
    print("\n" + "=" * 60)
    print("内存监控测试")
    print("=" * 60)
    
    try:
        import psutil
        
        # 获取当前进程
        current_process = psutil.Process()
        
        print(f"测试前内存使用: {current_process.memory_info().rss / 1024 / 1024:.2f} MB")
        
        # 运行测试
        success = test_binfile_parser()
        
        print(f"测试后内存使用: {current_process.memory_info().rss / 1024 / 1024:.2f} MB")
        
        return success
        
    except ImportError:
        print("psutil未安装，跳过内存监控")
        return test_binfile_parser()

def performance_test():
    """性能测试"""
    print("\n" + "=" * 60)
    print("性能测试")
    print("=" * 60)
    
    bin_file = "./LOG00741.BIN"
    if not os.path.exists(bin_file):
        print(f"跳过性能测试: 文件不存在 {bin_file}")
        return
    
    signals_list = load_signals_from_tmpconf()
    
    try:
        import binfile_parser
        
        # 多次运行取平均值
        run_times = []
        for i in range(1):
            print(f"性能测试运行 {i+1}/1...")
            start_time = time.time()
            
            result = binfile_parser.parse_bin_file(bin_file, signals_list, 0)  # 关闭verbose
            
            end_time = time.time()
            run_time = end_time - start_time
            run_times.append(run_time)
            print(f"  运行时间: {run_time:.2f}秒")
        
        avg_time = sum(run_times) / len(run_times)
        min_time = min(run_times)
        max_time = max(run_times)
        
        print(f"\n性能测试结果:")
        print(f"  平均时间: {avg_time:.2f}秒")
        print(f"  最快时间: {min_time:.2f}秒")
        print(f"  最慢时间: {max_time:.2f}秒")
        
        # 计算处理速度
        file_size = os.path.getsize(bin_file)
        speed_mb_s = (file_size / 1024 / 1024) / avg_time
        print(f"  处理速度: {speed_mb_s:.2f} MB/秒")
        
    except Exception as e:
        print(f"性能测试失败: {e}")

def main():
    """主函数"""
    print("EP2002 C扩展模块测试程序 (包含billiard多进程测试)")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查必要文件
    required_files = [
        'binfile_parser.cpython-310-x86_64-linux-gnu.so',  # C扩展模块
        'tmpConf',                                           # 信号配置
        'LOG00741.BIN'                                      # 测试文件
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"\n警告: 以下文件不存在:")
        for file in missing_files:
            print(f"  - {file}")
        
        if 'binfile_parser.cpython-310-x86_64-linux-gnu.so' in missing_files:
            print("\n请先运行 ./build.sh 编译C扩展模块")
            return False
    
    print("\n开始测试...")
    
    # 运行基本测试
    success = test_memory_monitoring()
    
    if success:
        # 运行C扩展分片功能测试
        test_c_extension_segment_function()
        
        # 运行多进程测试
        test_multiprocess_parsing()
        
        # 运行性能测试
        # performance_test()
        
        print("\n" + "=" * 60)
        print("✓ 所有测试完成")
        print("=" * 60)
        return True
    else:
        print("\n" + "=" * 60)
        print("✗ 测试失败")
        print("=" * 60)
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试程序异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
