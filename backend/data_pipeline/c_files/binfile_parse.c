// 此 C 文件解析 EP2002-Datalogger - Lato - .BIN文件, 生成可执行文件 ./binfile_parse
// 编译命令:
//      gcc -o binfile_parse binfile_parse.c -lcjson -I/usr/include/cjson
//      chmod +x binfile_parse
// 运行命令:
//      ./binfile_parse ./LOG00741.BIN ./tmpConf true

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <time.h>
#include <stdbool.h>
#include <cjson/cJSON.h>

// 常量定义
#define EP2002_LOG_FILE_ID 0xDEAD
#define EP2002_LOG_PACKET_SIZE 2048
#define EP2002_SECONDS_PER_SAMPLE 2
#define EP2002_MILLISECONDS_PER_SAMPLE (EP2002_SECONDS_PER_SAMPLE * 1000)
#define MAX_SIGNALS 1000
#define MAX_TIMESTAMP_CACHE 10000
#define BATCH_READ_SIZE 10  // 批量读取数据包数量

// 全局变量
static bool verbose = false;

// 数据结构定义
typedef struct {
    uint16_t block_id;
    uint16_t file_flags;
    uint8_t sw_version_major;
    uint8_t sw_version_minor;
    uint16_t sw_version_stroke;
    uint32_t timestamp;
    uint32_t sequence;
    uint32_t data_size;
    uint64_t dallas_id;
    int32_t session_id;
} FileHeader;

typedef struct {
    uint16_t record_id;
    uint8_t packet_version_major;
    uint8_t packet_version_minor;
    int32_t sample_cycles;
    int32_t session_sequence;
    int32_t timestamp;
} RecordHeader;

typedef struct {
    char* name;
    char* message_type;
    char** node_types;
    int node_types_count;
    int node_count;
    int bits_per_item;
    int storage_period;
    int byte_offset;
    int bit_offset;
    double scaling;
    char* unit;
    int element_count;
} Signal;

// 缓存结构定义
typedef struct {
    char timestamp_str[30];
    time_t timestamp_value;
} TimestampCache;

typedef struct {
    char* signal_name;
    cJSON* signal_timestamps;
    bool processed;
} SignalLookup;

// 全局缓存变量
static TimestampCache timestamp_cache[MAX_TIMESTAMP_CACHE];
static int timestamp_cache_size = 0;
static SignalLookup signal_lookup[MAX_SIGNALS];
static int signal_lookup_size = 0;

// 函数声明
void log_error(const char* message);
void log_debug(const char* message, bool verbose);
uint32_t extract_bits(const uint8_t* data, int bit_offset, int bit_count);
char* format_timestamp(uint32_t timestamp);
const char* fast_format_timestamp(time_t timestamp);
Signal* load_signals_from_json(const char* json_file, int* signal_count, bool verbose);
void build_signal_lookup(cJSON *signal_data_json, Signal* signals, int signal_count);
cJSON* fast_get_signal_timestamps(const char* signal_name);
void process_signal_data_optimized(Signal* signals, int signal_count, 
                                  const uint8_t* packet_data, time_t start_time);
void process_packets_batch(FILE *file, int start_packet, int batch_size, 
                          cJSON *records_json, Signal* signals, int signal_count);
int custom_cJSON_GetObjectSize(cJSON *object);

// 日志函数
void log_error(const char* message) {
    fprintf(stderr, "错误: %s\n", message);
}

void log_debug(const char* message, bool verbose) {
    if (verbose) {
        fprintf(stderr, "调试: %s\n", message);
    }
}

// 工具函数
uint32_t extract_bits(const uint8_t* data, int bit_offset, int bit_count) {
    uint32_t result = 0;
    int byte_pos = bit_offset / 8;
    int bit_pos = bit_offset % 8;
    
    for (int i = 0; i < bit_count; i++) {
        int current_byte = byte_pos + (bit_pos + i) / 8;
        int current_bit = (bit_pos + i) % 8;
        
        if ((data[current_byte] >> current_bit) & 0x01) {
            result |= (1 << i);
        }
    }
    
    return result;
}

char* format_timestamp(uint32_t timestamp) {
    time_t t = (time_t)timestamp;
    struct tm *tm_info = gmtime(&t);
    
    static char buffer[30];
    strftime(buffer, 30, "%Y-%m-%d %H:%M:%S", tm_info);
    
    return buffer;
}

// 优化：快速时间戳格式化（使用缓存）
const char* fast_format_timestamp(time_t timestamp) {
    // 检查缓存
    for (int i = 0; i < timestamp_cache_size; i++) {
        if (timestamp_cache[i].timestamp_value == timestamp) {
            return timestamp_cache[i].timestamp_str;
        }
    }
    
    // 如果缓存未满，添加新的时间戳
    if (timestamp_cache_size < MAX_TIMESTAMP_CACHE) {
        struct tm *tm_info = gmtime(&timestamp);
        strftime(timestamp_cache[timestamp_cache_size].timestamp_str, 30, 
                "%Y-%m-%d %H:%M:%S", tm_info);
        timestamp_cache[timestamp_cache_size].timestamp_value = timestamp;
        return timestamp_cache[timestamp_cache_size++].timestamp_str;
    }
    
    // 缓存已满，使用静态缓冲区
    static char buffer[30];
    struct tm *tm_info = gmtime(&timestamp);
    strftime(buffer, 30, "%Y-%m-%d %H:%M:%S", tm_info);
    return buffer;
}

int custom_cJSON_GetObjectSize(cJSON *object) {
    if (!object) return 0;
    
    int size = 0;
    cJSON *child = object->child;
    while (child) {
        size++;
        child = child->next;
    }
    return size;
}

// 优化：预构建信号查找表
void build_signal_lookup(cJSON *signal_data_json, Signal* signals, int signal_count) {
    signal_lookup_size = 0;
    
    for (int i = 0; i < signal_count && signal_lookup_size < MAX_SIGNALS; i++) {
        Signal *signal = &signals[i];
        
        // 检查是否已存在
        bool exists = false;
        for (int j = 0; j < signal_lookup_size; j++) {
            if (strcmp(signal_lookup[j].signal_name, signal->name) == 0) {
                exists = true;
                break;
            }
        }
        
        if (!exists) {
            signal_lookup[signal_lookup_size].signal_name = signal->name;
            
            // 创建或获取信号时间戳对象
            cJSON *signal_timestamps = cJSON_GetObjectItem(signal_data_json, signal->name);
            if (!signal_timestamps) {
                signal_timestamps = cJSON_CreateObject();
                cJSON_AddItemToObject(signal_data_json, signal->name, signal_timestamps);
            }
            
            signal_lookup[signal_lookup_size].signal_timestamps = signal_timestamps;
            signal_lookup[signal_lookup_size].processed = false;
            signal_lookup_size++;
        }
    }
}

// 优化：快速信号查找
cJSON* fast_get_signal_timestamps(const char* signal_name) {
    for (int i = 0; i < signal_lookup_size; i++) {
        if (strcmp(signal_lookup[i].signal_name, signal_name) == 0) {
            return signal_lookup[i].signal_timestamps;
        }
    }
    return NULL;
}

// 信号配置加载
Signal* load_signals_from_json(const char* json_file, int* signal_count, bool verbose) {
    FILE* f = fopen(json_file, "r");
    if (!f) {
        log_error("无法打开信号配置文件");
        return NULL;
    }
    
    // 读取文件内容
    fseek(f, 0, SEEK_END);
    long file_size = ftell(f);
    fseek(f, 0, SEEK_SET);
    
    char* json_content = (char*)malloc(file_size + 1);
    if (!json_content) {
        log_error("内存分配失败");
        fclose(f);
        return NULL;
    }
    
    size_t bytes_read = fread(json_content, 1, file_size, f);
    fclose(f);
    
    if (bytes_read != (size_t)file_size) {
        log_error("读取信号配置文件失败");
        free(json_content);
        return NULL;
    }
    
    json_content[file_size] = '\0';
    
    // 解析JSON
    cJSON* json = cJSON_Parse(json_content);
    free(json_content);
    
    if (!json) {
        log_error("解析信号JSON失败");
        return NULL;
    }
    
    *signal_count = cJSON_GetArraySize(json);
    if (*signal_count <= 0) {
        log_error("信号列表为空");
        cJSON_Delete(json);
        return NULL;
    }
    
    // 分配并解析信号数组
    Signal* signals = (Signal*)calloc(*signal_count, sizeof(Signal));
    if (!signals) {
        log_error("内存分配失败");
        cJSON_Delete(json);
        return NULL;
    }
    
    for (int i = 0; i < *signal_count; i++) {
        cJSON* signal_json = cJSON_GetArrayItem(json, i);
        
        // 解析信号字段
        cJSON* name = cJSON_GetObjectItem(signal_json, "name");
        cJSON* message_type = cJSON_GetObjectItem(signal_json, "message_type");
        cJSON* node_types = cJSON_GetObjectItem(signal_json, "node_types");
        cJSON* node_count = cJSON_GetObjectItem(signal_json, "node_count");
        cJSON* bits_per_item = cJSON_GetObjectItem(signal_json, "bits_per_item");
        cJSON* storage_period = cJSON_GetObjectItem(signal_json, "storage_period");
        cJSON* byte_offset = cJSON_GetObjectItem(signal_json, "byte_offset");
        cJSON* bit_offset = cJSON_GetObjectItem(signal_json, "bit_offset");
        cJSON* scaling = cJSON_GetObjectItem(signal_json, "scaling");
        cJSON* unit = cJSON_GetObjectItem(signal_json, "unit");
        cJSON* element_count = cJSON_GetObjectItem(signal_json, "element_count");
        
        // 设置信号字段值
        if (name && cJSON_IsString(name)) {
            signals[i].name = strdup(name->valuestring);
        }
        
        if (message_type && cJSON_IsString(message_type)) {
            signals[i].message_type = strdup(message_type->valuestring);
        }
        
        if (node_types && cJSON_IsArray(node_types)) {
            int type_count = cJSON_GetArraySize(node_types);
            signals[i].node_types = (char**)malloc(type_count * sizeof(char*));
            signals[i].node_types_count = type_count;
            
            for (int j = 0; j < type_count; j++) {
                cJSON* type = cJSON_GetArrayItem(node_types, j);
                if (type && cJSON_IsString(type)) {
                    signals[i].node_types[j] = strdup(type->valuestring);
                }
            }
        }
        
        if (node_count && cJSON_IsNumber(node_count)) {
            signals[i].node_count = node_count->valueint;
        }
        
        if (bits_per_item && cJSON_IsNumber(bits_per_item)) {
            signals[i].bits_per_item = bits_per_item->valueint;
        }
        
        if (storage_period && cJSON_IsNumber(storage_period)) {
            signals[i].storage_period = storage_period->valueint;
        }
        
        if (byte_offset && cJSON_IsNumber(byte_offset)) {
            signals[i].byte_offset = byte_offset->valueint;
        }
        
        if (bit_offset && cJSON_IsNumber(bit_offset)) {
            signals[i].bit_offset = bit_offset->valueint;
        }
        
        if (scaling && cJSON_IsNumber(scaling)) {
            signals[i].scaling = scaling->valuedouble;
        }
        
        if (unit && cJSON_IsString(unit)) {
            signals[i].unit = strdup(unit->valuestring);
        }
        
        if (element_count && cJSON_IsNumber(element_count)) {
            signals[i].element_count = element_count->valueint;
        }
    }
    
    cJSON_Delete(json);
    
    if (verbose) {
        fprintf(stderr, "成功加载 %d 个信号配置\n", *signal_count);
    }
    
    return signals;
}

// 处理信号数据
void process_signal_data(cJSON *signal_data_json, Signal* signals, int signal_count, 
                        const uint8_t* packet_data, time_t start_time) {
    if (!signal_data_json || !signals || !packet_data) return;
    
    for (int i = 0; i < signal_count; i++) {
        Signal *signal = &signals[i];
        
        // 计算信号在数据包中的位置
        int sample_bit_offset = signal->byte_offset * 8 + signal->bit_offset;
        int sample_count = EP2002_MILLISECONDS_PER_SAMPLE / signal->storage_period;
        
        // 获取信号的时间戳对象
        cJSON *signal_timestamps = cJSON_GetObjectItem(signal_data_json, signal->name);
        
        // 处理每个采样点
        for (int j = 0; j < sample_count; j++) {
            // 计算当前采样点的时间
            time_t sample_time = start_time + (j * signal->storage_period / 1000);
            
            // 格式化时间戳
            char timestamp_str[30];
            struct tm *tm_info = gmtime(&sample_time);
            strftime(timestamp_str, 30, "%Y-%m-%d %H:%M:%S", tm_info);
            
            // 检查此时间戳是否已存在，避免重复数据
            cJSON *existing_data = cJSON_GetObjectItem(signal_timestamps, timestamp_str);
            if (existing_data) {
                continue;
            }
            
            // 创建样本数组存储所有节点的值
            cJSON *samples_array = cJSON_CreateArray();
            
            // 处理每个节点的值
            for (int k = 0; k < signal->node_count; k++) {
                // 计算当前节点在数据包中的位位置
                int bit_pos = sample_bit_offset + 
                             j * (signal->bits_per_item * signal->node_count * signal->element_count) + 
                             k * signal->bits_per_item * signal->element_count;
                
                double value = 0;
                
                // 确保位位置在有效范围内
                if (bit_pos / 8 < EP2002_LOG_PACKET_SIZE) {
                    // 提取位值
                    value = extract_bits(packet_data, bit_pos, signal->bits_per_item);
                    
                    // 应用缩放因子
                    if (signal->scaling != 1.0) {
                        value = value * signal->scaling;
                    }
                }
                
                // 添加值到样本数组
                cJSON_AddItemToArray(samples_array, cJSON_CreateNumber(value));
            }
            
            // 创建二维数组格式：[[values]]
            cJSON *wrapper_array = cJSON_CreateArray();
            cJSON_AddItemToArray(wrapper_array, samples_array);
            
            // 添加到信号的时间戳对象
            cJSON_AddItemToObject(signal_timestamps, timestamp_str, wrapper_array);
        }
    }
}

// 优化：批量处理数据包
void process_packets_batch(FILE *file, int start_packet, int batch_size, 
                          cJSON *records_json, Signal* signals, int signal_count) {
    // 批量读取数据包
    uint8_t batch_data[BATCH_READ_SIZE * EP2002_LOG_PACKET_SIZE];
    size_t packets_read = fread(batch_data, EP2002_LOG_PACKET_SIZE, batch_size, file);
    
    for (size_t i = 0; i < packets_read; i++) {
        uint8_t* packet_data = &batch_data[i * EP2002_LOG_PACKET_SIZE];
        
        // 解析记录头
        RecordHeader record_header;
        record_header.record_id = *(uint16_t*)&packet_data[0];
        record_header.packet_version_major = packet_data[2];
        record_header.packet_version_minor = packet_data[3];
        record_header.sample_cycles = *(int32_t*)&packet_data[4];
        record_header.session_sequence = *(int32_t*)&packet_data[8];
        record_header.timestamp = *(int32_t*)&packet_data[12];
        
        // 批量创建记录JSON（复用对象）
        cJSON *record_json = cJSON_CreateObject();
        cJSON *record_header_json = cJSON_CreateObject();
        
        cJSON_AddNumberToObject(record_header_json, "record_id", record_header.record_id);
        cJSON_AddNumberToObject(record_header_json, "packet_version_major", record_header.packet_version_major);
        cJSON_AddNumberToObject(record_header_json, "packet_version_minor", record_header.packet_version_minor);
        cJSON_AddNumberToObject(record_header_json, "sample_cycles", record_header.sample_cycles);
        cJSON_AddNumberToObject(record_header_json, "session_sequence", record_header.session_sequence);
        cJSON_AddNumberToObject(record_header_json, "timestamp", record_header.timestamp);
        
        cJSON_AddItemToObject(record_json, "header", record_header_json);
        cJSON_AddStringToObject(record_json, "start_time", fast_format_timestamp(record_header.timestamp));
        cJSON_AddItemToArray(records_json, record_json);
        
        // 重置处理标志
        for (int j = 0; j < signal_lookup_size; j++) {
            signal_lookup[j].processed = false;
        }
        
        // 处理信号数据（优化版本）
        process_signal_data_optimized(signals, signal_count, packet_data, record_header.timestamp);
    }
}

// 优化：信号数据处理
void process_signal_data_optimized(Signal* signals, int signal_count, 
                                  const uint8_t* packet_data, time_t start_time) {
    for (int i = 0; i < signal_count; i++) {
        Signal *signal = &signals[i];
        
        // 快速查找信号时间戳对象
        cJSON *signal_timestamps = fast_get_signal_timestamps(signal->name);
        if (!signal_timestamps) continue;
        
        // 检查是否已处理过此信号
        bool already_processed = false;
        for (int j = 0; j < signal_lookup_size; j++) {
            if (strcmp(signal_lookup[j].signal_name, signal->name) == 0) {
                if (signal_lookup[j].processed) {
                    already_processed = true;
                } else {
                    signal_lookup[j].processed = true;
                }
                break;
            }
        }
        
        if (already_processed) continue;
        
        // 预计算常量
        int sample_bit_offset = signal->byte_offset * 8 + signal->bit_offset;
        int sample_count = EP2002_MILLISECONDS_PER_SAMPLE / signal->storage_period;
        int bits_per_sample = signal->bits_per_item * signal->node_count * signal->element_count;
        
        // 批量处理采样点
        for (int k = 0; k < sample_count; k++) {
            time_t sample_time = start_time + (k * signal->storage_period / 1000);
            const char* timestamp_str = fast_format_timestamp(sample_time);
            
            // 快速检查时间戳是否已存在
            if (cJSON_GetObjectItem(signal_timestamps, timestamp_str)) {
                continue;
            }
            
            // 预分配数组
            cJSON *samples_array = cJSON_CreateArray();
            cJSON_SetValuestring(samples_array, ""); // 预设容量
            
            // 批量计算所有节点值
            for (int l = 0; l < signal->node_count; l++) {
                int bit_pos = sample_bit_offset + k * bits_per_sample + l * signal->bits_per_item * signal->element_count;
                
                double value = 0;
                if (bit_pos / 8 < EP2002_LOG_PACKET_SIZE) {
                    value = extract_bits(packet_data, bit_pos, signal->bits_per_item);
                    if (signal->scaling != 1.0) {
                        value *= signal->scaling;  // 使用 *= 更快
                    }
                }
                
                cJSON_AddItemToArray(samples_array, cJSON_CreateNumber(value));
            }
            
            // 创建包装数组并添加
            cJSON *wrapper_array = cJSON_CreateArray();
            cJSON_AddItemToArray(wrapper_array, samples_array);
            cJSON_AddItemToObject(signal_timestamps, timestamp_str, wrapper_array);
        }
    }
}

// 主函数
int main(int argc, char *argv[]) {
    if (argc < 2) {
        fprintf(stderr, "用法: %s <bin_file> [signals_json_file] [verbose]\n", argv[0]);
        return 1;
    }
    
    const char *bin_file = argv[1];
    const char *signals_json_file = (argc > 2) ? argv[2] : NULL;
    
    // 设置verbose标志
    if (argc > 3) {
        verbose = (strcmp(argv[3], "true") == 0);
    }
    
    // 打开并验证二进制文件
    FILE *file = fopen(bin_file, "rb");
    if (!file) {
        log_error("无法打开二进制文件");
        return 1;
    }
    
    // 检查文件大小
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);
    
    if (file_size < 64 + EP2002_LOG_PACKET_SIZE) {
        log_error("文件太小，不是有效的EP2002日志文件");
        fclose(file);
        return 1;
    }
    
    // 验证文件ID
    uint16_t file_id;
    if (fread(&file_id, 2, 1, file) != 1) {
        log_error("读取文件ID失败");
        fclose(file);
        return 1;
    }
    
    if (file_id != EP2002_LOG_FILE_ID) {
        char msg[100];
        sprintf(msg, "不是有效的EP2002日志文件(ID: %04X)", file_id);
        log_error(msg);
        fclose(file);
        return 1;
    }
    
    // 读取文件头
    fseek(file, 0, SEEK_SET);
    uint8_t header_data[64];
    if (fread(header_data, 64, 1, file) != 1) {
        log_error("读取文件头失败");
        fclose(file);
        return 1;
    }
    
    // 解析文件头
    FileHeader header;
    header.block_id = *(uint16_t*)&header_data[0];
    header.file_flags = *(uint16_t*)&header_data[2];
    header.sw_version_major = header_data[4];
    header.sw_version_minor = header_data[5];
    header.sw_version_stroke = *(uint16_t*)&header_data[6];
    header.timestamp = *(uint32_t*)&header_data[8];
    header.sequence = *(uint32_t*)&header_data[12];
    header.data_size = *(uint32_t*)&header_data[16];
    header.dallas_id = *(uint64_t*)&header_data[20];
    header.session_id = *(int32_t*)&header_data[28];
    
    // 计算数据包数量
    long actual_data_size = file_size - 64;
    int packet_count = actual_data_size / EP2002_LOG_PACKET_SIZE;
    
    if (verbose) {
        fprintf(stderr, "文件: %s\n", bin_file);
        fprintf(stderr, "软件版本: %d.%d.%d\n", 
               header.sw_version_major, header.sw_version_minor, header.sw_version_stroke);
        fprintf(stderr, "会话ID: %d\n", header.session_id);
        fprintf(stderr, "数据包数量: %d\n", packet_count);
    }
    
    // 加载信号配置
    Signal* signals = NULL;
    int signal_count = 0;
    
    if (signals_json_file) {
        signals = load_signals_from_json(signals_json_file, &signal_count, verbose);
        if (!signals) {
            fclose(file);
            return 1;
        }
    }
    
    // 创建JSON结果对象
    cJSON *result = cJSON_CreateObject();
    
    // 添加文件头信息到JSON
    cJSON *header_json = cJSON_CreateObject();
    cJSON_AddNumberToObject(header_json, "block_id", header.block_id);
    cJSON_AddNumberToObject(header_json, "file_flags", header.file_flags);
    cJSON_AddNumberToObject(header_json, "sw_version_major", header.sw_version_major);
    cJSON_AddNumberToObject(header_json, "sw_version_minor", header.sw_version_minor);
    cJSON_AddNumberToObject(header_json, "sw_version_stroke", header.sw_version_stroke);
    cJSON_AddNumberToObject(header_json, "timestamp", header.timestamp);
    cJSON_AddNumberToObject(header_json, "sequence", header.sequence);
    cJSON_AddNumberToObject(header_json, "data_size", header.data_size);
    
    // 处理64位dallas_id（拆分为高32位和低32位）
    uint32_t dallas_id_high = (uint32_t)(header.dallas_id >> 32);
    uint32_t dallas_id_low = (uint32_t)(header.dallas_id & 0xFFFFFFFF);
    cJSON_AddNumberToObject(header_json, "dallas_id_high", (double)dallas_id_high);
    cJSON_AddNumberToObject(header_json, "dallas_id_low", (double)dallas_id_low);
    
    cJSON_AddNumberToObject(header_json, "session_id", header.session_id);
    cJSON_AddItemToObject(result, "header", header_json);
    
    // 创建记录数组和信号数据对象
    cJSON *records_json = cJSON_CreateArray();
    cJSON *signal_data_json = cJSON_CreateObject();
    
    // 构建信号查找表
    if (signals && signal_data_json) {
        build_signal_lookup(signal_data_json, signals, signal_count);
    }
    
    // 批量处理数据包
    for (int i = 0; i < packet_count; i += BATCH_READ_SIZE) {
        int batch_size = (i + BATCH_READ_SIZE > packet_count) ? (packet_count - i) : BATCH_READ_SIZE;
        process_packets_batch(file, i, batch_size, records_json, signals, signal_count);
    }
    
    // 添加记录和信号数据到最终结果
    cJSON_AddItemToObject(result, "records", records_json);
    if (signals && signal_data_json) {
        cJSON_AddItemToObject(result, "signal_data", signal_data_json);
        
        if (verbose) {
            fprintf(stderr, "信号数据处理完成，包含 %d 个信号\n", signal_count);
        }
    }
    
    // 输出最终JSON结果
    char *json_string = cJSON_Print(result);
    if (json_string) {
        if (verbose) {
            fprintf(stderr, "生成JSON长度: %zu 字节\n", strlen(json_string));
        }
        
        // 输出到stdout
        printf("%s\n", json_string);
        
        // 写入到文件
        FILE *output_file = fopen("c_test.js", "w");
        if (output_file) {
            fprintf(output_file, "const jsonData = %s;\n", json_string);
            fclose(output_file);
            if (verbose) {
                fprintf(stderr, "结果已写入 c_test.js 文件\n");
            }
        }
        
        free(json_string);
    } else {
        log_error("无法生成JSON字符串");
    }
    
    // 清理内存
    cJSON_Delete(result);
    
    if (signals) {
        for (int i = 0; i < signal_count; i++) {
            free(signals[i].name);
            free(signals[i].message_type);
            for (int j = 0; j < signals[i].node_types_count; j++) {
                free(signals[i].node_types[j]);
            }
            free(signals[i].node_types);
            free(signals[i].unit);
        }
        free(signals);
    }
    
    fclose(file);
    return 0;
}
