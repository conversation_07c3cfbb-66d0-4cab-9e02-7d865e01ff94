// 此 C 文件解析 EP2002-Datalogger - Lato - .BIN文件, 生成 python拓展 .so文件 被python调用
// 编译命令:
//      ./build.sh
// 运行测试脚本:
//      >>(myenv) python3 test.py


#include <Python.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <time.h>
#include <stdbool.h>
#include <stdarg.h>

// 常量定义
#define EP2002_LOG_FILE_ID 0xDEAD
#define EP2002_LOG_PACKET_SIZE 2048
#define EP2002_SECONDS_PER_SAMPLE 2
#define EP2002_MILLISECONDS_PER_SAMPLE (EP2002_SECONDS_PER_SAMPLE * 1000)
#define MAX_SIGNALS 1000
#define MAX_TIMESTAMP_CACHE 50000  // 增大缓存大小
#define BATCH_READ_SIZE 100        // 增大批次大小
#define MIN_BATCH_SIZE 50          // 最小批次大小
#define MAX_BATCH_SIZE 500         // 最大批次大小
#define SIGNAL_HASH_TABLE_SIZE 1024 // 信号哈希表大小
#define BATCH_SAMPLES_SIZE 1000    // 批量样本大小
#define TIMESTAMP_BATCH_SIZE 200   // 时间戳批量大小
#define TIMESTAMP_HASH_TABLE_SIZE 8192  // 时间戳哈希表大小（2的幂，便于优化）

// 全局变量
static bool verbose = false;

// 调试输出文件 --- 注意: 此文件里的所有调试 debug / log写入 都注释掉了, 没有删除
// static FILE* debug_file = NULL;

// 数据结构定义
typedef struct {
    uint16_t block_id;
    uint16_t file_flags;
    uint8_t sw_version_major;
    uint8_t sw_version_minor;
    uint16_t sw_version_stroke;
    uint32_t timestamp;
    uint32_t sequence;
    uint32_t data_size;
    uint64_t dallas_id;
    int32_t session_id;
} FileHeader;

typedef struct {
    uint16_t record_id;
    uint8_t packet_version_major;
    uint8_t packet_version_minor;
    int32_t sample_cycles;
    int32_t session_sequence;
    int32_t timestamp;
} RecordHeader;

typedef struct {
    char* name;
    char* message_type;
    char** node_types;
    int node_types_count;
    int node_count;
    int bits_per_item;
    int storage_period;
    int byte_offset;
    int bit_offset;
    double scaling;
    char* unit;
    int element_count;
} Signal;

// 优化：时间戳哈希表节点（支持毫秒精度）
typedef struct TimestampHashNode {
    uint64_t timestamp_value;      // 毫秒精度时间戳
    char timestamp_str[30];        // 格式化后的时间戳字符串
    bool is_formatted;
    int access_order;              // LRU访问顺序
    struct TimestampHashNode* next; // 链表处理哈希冲突
    struct TimestampHashNode* lru_prev; // LRU双向链表
    struct TimestampHashNode* lru_next;
} TimestampHashNode;

// 优化：时间戳哈希表和LRU管理
static TimestampHashNode* timestamp_hash_table[TIMESTAMP_HASH_TABLE_SIZE];
static TimestampHashNode* timestamp_node_pool = NULL;
static int timestamp_node_pool_size = 0;
static int timestamp_node_pool_index = 0;
static int timestamp_cache_access_counter = 0;

// LRU链表头尾指针
static TimestampHashNode* lru_head = NULL;
static TimestampHashNode* lru_tail = NULL;
static int active_timestamp_nodes = 0;

// 优化：哈希表节点结构
typedef struct SignalHashNode {
    char* signal_name;
    PyObject* signal_timestamps;
    bool processed;
    struct SignalHashNode* next;  // 链表处理哈希冲突
} SignalHashNode;

// 优化：信号哈希表
static SignalHashNode* signal_hash_table[SIGNAL_HASH_TABLE_SIZE];
static SignalHashNode* signal_node_pool = NULL;  // 预分配的节点池
static int signal_node_pool_size = 0;
static int signal_node_pool_index = 0;

// 优化：批量数据缓存结构（支持毫秒精度）
typedef struct {
    uint64_t* timestamps;         // 毫秒精度时间戳数组
    double** sample_data;         // 样本数据 [timestamp_index][node_index]
    int* sample_counts;           // 每个时间戳的样本数量
    int timestamp_count;          // 当前时间戳数量
    int max_timestamps;           // 最大时间戳容量
    int max_nodes;               // 最大节点数量
    bool is_dirty;               // 是否有未提交的数据
} BatchDataCache;

// 优化：信号批量处理上下文
typedef struct {
    char* signal_name;
    PyObject* signal_timestamps_dict;
    BatchDataCache data_cache;
    bool is_active;
} SignalBatchContext;

// 全局批量处理上下文数组
static SignalBatchContext* signal_batch_contexts = NULL;
static int signal_batch_contexts_count = 0;

// 函数前向声明 -- 日志调试, 注释
// void init_debug_file();
// void write_debug(const char* format, ...);
// void cleanup_debug_file();

// 工具函数 - 优化的位提取
static inline uint32_t extract_bits_optimized(const uint8_t* data, int bit_offset, int bit_count) {
    if (bit_count == 0 || bit_count > 32) return 0;
    
    uint32_t result = 0;
    int byte_offset = bit_offset >> 3;  // 相当于 bit_offset / 8，按位运算优化
    int bit_start = bit_offset & 7;     // 相当于 bit_offset % 8，按位运算优化
    
    int bits_copied = 0;
    int current_byte = byte_offset;
    
    while (bits_copied < bit_count && current_byte < EP2002_LOG_PACKET_SIZE) {
        int bits_available = 8 - bit_start;
        int bits_to_copy = (bit_count - bits_copied < bits_available) ? 
                          (bit_count - bits_copied) : bits_available;
        
        // 批量提取位，避免逐位处理
        uint8_t mask = (1 << bits_to_copy) - 1;
        uint8_t extracted = (data[current_byte] >> bit_start) & mask;
        
        result |= ((uint32_t)extracted) << bits_copied;
        
        bits_copied += bits_to_copy;
        current_byte++;
        bit_start = 0;
    }
    
    return result;
}

// 优化：时间戳哈希函数（针对毫秒精度时间戳特性优化）
static inline unsigned int hash_timestamp(uint64_t timestamp_ms) {
    // 时间戳通常连续或有模式，使用混合哈希算法
    uint64_t hash = timestamp_ms;
    hash ^= hash >> 33;
    hash *= 0xff51afd7ed558ccdULL;
    hash ^= hash >> 33;
    hash *= 0xc4ceb9fe1a85ec53ULL;
    hash ^= hash >> 33;
    return (unsigned int)(hash & (TIMESTAMP_HASH_TABLE_SIZE - 1));
}

// 保持向后兼容的哈希函数（秒精度）
static inline unsigned int hash_timestamp_seconds(time_t timestamp) {
    return hash_timestamp((uint64_t)timestamp * 1000);
}

// 优化：初始化时间戳哈希表
void init_timestamp_hash_table() {
    // 初始化哈希表
    for (int i = 0; i < TIMESTAMP_HASH_TABLE_SIZE; i++) {
        timestamp_hash_table[i] = NULL;
    }
    
    // 预分配节点池
    timestamp_node_pool_size = MAX_TIMESTAMP_CACHE;
    timestamp_node_pool = (TimestampHashNode*)calloc(timestamp_node_pool_size, sizeof(TimestampHashNode));
    timestamp_node_pool_index = 0;
    
    // 初始化LRU链表
    lru_head = lru_tail = NULL;
    active_timestamp_nodes = 0;
    timestamp_cache_access_counter = 0;
    
    //  -- 日志调试, 注释
    // write_debug("初始化时间戳哈希表，大小: %d，节点池: %d\n", 
    //            TIMESTAMP_HASH_TABLE_SIZE, timestamp_node_pool_size);
}

// 优化：从节点池获取节点
TimestampHashNode* get_timestamp_node_from_pool() {
    if (timestamp_node_pool_index < timestamp_node_pool_size) {
        TimestampHashNode* node = &timestamp_node_pool[timestamp_node_pool_index++];
        memset(node, 0, sizeof(TimestampHashNode));
        return node;
    }
    return NULL;  // 节点池已满
}

// 优化：LRU链表操作 - 移动节点到头部
void move_to_lru_head(TimestampHashNode* node) {
    if (node == lru_head) return;  // 已经在头部
    
    // 从当前位置移除
    if (node->lru_prev) {
        node->lru_prev->lru_next = node->lru_next;
    }
    if (node->lru_next) {
        node->lru_next->lru_prev = node->lru_prev;
    }
    if (node == lru_tail) {
        lru_tail = node->lru_prev;
    }
    
    // 插入到头部
    node->lru_prev = NULL;
    node->lru_next = lru_head;
    if (lru_head) {
        lru_head->lru_prev = node;
    }
    lru_head = node;
    
    if (!lru_tail) {
        lru_tail = node;
    }
}

// 优化：LRU链表操作 - 添加节点到头部
void add_to_lru_head(TimestampHashNode* node) {
    node->lru_prev = NULL;
    node->lru_next = lru_head;
    
    if (lru_head) {
        lru_head->lru_prev = node;
    }
    lru_head = node;
    
    if (!lru_tail) {
        lru_tail = node;
    }
    
    active_timestamp_nodes++;
}

// 优化：LRU链表操作 - 移除尾部节点（最久未使用）
TimestampHashNode* remove_lru_tail() {
    if (!lru_tail) return NULL;

    TimestampHashNode* node = lru_tail;

    // 从哈希表中移除
    unsigned int hash = hash_timestamp(node->timestamp_value);
    TimestampHashNode** curr = &timestamp_hash_table[hash];

    while (*curr && *curr != node) {
        curr = &((*curr)->next);
    }
    if (*curr) {
        *curr = node->next;
    }

    // 从LRU链表移除
    if (node->lru_prev) {
        node->lru_prev->lru_next = NULL;
    } else {
        lru_head = NULL;
    }
    lru_tail = node->lru_prev;

    active_timestamp_nodes--;
    return node;
}

// 优化：时间戳哈希表查找（支持毫秒精度）
TimestampHashNode* hash_find_timestamp(uint64_t timestamp_ms) {
    unsigned int hash = hash_timestamp(timestamp_ms);
    TimestampHashNode* node = timestamp_hash_table[hash];

    while (node) {
        if (node->timestamp_value == timestamp_ms) {
            // 更新LRU顺序
            node->access_order = ++timestamp_cache_access_counter;
            move_to_lru_head(node);
            return node;
        }
        node = node->next;
    }
    return NULL;
}

// 优化：时间戳哈希表插入（支持毫秒精度）
TimestampHashNode* hash_insert_timestamp(uint64_t timestamp_ms) {
    unsigned int hash = hash_timestamp(timestamp_ms);

    // 如果需要替换LRU节点
    TimestampHashNode* node = NULL;
    if (active_timestamp_nodes >= MAX_TIMESTAMP_CACHE) {
        node = remove_lru_tail();
        if (node) {
            // 重用节点，清理内容
            node->timestamp_value = timestamp_ms;
            node->is_formatted = false;
            node->access_order = ++timestamp_cache_access_counter;
            node->next = NULL;
        }
    } else {
        // 从节点池获取新节点
        node = get_timestamp_node_from_pool();
        if (node) {
            node->timestamp_value = timestamp_ms;
            node->is_formatted = false;
            node->access_order = ++timestamp_cache_access_counter;
        }
    }

    if (node) {
        // 插入到哈希表
        node->next = timestamp_hash_table[hash];
        timestamp_hash_table[hash] = node;

        // 添加到LRU链表头部
        add_to_lru_head(node);
    }

    return node;
}

// 优化：高效的时间戳字符串获取（哈希表版本，支持毫秒精度）
const char* get_timestamp_string_optimized_with_milliseconds(uint64_t timestamp_ms) {
    // 分离秒和毫秒部分
    time_t timestamp_sec = timestamp_ms / 1000;
    int milliseconds = timestamp_ms % 1000;

    // 哈希表查找 - O(1)复杂度（基于毫秒时间戳）
    TimestampHashNode* node = hash_find_timestamp(timestamp_ms);

    if (node) {
        // 缓存命中，检查是否需要格式化
        if (!node->is_formatted) {
            struct tm *tm_info = gmtime(&timestamp_sec);
            snprintf(node->timestamp_str, 30, "%04d-%02d-%02d %02d:%02d:%02d.%03d",
                    tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
                    tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec, milliseconds);
            node->is_formatted = true;
        }
        return node->timestamp_str;
    }

    // 缓存未命中，插入新节点
    node = hash_insert_timestamp(timestamp_ms);
    if (node) {
        struct tm *tm_info = gmtime(&timestamp_sec);
        snprintf(node->timestamp_str, 30, "%04d-%02d-%02d %02d:%02d:%02d.%03d",
                tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
                tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec, milliseconds);
        node->is_formatted = true;
        return node->timestamp_str;
    }

    // 后备方案：直接格式化（不缓存）
    static char fallback_buffer[30];
    struct tm *tm_info = gmtime(&timestamp_sec);
    snprintf(fallback_buffer, 30, "%04d-%02d-%02d %02d:%02d:%02d.%03d",
            tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
            tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec, milliseconds);
    return fallback_buffer;
}

// 保持向后兼容的函数（秒精度）
const char* get_timestamp_string_optimized(time_t timestamp) {
    // 转换为毫秒时间戳并调用新函数
    return get_timestamp_string_optimized_with_milliseconds((uint64_t)timestamp * 1000);
}

// 优化：清理时间戳哈希表
void cleanup_timestamp_hash_table() {
    for (int i = 0; i < TIMESTAMP_HASH_TABLE_SIZE; i++) {
        timestamp_hash_table[i] = NULL;
    }
    
    if (timestamp_node_pool) {
        free(timestamp_node_pool);
        timestamp_node_pool = NULL;
    }
    
    timestamp_node_pool_size = 0;
    timestamp_node_pool_index = 0;
    lru_head = lru_tail = NULL;
    active_timestamp_nodes = 0;
    
    //  -- 日志调试, 注释
    // write_debug("清理时间戳哈希表完成\n");
}

//  -- 日志调试, 注释
// 调试工具函数（移到时间戳函数之后）
// void init_debug_file() {
//     if (!debug_file) {
//         debug_file = fopen("os_c_test.txt", "w");
//         if (debug_file) {
//             fprintf(debug_file, "EP2002 C扩展调试输出\n");
//             fprintf(debug_file, "启动时间: %ld\n", time(NULL));
//             fflush(debug_file);
//         }
//     }
// }

// void write_debug(const char* format, ...) {
//     if (debug_file) {
//         va_list args;
//         va_start(args, format);
//         vfprintf(debug_file, format, args);
//         va_end(args);
//         fflush(debug_file);
//     }
// }

// void cleanup_debug_file() {
//     if (debug_file) {
//         fclose(debug_file);
//         debug_file = NULL;
//     }
// }

// 解析Python信号列表
Signal* parse_signals_from_python(PyObject* signals_list, int* signal_count) {
    if (!PyList_Check(signals_list)) {
        return NULL;
    }
    
    *signal_count = PyList_Size(signals_list);
    if (*signal_count <= 0) {
        return NULL;
    }
    
    Signal* signals = (Signal*)calloc(*signal_count, sizeof(Signal));
    if (!signals) {
        return NULL;
    }
    
    //  -- 日志调试, 注释
    // write_debug("解析 %d 个信号配置\n", *signal_count);
    
    for (int i = 0; i < *signal_count; i++) {
        PyObject* signal_dict = PyList_GetItem(signals_list, i);
        if (!PyDict_Check(signal_dict)) {
            continue;
        }
        
        // 解析信号字段
        PyObject* name = PyDict_GetItemString(signal_dict, "name");
        if (name && PyUnicode_Check(name)) {
            const char* name_str = PyUnicode_AsUTF8(name);
            signals[i].name = strdup(name_str);
            //  -- 日志调试, 注释
            // write_debug("信号[%d]: %s\n", i, name_str);
        }
        
        PyObject* message_type = PyDict_GetItemString(signal_dict, "message_type");
        if (message_type && PyUnicode_Check(message_type)) {
            signals[i].message_type = strdup(PyUnicode_AsUTF8(message_type));
        }
        
        PyObject* node_types = PyDict_GetItemString(signal_dict, "node_types");
        if (node_types && PyList_Check(node_types)) {
            int type_count = PyList_Size(node_types);
            signals[i].node_types = (char**)malloc(type_count * sizeof(char*));
            signals[i].node_types_count = type_count;
            
            for (int j = 0; j < type_count; j++) {
                PyObject* type_obj = PyList_GetItem(node_types, j);
                if (type_obj && PyUnicode_Check(type_obj)) {
                    signals[i].node_types[j] = strdup(PyUnicode_AsUTF8(type_obj));
                }
            }
        }
        
        // 解析数值字段
        PyObject* node_count = PyDict_GetItemString(signal_dict, "node_count");
        if (node_count && PyLong_Check(node_count)) {
            signals[i].node_count = PyLong_AsLong(node_count);
        }
        
        PyObject* bits_per_item = PyDict_GetItemString(signal_dict, "bits_per_item");
        if (bits_per_item && PyLong_Check(bits_per_item)) {
            signals[i].bits_per_item = PyLong_AsLong(bits_per_item);
        }
        
        PyObject* storage_period = PyDict_GetItemString(signal_dict, "storage_period");
        if (storage_period && PyLong_Check(storage_period)) {
            signals[i].storage_period = PyLong_AsLong(storage_period);
        }
        
        PyObject* byte_offset = PyDict_GetItemString(signal_dict, "byte_offset");
        if (byte_offset && PyLong_Check(byte_offset)) {
            signals[i].byte_offset = PyLong_AsLong(byte_offset);
        }
        
        PyObject* bit_offset = PyDict_GetItemString(signal_dict, "bit_offset");
        if (bit_offset && PyLong_Check(bit_offset)) {
            signals[i].bit_offset = PyLong_AsLong(bit_offset);
        }
        
        PyObject* scaling = PyDict_GetItemString(signal_dict, "scaling");
        if (scaling && PyFloat_Check(scaling)) {
            signals[i].scaling = PyFloat_AsDouble(scaling);
        } else if (scaling && PyLong_Check(scaling)) {
            signals[i].scaling = (double)PyLong_AsLong(scaling);
        } else {
            signals[i].scaling = 1.0;
        }
        
        PyObject* unit = PyDict_GetItemString(signal_dict, "unit");
        if (unit && PyUnicode_Check(unit)) {
            signals[i].unit = strdup(PyUnicode_AsUTF8(unit));
        }
        
        PyObject* element_count = PyDict_GetItemString(signal_dict, "element_count");
        if (element_count && PyLong_Check(element_count)) {
            signals[i].element_count = PyLong_AsLong(element_count);
        } else {
            signals[i].element_count = 1;
        }
        
        //  -- 日志调试, 注释
        // write_debug("  node_count=%d, bits_per_item=%d, storage_period=%d\n", 
        //            signals[i].node_count, signals[i].bits_per_item, signals[i].storage_period);
        // write_debug("  byte_offset=%d, bit_offset=%d, scaling=%.3f\n",
        //            signals[i].byte_offset, signals[i].bit_offset, signals[i].scaling);
    }
    
    return signals;
}

// 优化：简单哈希函数
unsigned int hash_signal_name(const char* name) {
    unsigned int hash = 5381;
    int c;
    while ((c = *name++)) {
        hash = ((hash << 5) + hash) + c;
    }
    return hash % SIGNAL_HASH_TABLE_SIZE;
}

// 优化：预分配信号节点池
void init_signal_node_pool(int estimated_signal_count) {
    signal_node_pool_size = estimated_signal_count * 2;  // 预留额外空间
    signal_node_pool = (SignalHashNode*)calloc(signal_node_pool_size, sizeof(SignalHashNode));
    signal_node_pool_index = 0;
    
    // 初始化哈希表
    for (int i = 0; i < SIGNAL_HASH_TABLE_SIZE; i++) {
        signal_hash_table[i] = NULL;
    }
    
    //  -- 日志调试, 注释
    // write_debug("预分配信号节点池，大小: %d\n", signal_node_pool_size);
}

// 优化：从池中获取信号节点
SignalHashNode* get_signal_node_from_pool() {
    if (signal_node_pool_index >= signal_node_pool_size) {
        return NULL;  // 池已满
    }
    return &signal_node_pool[signal_node_pool_index++];
}

// 优化：哈希表插入信号
void hash_insert_signal(const char* signal_name, PyObject* signal_timestamps) {
    unsigned int hash = hash_signal_name(signal_name);
    SignalHashNode* node = get_signal_node_from_pool();
    
    if (!node) {
        //  -- 日志调试, 注释
        // write_debug("警告: 信号节点池已满\n");
        return;
    }
    
    node->signal_name = strdup(signal_name);
    node->signal_timestamps = signal_timestamps;
    node->processed = false;
    node->next = signal_hash_table[hash];
    signal_hash_table[hash] = node;
}

// 优化：哈希表查找信号
PyObject* hash_get_signal_timestamps(const char* signal_name) {
    unsigned int hash = hash_signal_name(signal_name);
    SignalHashNode* node = signal_hash_table[hash];
    
    while (node) {
        if (strcmp(node->signal_name, signal_name) == 0) {
            return node->signal_timestamps;
        }
        node = node->next;
    }
    return NULL;
}

// 优化：设置信号处理状态
void hash_set_signal_processed(const char* signal_name, bool processed) {
    unsigned int hash = hash_signal_name(signal_name);
    SignalHashNode* node = signal_hash_table[hash];
    
    while (node) {
        if (strcmp(node->signal_name, signal_name) == 0) {
            node->processed = processed;
            return;
        }
        node = node->next;
    }
}

// 优化：检查信号是否已处理
bool hash_is_signal_processed(const char* signal_name) {
    unsigned int hash = hash_signal_name(signal_name);
    SignalHashNode* node = signal_hash_table[hash];
    
    while (node) {
        if (strcmp(node->signal_name, signal_name) == 0) {
            return node->processed;
        }
        node = node->next;
    }
    return false;
}

// 优化：重置所有信号处理状态
void hash_reset_all_processed_flags() {
    for (int i = 0; i < SIGNAL_HASH_TABLE_SIZE; i++) {
        SignalHashNode* node = signal_hash_table[i];
        while (node) {
            node->processed = false;
            node = node->next;
        }
    }
}

// 优化：清理哈希表
void cleanup_signal_hash_table() {
    for (int i = 0; i < SIGNAL_HASH_TABLE_SIZE; i++) {
        SignalHashNode* node = signal_hash_table[i];
        while (node) {
            free(node->signal_name);
            node = node->next;
        }
        signal_hash_table[i] = NULL;
    }
    
    if (signal_node_pool) {
        free(signal_node_pool);
        signal_node_pool = NULL;
    }
}

// 优化：动态批次大小计算
static int calculate_optimal_batch_size(long file_size, int packet_count) {
    if (packet_count <= 0) return MIN_BATCH_SIZE;
    
    // 根据文件大小和数据包数量动态调整
    int optimal_size;
    if (file_size < 1024 * 1024) {  // < 1MB
        optimal_size = MIN_BATCH_SIZE;
    } else if (file_size < 10 * 1024 * 1024) {  // < 10MB
        optimal_size = BATCH_READ_SIZE;
    } else {  // >= 10MB
        optimal_size = MAX_BATCH_SIZE;
    }
    
    // 确保批次大小不超过总数据包数
    return (optimal_size > packet_count) ? packet_count : optimal_size;
}

// 优化：构建信号哈希表（替代线性查找表）
void build_signal_hash_table(PyObject *signal_data_dict, Signal* signals, int signal_count) {
    // 初始化节点池
    init_signal_node_pool(signal_count);
    
    for (int i = 0; i < signal_count; i++) {
        Signal *signal = &signals[i];
        
        // 检查是否已存在（使用哈希查找）
        if (hash_get_signal_timestamps(signal->name)) {
            continue;  // 已存在，跳过
        }
        
        // 创建或获取信号时间戳对象
        PyObject *signal_timestamps = PyDict_GetItemString(signal_data_dict, signal->name);
        if (!signal_timestamps) {
            signal_timestamps = PyDict_New();
            PyDict_SetItemString(signal_data_dict, signal->name, signal_timestamps);
            Py_DECREF(signal_timestamps); // PyDict_SetItemString会增加引用计数
        }
        
        // 插入哈希表
        hash_insert_signal(signal->name, signal_timestamps);
    }
    
    //  -- 日志调试, 注释
    // write_debug("构建信号哈希表完成，包含 %d 个信号\n", signal_count);
}

// 优化：初始化批量数据缓存（支持毫秒精度）
void init_batch_data_cache(BatchDataCache* cache, int max_timestamps, int max_nodes) {
    cache->timestamps = (uint64_t*)calloc(max_timestamps, sizeof(uint64_t));
    cache->sample_data = (double**)calloc(max_timestamps, sizeof(double*));
    cache->sample_counts = (int*)calloc(max_timestamps, sizeof(int));

    for (int i = 0; i < max_timestamps; i++) {
        cache->sample_data[i] = (double*)calloc(max_nodes, sizeof(double));
    }

    cache->timestamp_count = 0;
    cache->max_timestamps = max_timestamps;
    cache->max_nodes = max_nodes;
    cache->is_dirty = false;

    //  -- 日志调试, 注释
    // write_debug("初始化批量数据缓存: max_timestamps=%d, max_nodes=%d\n",
            //    max_timestamps, max_nodes);
}

// 优化：清理批量数据缓存
void cleanup_batch_data_cache(BatchDataCache* cache) {
    if (cache->timestamps) {
        free(cache->timestamps);
        cache->timestamps = NULL;
    }
    
    if (cache->sample_data) {
        for (int i = 0; i < cache->max_timestamps; i++) {
            if (cache->sample_data[i]) {
                free(cache->sample_data[i]);
            }
        }
        free(cache->sample_data);
        cache->sample_data = NULL;
    }
    
    if (cache->sample_counts) {
        free(cache->sample_counts);
        cache->sample_counts = NULL;
    }
}

// 优化：初始化信号批量上下文
void init_signal_batch_contexts(Signal* signals, int signal_count, PyObject* signal_data_dict) {
    signal_batch_contexts_count = signal_count;
    signal_batch_contexts = (SignalBatchContext*)calloc(signal_count, sizeof(SignalBatchContext));
    
    for (int i = 0; i < signal_count; i++) {
        Signal* signal = &signals[i];
        SignalBatchContext* context = &signal_batch_contexts[i];
        
        context->signal_name = strdup(signal->name);
        context->is_active = true;
        
        // 获取或创建信号时间戳字典
        context->signal_timestamps_dict = PyDict_GetItemString(signal_data_dict, signal->name);
        if (!context->signal_timestamps_dict) {
            context->signal_timestamps_dict = PyDict_New();
            PyDict_SetItemString(signal_data_dict, signal->name, context->signal_timestamps_dict);
            Py_DECREF(context->signal_timestamps_dict); // PyDict_SetItemString增加了引用计数
        }
        
        // 初始化批量数据缓存
        init_batch_data_cache(&context->data_cache, TIMESTAMP_BATCH_SIZE, signal->node_count);
        
        //  -- 日志调试, 注释
        // write_debug("初始化信号[%s]批量上下文，节点数: %d\n", signal->name, signal->node_count);
    }
}

// 优化：查找信号批量上下文
SignalBatchContext* find_signal_batch_context(const char* signal_name) {
    for (int i = 0; i < signal_batch_contexts_count; i++) {
        if (strcmp(signal_batch_contexts[i].signal_name, signal_name) == 0) {
            return &signal_batch_contexts[i];
        }
    }
    return NULL;
}

// 优化：添加样本到批量缓存（支持毫秒精度）
bool add_sample_to_batch_cache_with_milliseconds(BatchDataCache* cache, uint64_t timestamp_ms, double* node_values, int node_count) {
    // 检查是否已存在该时间戳
    for (int i = 0; i < cache->timestamp_count; i++) {
        if (cache->timestamps[i] == timestamp_ms) {
            return false; // 时间戳已存在，跳过
        }
    }

    // 检查缓存是否已满
    if (cache->timestamp_count >= cache->max_timestamps) {
        return false; // 缓存已满，需要刷新
    }

    // 添加新样本
    int index = cache->timestamp_count++;
    cache->timestamps[index] = timestamp_ms;
    cache->sample_counts[index] = node_count;

    // 复制节点数据
    for (int i = 0; i < node_count && i < cache->max_nodes; i++) {
        cache->sample_data[index][i] = node_values[i];
    }

    cache->is_dirty = true;
    return true;
}

// 保持向后兼容的函数（秒精度）
bool add_sample_to_batch_cache(BatchDataCache* cache, time_t timestamp, double* node_values, int node_count) {
    return add_sample_to_batch_cache_with_milliseconds(cache, (uint64_t)timestamp * 1000, node_values, node_count);
}

// 优化：批量提交缓存到Python对象（支持毫秒精度）
void flush_batch_cache_to_python(SignalBatchContext* context) {
    BatchDataCache* cache = &context->data_cache;

    if (!cache->is_dirty || cache->timestamp_count == 0) {
        return; // 没有数据需要提交
    }

    //  -- 日志调试, 注释
    // write_debug("批量提交信号[%s]的%d个时间戳\n", context->signal_name, cache->timestamp_count);

    // 预分配Python对象数组
    PyObject** timestamp_keys = (PyObject**)calloc(cache->timestamp_count, sizeof(PyObject*));
    PyObject** wrapper_arrays = (PyObject**)calloc(cache->timestamp_count, sizeof(PyObject*));

    // 批量创建Python对象
    for (int i = 0; i < cache->timestamp_count; i++) {
        // 创建时间戳键（使用毫秒精度）
        timestamp_keys[i] = PyLong_FromUnsignedLongLong(cache->timestamps[i]);

        // 创建样本数组
        PyObject* samples_array = PyList_New(cache->sample_counts[i]);
        for (int j = 0; j < cache->sample_counts[i]; j++) {
            PyList_SetItem(samples_array, j, PyFloat_FromDouble(cache->sample_data[i][j]));
        }

        // 创建包装数组
        wrapper_arrays[i] = PyList_New(1);
        PyList_SetItem(wrapper_arrays[i], 0, samples_array);
    }

    // 批量添加到字典（减少字典操作次数）
    for (int i = 0; i < cache->timestamp_count; i++) {
        PyDict_SetItem(context->signal_timestamps_dict, timestamp_keys[i], wrapper_arrays[i]);
    }

    // 批量清理引用
    for (int i = 0; i < cache->timestamp_count; i++) {
        Py_DECREF(timestamp_keys[i]);
        Py_DECREF(wrapper_arrays[i]);
    }

    free(timestamp_keys);
    free(wrapper_arrays);

    // 重置缓存
    cache->timestamp_count = 0;
    cache->is_dirty = false;
}

// 优化：清理所有信号批量上下文
void cleanup_signal_batch_contexts() {
    if (signal_batch_contexts) {
        for (int i = 0; i < signal_batch_contexts_count; i++) {
            SignalBatchContext* context = &signal_batch_contexts[i];
            
            // 提交未处理的数据
            flush_batch_cache_to_python(context);
            
            // 清理缓存
            cleanup_batch_data_cache(&context->data_cache);
            
            // 释放信号名
            if (context->signal_name) {
                free(context->signal_name);
            }
        }
        
        free(signal_batch_contexts);
        signal_batch_contexts = NULL;
        signal_batch_contexts_count = 0;
    }
}

// 优化：批量信号数据处理 - 减少Python对象创建（支持毫秒精度）
void process_signal_data_batch_optimized(Signal* signals, int signal_count,
                                        const uint8_t* packet_data, time_t start_time) {
    static int batch_counter = 0;

    for (int i = 0; i < signal_count; i++) {
        Signal *signal = &signals[i];

        // 查找信号批量上下文
        SignalBatchContext* context = find_signal_batch_context(signal->name);
        if (!context || !context->is_active) {
            continue;
        }

        // 预计算常量
        int sample_bit_offset = signal->byte_offset * 8 + signal->bit_offset;
        int sample_count = EP2002_MILLISECONDS_PER_SAMPLE / signal->storage_period;
        int bits_per_sample = signal->bits_per_item * signal->node_count * signal->element_count;

        //  -- 日志调试, 注释
        // if (batch_counter < 5) {
        //     write_debug("批量处理信号[%s]: sample_bit_offset=%d, sample_count=%d, bits_per_sample=%d\n",
        //                signal->name, sample_bit_offset, sample_count, bits_per_sample);
        // }

        // 预分配节点数据数组
        double* node_values = (double*)calloc(signal->node_count, sizeof(double));

        // 批量处理采样点
        for (int k = 0; k < sample_count; k++) {
            // 计算毫秒精度的时间戳
            uint64_t sample_time_ms = (uint64_t)start_time * 1000 + (k * signal->storage_period);

            // 批量计算所有节点值
            for (int l = 0; l < signal->node_count; l++) {
                int bit_pos = sample_bit_offset + k * bits_per_sample + l * signal->bits_per_item * signal->element_count;

                double value = 0;
                if (bit_pos / 8 < EP2002_LOG_PACKET_SIZE) {
                    value = extract_bits_optimized(packet_data, bit_pos, signal->bits_per_item);
                    if (signal->scaling != 1.0) {
                        value *= signal->scaling;
                    }
                }

                node_values[l] = value;

                //  -- 日志调试, 注释
                // if (batch_counter < 3 && k < 2 && l < 3) {
                //     const char* timestamp_str = get_timestamp_string_optimized_with_milliseconds(sample_time_ms);
                //     write_debug("    批量样本[%s][%s][节点%d] = %.3f (bit_pos=%d)\n",
                //                signal->name, timestamp_str, l, value, bit_pos);
                // }
            }

            // 添加到批量缓存而不是立即创建Python对象（使用毫秒精度）
            if (!add_sample_to_batch_cache_with_milliseconds(&context->data_cache, sample_time_ms, node_values, signal->node_count)) {
                // 缓存已满，先提交现有数据
                flush_batch_cache_to_python(context);

                // 重试添加
                add_sample_to_batch_cache_with_milliseconds(&context->data_cache, sample_time_ms, node_values, signal->node_count);
            }
        }

        free(node_values);
    }

    batch_counter++;

    // 定期刷新缓存以避免内存过度使用
    if (batch_counter % 50 == 0) {
        //  -- 日志调试, 注释
        // write_debug("定期刷新批量缓存 (batch_counter=%d)\n", batch_counter);
        for (int i = 0; i < signal_batch_contexts_count; i++) {
            flush_batch_cache_to_python(&signal_batch_contexts[i]);
        }
    }
}

// 优化：批量处理数据包 - 使用批量上下文
void process_packets_batch(FILE *file, int start_packet, int batch_size, 
                          PyObject *records_list, Signal* signals, int signal_count) {
    // 动态分配批量数据缓冲区
    uint8_t* batch_data = (uint8_t*)malloc(batch_size * EP2002_LOG_PACKET_SIZE);
    if (!batch_data) {
        //  -- 日志调试, 注释
        // write_debug("错误: 无法分配批量数据缓冲区\n");
        return;
    }
    
    size_t packets_read = fread(batch_data, EP2002_LOG_PACKET_SIZE, batch_size, file);
    
    //  -- 日志调试, 注释
    // write_debug("批量读取数据包：起始=%d, 大小=%d, 实际读取=%zu\n", start_packet, batch_size, packets_read);
    
    // 预分配记录Python对象数组
    PyObject** record_objects = (PyObject**)calloc(packets_read, sizeof(PyObject*));
    
    for (size_t i = 0; i < packets_read; i++) {
        uint8_t* packet_data = &batch_data[i * EP2002_LOG_PACKET_SIZE];
        
        // 解析记录头
        RecordHeader record_header;
        record_header.record_id = *(uint16_t*)&packet_data[0];
        record_header.packet_version_major = packet_data[2];
        record_header.packet_version_minor = packet_data[3];
        record_header.sample_cycles = *(int32_t*)&packet_data[4];
        record_header.session_sequence = *(int32_t*)&packet_data[8];
        record_header.timestamp = *(int32_t*)&packet_data[12];
        
        //  -- 日志调试, 注释
        // if (i < 3) { // 记录前几个记录头信息
        //     write_debug("记录[%zu]: record_id=%d, timestamp=%d, session_sequence=%d\n",
        //                i, record_header.record_id, record_header.timestamp, record_header.session_sequence);
        // }
        
        // 创建记录Python对象
        PyObject *record_dict = PyDict_New();
        PyObject *record_header_dict = PyDict_New();
        
        PyDict_SetItemString(record_header_dict, "record_id", PyLong_FromLong(record_header.record_id));
        PyDict_SetItemString(record_header_dict, "packet_version_major", PyLong_FromLong(record_header.packet_version_major));
        PyDict_SetItemString(record_header_dict, "packet_version_minor", PyLong_FromLong(record_header.packet_version_minor));
        PyDict_SetItemString(record_header_dict, "sample_cycles", PyLong_FromLong(record_header.sample_cycles));
        PyDict_SetItemString(record_header_dict, "session_sequence", PyLong_FromLong(record_header.session_sequence));
        PyDict_SetItemString(record_header_dict, "timestamp", PyLong_FromLong(record_header.timestamp));
        
        PyDict_SetItemString(record_dict, "header", record_header_dict);
        PyDict_SetItemString(record_dict, "start_time", PyUnicode_FromString(get_timestamp_string_optimized(record_header.timestamp)));
        
        record_objects[i] = record_dict;
        Py_DECREF(record_header_dict);
        
        // 处理信号数据（批量优化版本）
        process_signal_data_batch_optimized(signals, signal_count, packet_data, record_header.timestamp);
    }

    // -- 日志调试, 注释
    // === 新增：写入时间戳到 timestamp.js, 检查是否精确到毫秒 ===
    // FILE *fp = fopen("./timestamp.js", "w");
    // if (!fp) {
    //     write_debug("错误: 无法创建文件 ./timestamp.js\n");
    // } else {
    //     fprintf(fp, "const timestamps = [\n");

    //     for (size_t i = 0; i < packets_read; i++) {
    //         PyObject *record_dict = record_objects[i];
    //         PyObject *header_dict = PyDict_GetItemString(record_dict, "header");
    //         if (!header_dict) {
    //             write_debug("警告: record_dict[%zu] 缺少 header 字段\n", i);
    //             continue;
    //         }

    //         PyObject *timestamp_obj = PyDict_GetItemString(header_dict, "timestamp");
    //         if (!timestamp_obj) {
    //             write_debug("警告: record_dict[%zu] 缺少 timestamp 字段\n", i);
    //             continue;
    //         }

    //         long timestamp = PyLong_AsLong(timestamp_obj);
    //         fprintf(fp, "  %ld", timestamp);
    //         if (i != packets_read - 1)
    //             fprintf(fp, ",");
    //         fprintf(fp, "\n");
    //     }

    //     fprintf(fp, "];\n");
    //     fclose(fp);
    //     write_debug("时间戳已写入到 ./timestamp.js\n");
    // }


    // 批量添加记录到列表
    for (size_t i = 0; i < packets_read; i++) {
        PyList_Append(records_list, record_objects[i]);
        Py_DECREF(record_objects[i]);
    }

    free(record_objects);
    free(batch_data);
}

// 清理信号内存
void cleanup_signals(Signal* signals, int signal_count) {
    if (signals) {
        for (int i = 0; i < signal_count; i++) {
            free(signals[i].name);
            free(signals[i].message_type);
            for (int j = 0; j < signals[i].node_types_count; j++) {
                free(signals[i].node_types[j]);
            }
            free(signals[i].node_types);
            free(signals[i].unit);
        }
        free(signals);
    }
}

// 优化：在最终结果中转换数值型时间戳为字符串格式（支持毫秒精度）
PyObject* convert_numeric_timestamps_to_strings(PyObject* signal_data_dict) {
    PyObject* converted_dict = PyDict_New();
    PyObject* signal_name = NULL;
    PyObject* signal_timestamps = NULL;
    Py_ssize_t pos = 0;

    // 遍历所有信号
    while (PyDict_Next(signal_data_dict, &pos, &signal_name, &signal_timestamps)) {
        PyObject* converted_timestamps = PyDict_New();

        PyObject* timestamp_key = NULL;
        PyObject* timestamp_value = NULL;
        Py_ssize_t timestamp_pos = 0;

        // 遍历该信号的所有时间戳
        while (PyDict_Next(signal_timestamps, &timestamp_pos, &timestamp_key, &timestamp_value)) {
            if (PyLong_Check(timestamp_key)) {
                // 转换数值型时间戳为字符串（支持毫秒精度）
                uint64_t timestamp_ms = (uint64_t)PyLong_AsUnsignedLongLong(timestamp_key);
                const char* timestamp_str = get_timestamp_string_optimized_with_milliseconds(timestamp_ms);

                PyDict_SetItemString(converted_timestamps, timestamp_str, timestamp_value);
            } else {
                // 如果已经是字符串键，直接复制
                PyDict_SetItem(converted_timestamps, timestamp_key, timestamp_value);
            }
        }

        PyDict_SetItem(converted_dict, signal_name, converted_timestamps);
        Py_DECREF(converted_timestamps);
    }

    return converted_dict;
}

// 新增分片解析函数
static PyObject* parse_bin_file_segment(PyObject* self, PyObject* args) {
    const char* bin_file;
    long start_offset;
    int packet_count;
    PyObject* signals_list = NULL;
    int verbose_int = 0;
    
    // 解析参数：文件路径、起始偏移量、数据包数量、信号列表、详细模式
    if (!PyArg_ParseTuple(args, "sliOi", &bin_file, &start_offset, &packet_count, &signals_list, &verbose_int)) {
        return NULL;
    }
    
    verbose = (verbose_int != 0);
    
    // -- 日志调试, 注释
    // 初始化调试文件
    // init_debug_file();
    // write_debug("开始分片解析: 文件=%s, 偏移=%ld, 数量=%d\n", bin_file, start_offset, packet_count);
    
    // 初始化时间戳哈希表
    init_timestamp_hash_table();
    
    // 打开二进制文件
    FILE *file = fopen(bin_file, "rb");
    if (!file) {
        // -- 日志调试, 注释
        // write_debug("错误: 无法打开二进制文件\n");
        // cleanup_debug_file();
        PyErr_SetString(PyExc_IOError, "无法打开二进制文件");
        return NULL;
    }
    
    // 检查文件大小和偏移量有效性
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    
    if (start_offset < 64 || start_offset >= file_size) {
        // -- 日志调试, 注释
        // write_debug("错误: 起始偏移量无效: %ld\n", start_offset);
        fclose(file);
        // cleanup_debug_file();
        PyErr_SetString(PyExc_ValueError, "起始偏移量无效");
        return NULL;
    }
    
    // 读取文件头（只需要读一次，所有分片共享同一个文件头）
    fseek(file, 0, SEEK_SET);
    uint8_t header_data[64];
    if (fread(header_data, 64, 1, file) != 1) {
        // -- 日志调试, 注释
        // write_debug("错误: 读取文件头失败\n");
        fclose(file);
        // cleanup_debug_file();
        PyErr_SetString(PyExc_IOError, "读取文件头失败");
        return NULL;
    }
    
    // 解析文件头
    FileHeader header;
    header.block_id = *(uint16_t*)&header_data[0];
    header.file_flags = *(uint16_t*)&header_data[2];
    header.sw_version_major = header_data[4];
    header.sw_version_minor = header_data[5];
    header.sw_version_stroke = *(uint16_t*)&header_data[6];
    header.timestamp = *(uint32_t*)&header_data[8];
    header.sequence = *(uint32_t*)&header_data[12];
    header.data_size = *(uint32_t*)&header_data[16];
    header.dallas_id = *(uint64_t*)&header_data[20];
    header.session_id = *(int32_t*)&header_data[28];
    
    // // -- 日志调试, 注释
    // write_debug("分片处理: 软件版本=%d.%d.%d, 会话ID=%d\n", 
    //            header.sw_version_major, header.sw_version_minor, header.sw_version_stroke, header.session_id);
    
    // 解析信号配置
    Signal* signals = NULL;
    int signal_count = 0;
    
    if (signals_list && PyList_Check(signals_list)) {
        signals = parse_signals_from_python(signals_list, &signal_count);
        // -- 日志调试, 注释
        // write_debug("分片解析信号配置: %d 个信号\n", signal_count);
    }
    
    // 创建Python结果对象
    PyObject *result = PyDict_New();
    
    // 添加文件头信息
    PyObject *header_dict = PyDict_New();
    PyDict_SetItemString(header_dict, "block_id", PyLong_FromLong(header.block_id));
    PyDict_SetItemString(header_dict, "file_flags", PyLong_FromLong(header.file_flags));
    PyDict_SetItemString(header_dict, "sw_version_major", PyLong_FromLong(header.sw_version_major));
    PyDict_SetItemString(header_dict, "sw_version_minor", PyLong_FromLong(header.sw_version_minor));
    PyDict_SetItemString(header_dict, "sw_version_stroke", PyLong_FromLong(header.sw_version_stroke));
    PyDict_SetItemString(header_dict, "timestamp", PyLong_FromLong(header.timestamp));
    PyDict_SetItemString(header_dict, "sequence", PyLong_FromLong(header.sequence));
    PyDict_SetItemString(header_dict, "data_size", PyLong_FromLong(header.data_size));
    
    // 处理64位dallas_id
    uint32_t dallas_id_high = (uint32_t)(header.dallas_id >> 32);
    uint32_t dallas_id_low = (uint32_t)(header.dallas_id & 0xFFFFFFFF);
    PyDict_SetItemString(header_dict, "dallas_id_high", PyLong_FromLong(dallas_id_high));
    PyDict_SetItemString(header_dict, "dallas_id_low", PyLong_FromLong(dallas_id_low));
    
    PyDict_SetItemString(header_dict, "session_id", PyLong_FromLong(header.session_id));
    PyDict_SetItemString(result, "header", header_dict);
    Py_DECREF(header_dict);
    
    // 创建记录列表和信号数据字典
    PyObject *records_list = PyList_New(0);
    PyObject *signal_data_dict = PyDict_New();
    
    // 初始化信号批量上下文
    if (signals && signal_data_dict) {
        init_signal_batch_contexts(signals, signal_count, signal_data_dict);
    }
    
    // 移动到指定偏移量开始处理
    fseek(file, start_offset, SEEK_SET);
    // -- 日志调试, 注释
    // write_debug("开始分片处理: 偏移=%ld, 数据包数=%d\n", start_offset, packet_count);
    
    // 计算动态批次大小（针对分片调整）
    int segment_batch_size = calculate_optimal_batch_size(packet_count * EP2002_LOG_PACKET_SIZE, packet_count);
    // -- 日志调试, 注释
    // write_debug("分片动态批次大小: %d\n", segment_batch_size);
    
    // 分批处理指定数量的数据包
    int processed_packets = 0;
    while (processed_packets < packet_count) {
        int current_batch_size = (processed_packets + segment_batch_size > packet_count) ? 
                                (packet_count - processed_packets) : segment_batch_size;
        
        // 处理当前批次
        process_packets_batch(file, processed_packets, current_batch_size, records_list, signals, signal_count);
        
        processed_packets += current_batch_size;
        
        // -- 日志调试, 注释
        // if (processed_packets % 100 == 0 || processed_packets >= packet_count) {
        //     write_debug("分片处理进度: %d/%d\n", processed_packets, packet_count);
        // }
    }
    
    // 最终刷新所有批量缓存
    for (int i = 0; i < signal_batch_contexts_count; i++) {
        flush_batch_cache_to_python(&signal_batch_contexts[i]);
    }
    
    // 转换数值型时间戳为字符串格式
    PyObject *final_signal_data = NULL;
    if (signals && signal_data_dict) {
        final_signal_data = convert_numeric_timestamps_to_strings(signal_data_dict);
    } else {
        final_signal_data = signal_data_dict;
        Py_INCREF(final_signal_data);
    }
    
    // 添加记录和信号数据到结果
    PyDict_SetItemString(result, "records", records_list);
    if (final_signal_data) {
        PyDict_SetItemString(result, "signal_data", final_signal_data);
    }
    
    Py_DECREF(records_list);
    Py_DECREF(signal_data_dict);
    Py_DECREF(final_signal_data);
    
    // 清理内存
    cleanup_signals(signals, signal_count);
    cleanup_signal_batch_contexts();
    cleanup_timestamp_hash_table();
    fclose(file);
    
    // -- 日志调试, 注释
    // write_debug("分片解析完成，返回结果\n");
    // cleanup_debug_file();
    
    return result;
}

// 保留原来的完整文件解析函数（向后兼容）
static PyObject* parse_bin_file(PyObject* self, PyObject* args) {
    const char* bin_file;
    PyObject* signals_list = NULL;
    int verbose_int = 0;
    
    // 解析参数
    if (!PyArg_ParseTuple(args, "s|Oi", &bin_file, &signals_list, &verbose_int)) {
        return NULL;
    }
    
    // 计算文件数据包总数，然后调用分片函数处理整个文件
    FILE *file = fopen(bin_file, "rb");
    if (!file) {
        PyErr_SetString(PyExc_IOError, "无法打开二进制文件");
        return NULL;
    }
    
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fclose(file);
    
    if (file_size < 64 + EP2002_LOG_PACKET_SIZE) {
        PyErr_SetString(PyExc_ValueError, "文件太小，不是有效的EP2002日志文件");
        return NULL;
    }
    
    long actual_data_size = file_size - 64;
    int total_packet_count = actual_data_size / EP2002_LOG_PACKET_SIZE;
    
    // 调用分片函数处理整个文件
    PyObject* args_tuple = Py_BuildValue("sliOi", bin_file, 64L, total_packet_count, signals_list, verbose_int);
    PyObject* result = parse_bin_file_segment(self, args_tuple);
    Py_DECREF(args_tuple);
    
    return result;
}

// 更新模块方法定义
static PyMethodDef BinfileParserMethods[] = {
    {"parse_bin_file", parse_bin_file, METH_VARARGS, "解析EP2002二进制日志文件（完整文件）"},
    {"parse_bin_file_segment", parse_bin_file_segment, METH_VARARGS, "解析EP2002二进制日志文件片段"},
    {NULL, NULL, 0, NULL}
};

// 模块定义和初始化函数保持不变
static struct PyModuleDef binfile_parser_module = {
    PyModuleDef_HEAD_INIT,
    "binfile_parser",
    "EP2002二进制文件解析器C扩展模块",
    -1,
    BinfileParserMethods
};

PyMODINIT_FUNC PyInit_binfile_parser(void) {
    return PyModule_Create(&binfile_parser_module);
}
