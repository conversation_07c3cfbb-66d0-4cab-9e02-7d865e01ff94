<?xml version="1.0" encoding="UTF-8"?>
<!--Sample XML file generated by XMLSpy v2013 (http://www.altova.com)-->
<nt:EP2002NodeTypes xmlns:nt="http://www.Knorr-Bremse.com/EP2002NodeTypes">
	<nt:NodeType>
		<nt:Name>BCU</nt:Name>
		<nt:Node>
			<nt:Name>BCU 1</nt:Name>
			<nt:Index>0</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>BCU 2</nt:Name>
			<nt:Index>1</nt:Index>
		</nt:Node>
	</nt:NodeType>
	<nt:NodeType>
		<nt:Name>GSM</nt:Name>
		<nt:Node>
			<nt:Name>GSM 1P</nt:Name>
			<nt:Index>0</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>GSM 1S</nt:Name>
			<nt:Index>1</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>GSM 2P</nt:Name>
			<nt:Index>2</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>GSM 2S</nt:Name>
			<nt:Index>3</nt:Index>
		</nt:Node>
	</nt:NodeType>
	<nt:NodeType>
		<nt:Name>RBX-logical</nt:Name>
		<nt:Node>
			<nt:Name>RBX L1A</nt:Name>
			<nt:Index>0</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX L1B</nt:Name>
			<nt:Index>1</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX L2A</nt:Name>
			<nt:Index>2</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX L2B</nt:Name>
			<nt:Index>3</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX L3A</nt:Name>
			<nt:Index>4</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX L3B</nt:Name>
			<nt:Index>5</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX L4A</nt:Name>
			<nt:Index>6</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX L4B</nt:Name>
			<nt:Index>7</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX L5A</nt:Name>
			<nt:Index>8</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX L5B</nt:Name>
			<nt:Index>9</nt:Index>
		</nt:Node>
	</nt:NodeType>
	<nt:NodeType>
		<nt:Name>RIO-logical</nt:Name>
		<nt:Node>
			<nt:Name>RIO L1A</nt:Name>
			<nt:Index>0</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO L1B</nt:Name>
			<nt:Index>1</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO L2A</nt:Name>
			<nt:Index>2</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO L2B</nt:Name>
			<nt:Index>3</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO L3A</nt:Name>
			<nt:Index>4</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO L3B</nt:Name>
			<nt:Index>5</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO L4A</nt:Name>
			<nt:Index>6</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO L4B</nt:Name>
			<nt:Index>7</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO L5A</nt:Name>
			<nt:Index>8</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO L5B</nt:Name>
			<nt:Index>9</nt:Index>
		</nt:Node>
	</nt:NodeType>
	<nt:NodeType>
		<nt:Name>RBX-physical</nt:Name>
		<nt:Node>
			<nt:Name>RBX P00A</nt:Name>
			<nt:Index>0</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P00B</nt:Name>
			<nt:Index>1</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P01A</nt:Name>
			<nt:Index>2</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P01B</nt:Name>
			<nt:Index>3</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P02A</nt:Name>
			<nt:Index>4</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P02B</nt:Name>
			<nt:Index>5</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P03A</nt:Name>
			<nt:Index>6</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P03B</nt:Name>
			<nt:Index>7</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P04A</nt:Name>
			<nt:Index>8</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P04B</nt:Name>
			<nt:Index>9</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P05A</nt:Name>
			<nt:Index>10</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P05B</nt:Name>
			<nt:Index>11</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P06A</nt:Name>
			<nt:Index>12</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P06B</nt:Name>
			<nt:Index>13</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P07A</nt:Name>
			<nt:Index>14</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P07B</nt:Name>
			<nt:Index>15</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P08A</nt:Name>
			<nt:Index>16</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P08B</nt:Name>
			<nt:Index>17</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P09A</nt:Name>
			<nt:Index>18</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P09B</nt:Name>
			<nt:Index>19</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P10A</nt:Name>
			<nt:Index>20</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P10B</nt:Name>
			<nt:Index>21</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P11A</nt:Name>
			<nt:Index>22</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P11B</nt:Name>
			<nt:Index>23</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P12A</nt:Name>
			<nt:Index>24</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P12B</nt:Name>
			<nt:Index>25</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P13A</nt:Name>
			<nt:Index>26</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P13B</nt:Name>
			<nt:Index>27</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P14A</nt:Name>
			<nt:Index>28</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P14B</nt:Name>
			<nt:Index>29</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P15A</nt:Name>
			<nt:Index>30</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RBX P15B</nt:Name>
			<nt:Index>31</nt:Index>
		</nt:Node>
	</nt:NodeType>
	<nt:NodeType>
		<nt:Name>RIO-physical</nt:Name>
		<nt:Node>
			<nt:Name>RIO P00A</nt:Name>
			<nt:Index>0</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P00B</nt:Name>
			<nt:Index>1</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P01A</nt:Name>
			<nt:Index>2</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P01B</nt:Name>
			<nt:Index>3</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P02A</nt:Name>
			<nt:Index>4</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P02B</nt:Name>
			<nt:Index>5</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P03A</nt:Name>
			<nt:Index>6</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P03B</nt:Name>
			<nt:Index>7</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P04A</nt:Name>
			<nt:Index>8</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P04B</nt:Name>
			<nt:Index>9</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P05A</nt:Name>
			<nt:Index>10</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P05B</nt:Name>
			<nt:Index>11</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P06A</nt:Name>
			<nt:Index>12</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P06B</nt:Name>
			<nt:Index>13</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P07A</nt:Name>
			<nt:Index>14</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P07B</nt:Name>
			<nt:Index>15</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P08A</nt:Name>
			<nt:Index>16</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P08B</nt:Name>
			<nt:Index>17</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P09A</nt:Name>
			<nt:Index>18</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P09B</nt:Name>
			<nt:Index>19</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P10A</nt:Name>
			<nt:Index>20</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P10B</nt:Name>
			<nt:Index>21</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P11A</nt:Name>
			<nt:Index>22</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P11B</nt:Name>
			<nt:Index>23</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P12A</nt:Name>
			<nt:Index>24</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P12B</nt:Name>
			<nt:Index>25</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P13A</nt:Name>
			<nt:Index>26</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P13B</nt:Name>
			<nt:Index>27</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P14A</nt:Name>
			<nt:Index>28</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P14B</nt:Name>
			<nt:Index>29</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P15A</nt:Name>
			<nt:Index>30</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>RIO P15B</nt:Name>
			<nt:Index>31</nt:Index>
		</nt:Node>
	</nt:NodeType>
	<nt:NodeType>
		<nt:Name>SYSTEM</nt:Name>
		<nt:Node>
			<nt:Name>SYSTEM</nt:Name>
			<nt:Index>0</nt:Index>
		</nt:Node>
	</nt:NodeType>
	<nt:NodeType>
		<nt:Name>CAR-logical</nt:Name>
		<nt:Node>
			<nt:Name>L1</nt:Name>
			<nt:Index>0</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>L2</nt:Name>
			<nt:Index>1</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>L3</nt:Name>
			<nt:Index>2</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>L4</nt:Name>
			<nt:Index>3</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>L5</nt:Name>
			<nt:Index>4</nt:Index>
		</nt:Node>
	</nt:NodeType>
	<nt:NodeType>
		<nt:Name>CAR-physical</nt:Name>
		<nt:Node>
			<nt:Name>P00</nt:Name>
			<nt:Index>0</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P01</nt:Name>
			<nt:Index>1</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P02</nt:Name>
			<nt:Index>2</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P03</nt:Name>
			<nt:Index>3</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P04</nt:Name>
			<nt:Index>4</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P00</nt:Name>
			<nt:Index>0</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P01</nt:Name>
			<nt:Index>1</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P02</nt:Name>
			<nt:Index>2</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P03</nt:Name>
			<nt:Index>3</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P04</nt:Name>
			<nt:Index>4</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P05</nt:Name>
			<nt:Index>5</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P06</nt:Name>
			<nt:Index>6</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P07</nt:Name>
			<nt:Index>7</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P08</nt:Name>
			<nt:Index>8</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P09</nt:Name>
			<nt:Index>9</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P10</nt:Name>
			<nt:Index>10</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P11</nt:Name>
			<nt:Index>11</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P12</nt:Name>
			<nt:Index>12</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P13</nt:Name>
			<nt:Index>13</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P14</nt:Name>
			<nt:Index>14</nt:Index>
		</nt:Node>
		<nt:Node>
			<nt:Name>P15</nt:Name>
			<nt:Index>15</nt:Index>
		</nt:Node>
	</nt:NodeType>

	<nt:NodeType>
		<nt:Name>LOGGER</nt:Name>
		<nt:Node>
			<nt:Name>LOGGER</nt:Name>
			<nt:Index>0</nt:Index>
		</nt:Node>
	</nt:NodeType>
</nt:EP2002NodeTypes>
