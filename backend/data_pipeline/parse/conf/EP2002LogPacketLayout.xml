<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!-- edited with XMLSpy v2013 (http://www.altova.com) by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (KNORR-BREMSE Systeme für Schienenfahrzeuge GmbH) -->
<df:EP2002DataLoggerFileFormat xmlns:df="http://www.Knorr-Bremse.com/EP2002DataLoggerFileFormat">
	<df:PacketLayout>
		<df:DataItem>
			<!-- this item should stand in front of the others for implementation reasons -->
			<df:Name>Physical Car Code</df:Name>
			<df:MessageType>InternalSignal</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>CAR-logical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>5</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>17</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>1</df:Scaling>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Auxiliary Pressure 1</df:Name>
			<df:MessageType>RBX_ASP_AUXILIARY</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>12</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>32</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.01</df:Scaling>
			<df:Unit>bar</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Auxiliary Pressure 2</df:Name>
			<df:MessageType>RBX_ASP_AUXILIARY</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>12</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>47</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.01</df:Scaling>
			<df:Unit>bar</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>ASP 1 Transducer Status</df:Name>
			<df:MessageType>RBX_ASP_AUXILIARY</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>62</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>ASP 2 Transducer Status</df:Name>
			<df:MessageType>RBX_ASP_AUXILIARY</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>63</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>ASP Errors</df:Name>
			<df:MessageType>RBX_ASP_AUXILIARY</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>2</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>64</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>AUX 1</df:Name>
			<df:MessageType>RBX_ASP_AUXILIARY</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>67</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>AUX 2</df:Name>
			<df:MessageType>RBX_ASP_AUXILIARY</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>68</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>ASP Fit</df:Name>
			<df:MessageType>RBX_ASP_AUXILIARY</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>2</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>69</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>AUX Fit</df:Name>
			<df:MessageType>RBX_ASP_AUXILIARY</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>2</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>72</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>WSP Selftest Status</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>2</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>74</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Emergency Selftest Status</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>2</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>77</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Service Brake Selftest Status</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>2</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>79</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Emergency Jerk Timer</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>82</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>BCP 1 Transducer Status</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>83</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>BCP 2 Transducer Status</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>84</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Low BSR</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>85</df:ByteOffset>
			<df:BitOffset>6</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>VLCP</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>10</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>87</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.01</df:Scaling>
			<df:Unit>bar</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>#Reserved</df:Name>
			<df:MessageType/>
			<df:NodeType/>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>4</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>99</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit/>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>BSRP</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>12</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>100</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.01</df:Scaling>
			<df:Unit>bar</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Bogie Air Consumption</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>115</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>int</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Valve Temperature</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>125</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>C</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>RBX Selftest Status</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>135</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>int</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>VLCP Transducer Status</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>145</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>BSRP Transducer Status</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>146</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Valve temperature status</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>147</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Heater Supply</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>148</df:ByteOffset>
			<df:BitOffset>6</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Heater Status</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>150</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Fail to Release/Apply Mode</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>151</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>VLCP Fitted</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>152</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>BSRP Fitted</df:Name>
			<df:MessageType>RBX_GENERAL_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>153</df:ByteOffset>
			<df:BitOffset>6</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<!--<df:DataItem>
			<df:Name>Maintenance Flags</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes><df:NodeType>RBX-logical</df:NodeType><df:NodeType>RBX-physical</df:NodeType></df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>48</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo><df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>-->
		<df:DataItem>
			<df:Name>evtMRI WSP TIMEOUT</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>evtMRI SW TIMEOUT</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>1</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>evtMRI MISC</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>sysMRI EPROM</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>3</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>sysMRI RAM</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>sysMRI C167 DEVICE</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>5</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>sysMRI EXT DEVICE</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>6</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>sysMRI PROCESS</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>7</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>senMRI TACHO 1</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>8</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>senMRI TACHO 2</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>9</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>senMRI BCP SENSORS</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>10</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>senMRI PRESSURE SENSORS</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>11</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>senMRI VOLTAGE SENSORS</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>12</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>elcMRI WSP ENABLE</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>13</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>elcMRI BRAKES APPLIED</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>14</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>elcMRI HEATER</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>15</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>elcMRI VOLTAGE</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>16</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>elcMRI TIMER 2 LATCH</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>24</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>elcMRI LOW BSR</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>25</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>elcMRI PSU</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>26</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>elcMRI SW TIMERS</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>27</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>elcMRI MISC</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>28</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI BCP</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>29</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI SVB HOLD1</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>30</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI SVB HOLD2</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>31</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI WSP HOLD1</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>32</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI WSP HOLD2</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>33</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI SVB VENT1</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>34</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI SVB VENT2</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>35</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI WSP VENT1</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>36</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI WSP VENT2</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>37</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI LINK VALVE</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>38</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI VLCP</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>39</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI LEAKING</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>40</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI REMOTE RELE ASE</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>41</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>airMRI DRAGGING BR</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>155</df:ByteOffset>
			<df:BitOffset>42</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Time To Test</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>215</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Axle 1 Tacho (MSB)</df:Name>
			<df:MessageType>RBX_TACHO_FREQUENCY_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>216</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>int</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Axle 1 Tacho (LSB)</df:Name>
			<df:MessageType>RBX_TACHO_FREQUENCY_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>226</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>int</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Axle 2 Tacho (MSB)</df:Name>
			<df:MessageType>RBX_TACHO_FREQUENCY_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>236</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>int</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Axle 2 Tacho (LSB)</df:Name>
			<df:MessageType>RBX_TACHO_FREQUENCY_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>246</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>int</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Tacho Status</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>2</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>256</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Tacho Health</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>2</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>258</df:ByteOffset>
			<df:BitOffset>6</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Virtual WSP Segment</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>261</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>GS Master Fail Only</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>262</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Self-test Active</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>263</df:ByteOffset>
			<df:BitOffset>6</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Max Deceleration</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>265</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Super Inlet</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>266</df:ByteOffset>
			<df:BitOffset>2</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>GS Secondary Master</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>267</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>GS Test Master</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>268</df:ByteOffset>
			<df:BitOffset>6</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>ROCOGS</df:Name>
			<df:MessageType>RBX_GS_MASTER</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>GSM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>4</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>270</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>mm/s^2</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Ground Speed</df:Name>
			<df:MessageType>RBX_GS_MASTER</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>GSM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>4</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>278</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.0072</df:Scaling>
			<df:Unit>km/h</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Odometer 1km</df:Name>
			<df:MessageType>RBX_GS_MASTER</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>GSM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>4</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>286</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>km</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Current Loop Fault</df:Name>
			<df:MessageType>RIO_INPUTS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>286</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Current Loop Output</df:Name>
			<df:MessageType>RIO_SET_OUTPUTS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>BCU</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>287</df:ByteOffset>
			<df:BitOffset>6</df:BitOffset>
			<df:Scaling>0.001</df:Scaling>
			<df:Unit>mA</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<!--<df:DataItem>
			<df:Name>Maintenance Flags</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes><df:NodeType>RIO-logical</df:NodeType><df:NodeType>RIO-physical</df:NodeType></df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>48</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>6</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo><df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>-->
		<df:DataItem>
			<df:Name>Maintenance Event ID1</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>7</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID2</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>8</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID3</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>9</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID4</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>10</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID5</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>11</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID6</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>12</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID7</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>13</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID8</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>14</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID9</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>15</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID10</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>16</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID11</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>17</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID12</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>18</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID13</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>19</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID14</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>20</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID15</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>21</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID16</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>22</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID17</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>23</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID18</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>24</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID19</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>25</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID20</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>26</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID21</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>27</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID22</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>28</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID23</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>29</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID24</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>30</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID25</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>31</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID26</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>32</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID27</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>33</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID28</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>34</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID29</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>35</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID30</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>36</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID31</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>37</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID32</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>38</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID33</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>39</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID34</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>40</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID35</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>41</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID36</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>42</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID37</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>43</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID38</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>44</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID39</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>45</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID40</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>46</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID41</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>47</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID42</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>48</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID43</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>49</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID44</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>50</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID45</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>51</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID46</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>52</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID47</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>53</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Maintenance Event ID48</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>307</df:ByteOffset>
			<df:BitOffset>54</df:BitOffset>
			<df:Unit>hex</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
			<df:ElementCount>48</df:ElementCount>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Emergency Level Select</df:Name>
			<df:MessageType>Not in CAN Link Specification</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>367</df:ByteOffset>
			<df:BitOffset>6</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger Board Temperature</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>369</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>C</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CPU Temperature</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>370</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>C</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>#Reserved</df:Name>
			<df:MessageType>(Padding)</df:MessageType>
			<df:NodeType/>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>232</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>371</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit/>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>ASP 1</df:Name>
			<df:MessageType>RBX_ASP_AUXILIARY</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>10</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>400</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.01</df:Scaling>
			<df:Unit>bar</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>ASP 2</df:Name>
			<df:MessageType>RBX_ASP_AUXILIARY</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>10</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>425</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.01</df:Scaling>
			<df:Unit>bar</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Bogie (Axle 1) Target BCP</df:Name>
			<df:MessageType>RBX_BRAKE_DEMAND</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>450</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.001</df:Scaling>
			<df:Unit>bar</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Bogie (Axle 2) Target BCP</df:Name>
			<df:MessageType>RBX_BRAKE_DEMAND</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>490</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.001</df:Scaling>
			<df:Unit>bar</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Ramp Rate (Bogie/Axle 1)</df:Name>
			<df:MessageType>RBX_BRAKE_DEMAND</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>530</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.1</df:Scaling>
			<df:Unit>bar/s</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Ramp Rate (Bogie/Axle 2)</df:Name>
			<df:MessageType>RBX_BRAKE_DEMAND</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>550</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.1</df:Scaling>
			<df:Unit>bar/s</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>WSP Inhibit</df:Name>
			<df:MessageType>RBX_BRAKE_DEMAND</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>570</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Bogie Control</df:Name>
			<df:MessageType>RBX_BRAKE_DEMAND</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>572</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Slip Axle 1</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>575</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.3921568627451</df:Scaling>
			<df:Unit>%</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Slip Axle 2</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>595</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.3921568627451</df:Scaling>
			<df:Unit>%</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Wheel-slide Enabled</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>2</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>615</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Residual Pressure Switches Axle 1</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>620</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Residual Pressure Switches Axle 2</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>622</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Remote Release Demand</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>625</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Axle Speed Out of Range (Axle 1)</df:Name>
			<df:MessageType>RBX_TACHO_FREQUENCY_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>627</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Axle Speed Out of Range (Axle 2)</df:Name>
			<df:MessageType>RBX_TACHO_FREQUENCY_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>630</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Valve Timeout Status</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>2</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>632</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Wheel-slip Indicator</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>637</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>MVB Interface Working</df:Name>
			<df:MessageType>EXTENDED_BM_BRAKE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>BCU</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>2</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>640</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>#Reserved</df:Name>
			<df:MessageType>(Padding)</df:MessageType>
			<df:NodeType/>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>0</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>640</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit/>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>PWM 1 input</df:Name>
			<df:MessageType>RIO_INPUTS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>640</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Scaling>0.3921568627451</df:Scaling>
			<df:Unit>%</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>PWM 2 input</df:Name>
			<df:MessageType>RIO_INPUTS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>660</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Scaling>0.3921568627451</df:Scaling>
			<df:Unit>%</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>PWM 3 input</df:Name>
			<df:MessageType>RIO_INPUTS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>680</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Scaling>0.3921568627451</df:Scaling>
			<df:Unit>%</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>PWM Output 1</df:Name>
			<df:MessageType>RIO_SET_OUTPUTS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>700</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Scaling>0.3921568627451</df:Scaling>
			<df:Unit>%</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>PWM Output 2</df:Name>
			<df:MessageType>RIO_SET_OUTPUTS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>720</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Scaling>0.3921568627451</df:Scaling>
			<df:Unit>%</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>PWM Output 3</df:Name>
			<df:MessageType>RIO_SET_OUTPUTS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>740</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Scaling>0.3921568627451</df:Scaling>
			<df:Unit>%</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>System Conditions OK</df:Name>
			<df:MessageType>RIO_SET_OUTPUTS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>760</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Prime</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>763</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Emergency Level Confirmation</df:Name>
			<df:MessageType>Not in CAN Link Specification</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>1000</df:StoragePeriod>
			<df:ByteOffset>765</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>#Reserved</df:Name>
			<df:MessageType>(Padding)</df:MessageType>
			<df:NodeType/>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>256</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>768</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit/>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Axle 1 Speed</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>800</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.0072</df:Scaling>
			<df:Unit>km/h</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Axle 2 Speed</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1000</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.0072</df:Scaling>
			<df:Unit>km/h</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>RBX Valve States</df:Name>
			<df:MessageType>RBX_WSP_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>5</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1200</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Brake Mode</df:Name>
			<df:MessageType>RBX_BRAKE_DEMAND</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1262</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>BCP Axle 1</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>10</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1275</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.01</df:Scaling>
			<df:Unit>bar</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>BCP Axle 2</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>10</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1400</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.01</df:Scaling>
			<df:Unit>bar</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Brakes Active</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1525</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Emergency Brake Demand</df:Name>
			<df:MessageType>RBX_BRAKING_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1537</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>GS Test Status</df:Name>
			<df:MessageType>RBX_GS_MASTER</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>GSM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>4</df:NodeCount>
			<df:BitsPerItem>2</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1550</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Test Axle 1 status</df:Name>
			<df:MessageType>RBX_GS_MASTER</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>GSM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>4</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1560</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Test Axle 2 status</df:Name>
			<df:MessageType>RBX_GS_MASTER</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>GSM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>4</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1565</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Wheel-slip Indicator</df:Name>
			<df:MessageType>RBX_GS_MASTER</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>GSM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>4</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1570</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Odometer 100m</df:Name>
			<df:MessageType>RBX_GS_MASTER</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>GSM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>4</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1575</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>RBX Health Status</df:Name>
			<df:MessageType>RBX_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RBX-logical</df:NodeType>
				<df:NodeType>RBX-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>3</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1580</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Prime Active</df:Name>
			<df:MessageType>EXTENDED_BM_BRAKE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>BCU</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>2</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1617</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Auto-configuration Active</df:Name>
			<df:MessageType>EXTENDED_BM_BRAKE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>BCU</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>2</df:NodeCount>
			<df:BitsPerItem>1</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1620</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Digital Inputs</df:Name>
			<df:MessageType>RIO_INPUTS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1622</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Digital Outputs</df:Name>
			<df:MessageType>RIO_SET_OUTPUTS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>4</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1722</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Health Status</df:Name>
			<df:MessageType>RIO_MAINTENANCE_STATUS</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>RIO-logical</df:NodeType>
				<df:NodeType>RIO-physical</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>10</df:NodeCount>
			<df:BitsPerItem>3</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1772</df:ByteOffset>
			<df:BitOffset>4</df:BitOffset>
			<df:Unit>binary</df:Unit>
			<df:Visibility>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger Calculated Ground Speed</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>LOGGER</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>200</df:StoragePeriod>
			<df:ByteOffset>1810</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Scaling>0.036</df:Scaling>
			<df:Unit>km/h</df:Unit>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH0 RX Error Count</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>32</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1830</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH0 TX Error Count</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>32</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1834</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH0 none</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1838</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH0 stuff</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1840</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH0 form</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1842</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH0 ACK</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1844</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH0 bit1</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1846</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH0 bit0</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1848</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH0 CRC</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1850</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH0 CPU write</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1852</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH1 RX Error Count</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>32</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1854</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH1 TX Error Count</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>32</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1858</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH1 none</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1862</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH1 stuff</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1864</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH1 form</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1866</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH1 ACK</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1868</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH1 bit1</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1870</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH1 bit0</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1872</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH1 CRC</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1874</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH1 CPU write</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1876</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH2 RX Error Count</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>32</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1878</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH2 TX Error Count</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>32</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1882</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH2 none</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1886</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH2 stuff</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1888</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH2 form</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1890</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH2 ACK</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1892</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH2 bit1</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1894</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH2 bit0</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1896</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH2 CRC</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1898</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH2 CPU write</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1900</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH3 RX Error Count</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>32</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1902</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH3 TX Error Count</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>32</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1906</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH3 none</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1910</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH3 stuff</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1912</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH3 form</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1914</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH3 ACK</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1916</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH3 bit1</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1918</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH3 bit0</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1920</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH3 CRC</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1922</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger CH3 CPU write</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>16</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1924</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger Current CAN Loading</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1926</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger Average CAN Loading</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1927</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>Data Logger Peak CAN Loading</df:Name>
			<df:MessageType>(Data Logger)</df:MessageType>
			<df:NodeTypes>
				<df:NodeType>SYSTEM</df:NodeType>
			</df:NodeTypes>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>8</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1928</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:DataItem>
			<df:Name>#Reserved</df:Name>
			<df:MessageType/>
			<df:NodeType/>
			<df:NodeCount>1</df:NodeCount>
			<df:BitsPerItem>936</df:BitsPerItem>
			<df:StoragePeriod>2000</df:StoragePeriod>
			<df:ByteOffset>1929</df:ByteOffset>
			<df:BitOffset>0</df:BitOffset>
			<df:Visibility>
				<df:VisibleTo>all</df:VisibleTo>
				<df:VisibleTo>kb</df:VisibleTo>
			</df:Visibility>
		</df:DataItem>
		<df:StoragePeriod>2000</df:StoragePeriod>
		<df:Version>3</df:Version>
	</df:PacketLayout>
</df:EP2002DataLoggerFileFormat>
