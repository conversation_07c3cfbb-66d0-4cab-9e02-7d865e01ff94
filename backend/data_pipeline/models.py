from django.db import models
from dvadmin.utils.models import CoreModel, table_prefix

class DataPipelineProject(CoreModel):
    """
    数据管道项目模型
    """

    TEMPLATE_TYPE_CHOICES = (
        (0, "EP2002"),
        (1, "ASU"),
        (2, "PHM"),
    )

    project_number = models.CharField(max_length=32, unique=True, verbose_name="项目编号", help_text="项目编号")
    project_name = models.CharField(max_length=128, verbose_name="项目名称", help_text="项目名称")
    customer_name = models.CharField(max_length=128, verbose_name="客户名称", help_text="客户名称")
    template_type = models.IntegerField(choices=TEMPLATE_TYPE_CHOICES, default=0, verbose_name="模板类型", help_text="模板类型")

    class Meta:
        db_table = table_prefix + "data_pipeline_project"
        verbose_name = "数据管道项目"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)

    def __str__(self):
        return f"{self.project_name}({self.project_number})"


class DataPipelineFile(CoreModel):
    """
    数据管道文件模型
    """
    FILE_STATUS_CHOICES = (
        (0, "上传中"),
        (1, "已上传"),
        (2, "处理中"),
        (3, "已完成"),
        (4, "失败")
    )

    # 使用项目编号而不是外键关联
    project_number = models.CharField(max_length=32, verbose_name="项目编号", help_text="项目编号")
    file_name = models.CharField(max_length=255, verbose_name="文件名称", help_text="文件名称")
    file_path = models.CharField(max_length=500, verbose_name="MinIO存储路径", help_text="MinIO存储路径")
    file_size = models.BigIntegerField(verbose_name="文件大小(字节)", help_text="文件大小(字节)")
    file_status = models.IntegerField(choices=FILE_STATUS_CHOICES, default=0, verbose_name="文件状态", help_text="文件状态")
    version = models.IntegerField(default=1, verbose_name="文件版本", help_text="文件版本")
    
    # 新增字段
    start_time = models.DateTimeField(null=True, blank=True, verbose_name="开始时间", help_text="任务/处理开始时间")
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="结束时间", help_text="任务/处理结束时间")
    file_Minio_path = models.CharField(null=True, blank=True, max_length=500, verbose_name="文件对应的MinIO存储路径", help_text="文件对应的MinIO存储路径")
    file_Minio_version_id = models.CharField(null=True, blank=True, max_length=500, verbose_name="文件对应的MinIO_ID", help_text="文件对应的MinIO_ID")
    influx_ubique_tagId = models.CharField(null=True, blank=True, max_length=500, verbose_name="文件上传操作对应的Influx_UniqueTag", help_text="文件上传操作对应的Influx_UniqueTag")
    fileFolder_name = models.CharField(null=True, blank=True, max_length=500, verbose_name="文件夹名称", help_text="文件夹名称")
    fileFolder_startTime = models.DateTimeField(null=True, blank=True, verbose_name="文件夹起始时间", help_text="上传的bin文件夹总体解析出的起始时间")
    fileFolder_endTime = models.DateTimeField(null=True, blank=True, verbose_name="文件夹结束时间", help_text="上传的bin文件夹总体解析出的结束时间")
    
    class Meta:
        db_table = table_prefix + "data_pipeline_file"
        verbose_name = "数据管道文件"
        verbose_name_plural = verbose_name
        ordering = ("-create_datetime",)
        unique_together = (("project_number", "file_name", "version"),)  # 同一项目下的同名文件需要用版本区分

    def __str__(self):
        return f"{self.project_number}:{self.file_name}(v{self.version})" 