#!/usr/bin/env python3
"""
EP2002 Binary Log File Parser

This script parses EP2002 binary log files (.bin) and extracts the file header,
record headers, and signal data based on the EP2002LogPacketLayout.xml configuration.

Usage:
    python log_file_parser.py <bin_file> [xml_config_file]
    python log_file_parser.py --dir <directory> --xml <xml_file> --output <output_dir>
"""

import os
import sys
import struct
import datetime
import xml.etree.ElementTree as ET
from collections import namedtuple, defaultdict
import argparse
import json
import glob
import csv

# Constants from the C# code
EP2002_LOG_FILE_ID = 0xDEAD
EP2002_LOG_PACKET_SIZE = 2048
EP2002_SECONDS_PER_SAMPLE = 2
EP2002_MILLISECONDS_PER_SAMPLE = EP2002_SECONDS_PER_SAMPLE * 1000

# Define structures for the file header and record header
FileHeader = namedtuple('FileHeader', [
    'block_id', 'file_flags', 'sw_version_major', 'sw_version_minor',
    'sw_version_stroke', 'timestamp', 'sequence', 'data_size',
    'dallas_id', 'session_id'
])

RecordHeader = namedtuple('RecordHeader', [
    'record_id', 'packet_version_major', 'packet_version_minor',
    'sample_cycles', 'session_sequence', 'timestamp'
])

# Signal definition from XML
Signal = namedtuple('Signal', [
    'name', 'message_type', 'node_types', 'node_count', 'bits_per_item',
    'storage_period', 'byte_offset', 'bit_offset', 'scaling', 'unit',
    'element_count'
])

def epoch_to_datetime(seconds):
    """Convert UNIX/Epoch time to a datetime object."""
    return datetime.datetime(1970, 1, 1) + datetime.timedelta(seconds=seconds)

def extract_bits(data, start_bit, num_bits):
    """Extract bits from a byte array."""
    # Calculate the byte index and bit offset within that byte
    start_byte = start_bit // 8
    bit_offset = start_bit % 8
    
    # Extract the required bytes
    num_bytes = (bit_offset + num_bits + 7) // 8
    if start_byte + num_bytes > len(data):
        return 0  # Not enough data
    
    # Convert the bytes to an integer
    value = 0
    for i in range(num_bytes):
        if start_byte + i < len(data):
            value |= data[start_byte + i] << (i * 8)
    
    # Mask out the bits we want
    value >>= bit_offset
    mask = (1 << num_bits) - 1
    return value & mask

def parse_xml_config(xml_file):
    """Parse the EP2002LogPacketLayout.xml file to get signal definitions."""
    signals = []
    
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # Handle namespace in the XML
        ns = {'df': 'http://www.Knorr-Bremse.com/EP2002DataLoggerFileFormat'}
        
        # Get packet version
        packet_version = root.find('.//df:PacketLayout/df:Version', ns).text
        
        # Get all data items
        for data_item in root.findall('.//df:PacketLayout/df:DataItem', ns):
            # Skip placeholders and commented out signals
            name = data_item.find('df:Name', ns).text
            if name.startswith('#'):
                continue
            
            # Get node types
            node_types = []
            for node_type in data_item.findall('df:NodeTypes/df:NodeType', ns):
                node_types.append(node_type.text)
            
            # Get message type
            message_type_elem = data_item.find('df:MessageType', ns)
            message_type = message_type_elem.text if message_type_elem is not None else ""
            
            # Get other properties
            node_count = int(data_item.find('df:NodeCount', ns).text)
            bits_per_item = int(data_item.find('df:BitsPerItem', ns).text)
            storage_period = int(data_item.find('df:StoragePeriod', ns).text)
            byte_offset = int(data_item.find('df:ByteOffset', ns).text)
            
            bit_offset_elem = data_item.find('df:BitOffset', ns)
            bit_offset = int(bit_offset_elem.text) if bit_offset_elem is not None else 0
            
            scaling_elem = data_item.find('df:Scaling', ns)
            scaling = float(scaling_elem.text) if scaling_elem is not None else 1.0
            
            unit_elem = data_item.find('df:Unit', ns)
            unit = unit_elem.text if unit_elem is not None else ""
            
            element_count_elem = data_item.find('df:ElementCount', ns)
            element_count = int(element_count_elem.text) if element_count_elem is not None else 1
            
            # Calculate overall bit offset
            overall_bit_offset = byte_offset * 8 + bit_offset
            
            for node_type in node_types:
                signal = Signal(
                    name=name,
                    message_type=message_type,
                    node_types=[node_type],
                    node_count=node_count,
                    bits_per_item=bits_per_item,
                    storage_period=storage_period,
                    byte_offset=byte_offset,
                    bit_offset=bit_offset,
                    scaling=scaling,
                    unit=unit,
                    element_count=element_count
                )
                signals.append(signal)
        
        return signals, packet_version
    
    except Exception as e:
        print(f"Error parsing XML config: {e}")
        return [], "unknown"

def parse_bin_file(bin_file, signals=None, verbose=True):
    """Parse an EP2002 binary log file."""
    try:
        with open(bin_file, 'rb') as f:
            # Check file size
            f.seek(0, os.SEEK_END)
            file_size = f.tell()
            f.seek(0)
            
            if file_size < 64 + EP2002_LOG_PACKET_SIZE:  # Minimum size check
                print(f"File {bin_file} is too small to be a valid EP2002 log file")
                return None
            
            # Read and check file ID
            file_id = struct.unpack('<H', f.read(2))[0]
            if file_id != EP2002_LOG_FILE_ID:
                print(f"File {bin_file} is not a valid EP2002 log file (ID: {file_id:04X})")
                return None
            
            # Go back to the beginning
            f.seek(0)
            
            # Read file header (64 bytes)
            header_data = f.read(64)
            header = FileHeader(
                block_id=struct.unpack('<H', header_data[0:2])[0],
                file_flags=struct.unpack('<H', header_data[2:4])[0],
                sw_version_major=header_data[4],
                sw_version_minor=header_data[5],
                sw_version_stroke=struct.unpack('<H', header_data[6:8])[0],
                timestamp=struct.unpack('<I', header_data[8:12])[0],
                sequence=struct.unpack('<I', header_data[12:16])[0],
                data_size=struct.unpack('<I', header_data[16:20])[0],
                dallas_id=struct.unpack('<Q', header_data[20:28])[0],
                session_id=struct.unpack('<i', header_data[28:32])[0]
            )
            
            # Calculate actual data size
            actual_data_size = file_size - 64  # 64 is the header size
            packet_count = actual_data_size // EP2002_LOG_PACKET_SIZE
            
            if verbose:
                print(f"File: {bin_file}")
                print(f"Software Version: {header.sw_version_major}.{header.sw_version_minor}.{header.sw_version_stroke}")
                print(f"Session ID: {header.session_id}")
                print(f"Dallas ID: {header.dallas_id:016X}")
                print(f"Timestamp: {epoch_to_datetime(header.timestamp)}")
                print(f"Sequence Number: {header.sequence}")
                print(f"Data Size: {header.data_size} bytes")
                print(f"Actual Data Size: {actual_data_size} bytes")
                print(f"Packet Count: {packet_count}")
                print()
            
            # Read all records
            records = []
            signal_data = defaultdict(lambda: defaultdict(list))
            
            for i in range(packet_count):
                # Read record header (32 bytes)
                record_header_data = f.read(32)
                record_header = RecordHeader(
                    record_id=struct.unpack('<H', record_header_data[0:2])[0],
                    packet_version_major=record_header_data[2],
                    packet_version_minor=record_header_data[3],
                    sample_cycles=struct.unpack('<i', record_header_data[4:8])[0],
                    session_sequence=struct.unpack('<i', record_header_data[8:12])[0],
                    timestamp=struct.unpack('<i', record_header_data[12:16])[0]
                )
                
                # Read the rest of the packet
                packet_data = f.read(EP2002_LOG_PACKET_SIZE - 32)
                
                # Combine header and data for full packet
                full_packet = record_header_data + packet_data
                
                # Record start time
                start_time = epoch_to_datetime(record_header.timestamp)
                
                record = {
                    'header': record_header,
                    'start_time': start_time,
                    'data': full_packet
                }
                records.append(record)
                
                # Parse signals if provided
                if signals:
                    for signal in signals:
                        sample_bit_offset = signal.byte_offset * 8 + signal.bit_offset
                        sample_count = EP2002_MILLISECONDS_PER_SAMPLE // signal.storage_period
                        
                        # First timestamp is the timestamp of the record
                        sample_timestamp = start_time
                        
                        # 添加循环，处理每个采样点
                        for j in range(sample_count):
                            samples = []
                            for k in range(signal.node_count):
                                bit_pos = sample_bit_offset + k * signal.bits_per_item * signal.element_count
                                value = extract_bits(full_packet, bit_pos, signal.bits_per_item)
                                
                                # Apply scaling if needed
                                if signal.scaling != 1.0:
                                    value = value * signal.scaling
                                
                                samples.append(value)
                            
                            # 存储当前时间点的样本数据
                            signal_data[signal.name][sample_timestamp] = [samples]
                            
                            # Update bit offset for next sample
                            sample_bit_offset += signal.node_count * signal.bits_per_item * signal.element_count
                            
                            # Update timestamp for next sample
                            sample_timestamp += datetime.timedelta(milliseconds=signal.storage_period)
            
            return {
                'header': header,
                'records': records,
                'signal_data': signal_data if signals else None
            }
    
    except Exception as e:
        print(f"Error parsing bin file: {e}")
        return None

def format_signal_unit(unit):
    """Format signal unit according to C# code"""
    # Internal units should not be displayed
    internal_units = {'hex', 'binary'}  # Add other internal units if needed
    return f"({unit})" if unit and unit not in internal_units else ""

def write_csv_output(output_file, result, signals, node_mappings):
    """将信号数据写入 CSV 文件，格式匹配 GenerateTableData 函数的输出"""
    # 创建一个字典来存储每个信号的所有时间戳
    all_timestamps = set()
    signal_data_map = {}
    
    # 首先收集所有时间戳和组织数据结构
    if result['signal_data']:
        for signal_name, timestamps in result['signal_data'].items():
            signal_config = next((s for s in signals if s.name == signal_name), None)
            if not signal_config:
                continue
                
            node_type = signal_config.node_types[0]
            node_mapping = node_mappings.get(node_type, {})
            
            # 为每个信号创建数据结构
            signal_data_map[signal_name] = {
                'config': signal_config,
                'node_mapping': node_mapping,
                'data': {}  # {timestamp: {node_index: value}}
            }
            
            # 收集数据和时间戳
            for timestamp, samples_list in timestamps.items():
                all_timestamps.add(timestamp)
                for samples in samples_list:
                    if timestamp not in signal_data_map[signal_name]['data']:
                        signal_data_map[signal_name]['data'][timestamp] = {}
                    for i, value in enumerate(samples):
                        if i < signal_config.node_count:
                            signal_data_map[signal_name]['data'][timestamp][i] = value
    
    # 将时间戳排序
    all_timestamps = sorted(list(all_timestamps))
    
    # 准备 CSV 头
    csv_headers = ['Timestamp']
    signal_columns = []  # 用于跟踪每个列对应的信号和节点
    
    # 为每个信号的每个节点创建列
    for signal_name, signal_info in signal_data_map.items():
        config = signal_info['config']
        node_mapping = signal_info['node_mapping']
        
        for i in range(config.node_count):
            node_name = node_mapping.get(i, f"Node_{i}")
            column_name = f"{signal_name} - {node_name} ({config.unit})"
            csv_headers.append(column_name)
            signal_columns.append((signal_name, i))
    
    # 写入 CSV 文件
    with open(output_file, 'w', newline='') as f:   
        writer = csv.writer(f)
        writer.writerow(csv_headers)
        
        # 写入数据行
        for timestamp in all_timestamps:
            row = [timestamp]
            
            # 为每个信号列添加数据
            for signal_name, node_index in signal_columns:
                value = signal_data_map[signal_name]['data'].get(timestamp, {}).get(node_index, '')
                row.append(value)
            
            writer.writerow(row)

def parse_node_types(xml_file):
    """解析 EP2002NodeTypes.xml 文件以获取节点映射关系"""
    node_mappings = {}  # {node_type: {index: node_name}}
    
    try:
        tree = ET.parse(xml_file)
        root = tree.getroot()
        
        # 处理 XML 中的命名空间
        ns = {'nt': 'http://www.Knorr-Bremse.com/EP2002NodeTypes'}
        
        # 获取所有节点类型
        for node_type in root.findall('.//nt:NodeType', ns):
            type_name = node_type.find('nt:Name', ns).text
            node_mappings[type_name] = {}
            
            # 获取该类型下的所有节点
            for node in node_type.findall('nt:Node', ns):
                node_name = node.find('nt:Name', ns).text
                node_index = int(node.find('nt:Index', ns).text)
                node_mappings[type_name][node_index] = node_name
    
    except Exception as e:
        print(f"解析节点类型 XML 配置时出错: {e}")
        return {}
    
    return node_mappings

def print_parse_results(result, signals, node_mappings, max_signals=5, max_samples=3):
    """打印解析结果的示例数据"""
    print("\n解析结果示例:")
    print("=" * 80)
    
    # 打印文件头信息
    print("文件信息:")
    print(f"  软件版本: {result['header'].sw_version_major}.{result['header'].sw_version_minor}.{result['header'].sw_version_stroke}")
    print(f"  会话 ID: {result['header'].session_id}")
    print(f"  Dallas ID: {result['header'].dallas_id:016X}")
    print(f"  开始时间: {epoch_to_datetime(result['header'].timestamp)}")
    print(f"  序列号: {result['header'].sequence}")
    print(f"  数据包数量: {len(result['records'])}")
    
    # 打印信号数据示例
    if result['signal_data']:
        print("\n信号数据示例:")
        signal_count = 0
        
        for signal_name, timestamps in result['signal_data'].items():
            if signal_count >= max_signals:
                print("\n... (还有更多信号)")
                break
                
            signal_config = next((s for s in signals if s.name == signal_name), None)
            if not signal_config:
                continue
                
            node_type = signal_config.node_types[0]
            node_mapping = node_mappings.get(node_type, {})
            
            print(f"\n信号: {signal_name}")
            print(f"  消息类型: {signal_config.message_type}")
            print(f"  节点类型: {node_type}")
            print(f"  单位: {signal_config.unit}")
            print(f"  采样周期: {signal_config.storage_period} ms")
            
            # 打印一些样本值
            print("  数据示例:")
            sample_count = 0
            for timestamp, samples_list in sorted(timestamps.items()):
                if sample_count >= max_samples:
                    print("    ...")
                    break
                    
                print(f"    时间: {timestamp}")
                for samples in samples_list:
                    print("    节点值:", end=" ")
                    for i, value in enumerate(samples):
                        if i < signal_config.node_count:
                            node_name = node_mapping.get(i, f"Node_{i}")
                            print(f"{node_name}: {value:.3f}", end=", ")
                    print()
                sample_count += 1
            
            signal_count += 1
    
    print("\n" + "=" * 80)

def main():
    parser = argparse.ArgumentParser(description='解析 EP2002 二进制日志文件')
    parser.add_argument('--bin_file', help='二进制日志文件的路径', default='./data/LOG00739.BIN')
    parser.add_argument('--xml', help='信号配置 XML 文件的路径', 
                        default='../Config/EP2002LogPacketLayout.xml')
    parser.add_argument('--node-types', help='节点类型 XML 文件的路径',
                        default='../Config/EP2002NodeTypes.xml')
    parser.add_argument('--output', help='输出 CSV 文件的路径',
                        default=None)
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.isfile(args.bin_file):
        print(f"错误: 未找到二进制文件 {args.bin_file}")
        return 1
    
    if not os.path.isfile(args.xml):
        print(f"错误: 未找到信号配置文件 {args.xml}")
        return 1
        
    if not os.path.isfile(args.node_types):
        print(f"错误: 未找到节点类型配置文件 {args.node_types}")
        return 1
    
    # 设置默认输出文件名
    if args.output is None:
        args.output = os.path.splitext(args.bin_file)[0] + '.csv'
    
    # 解析配置文件
    signals, packet_version = parse_xml_config(args.xml)
    print(f"从信号配置加载了 {len(signals)} 个信号")
    
    node_mappings = parse_node_types(args.node_types)
    print(f"从节点类型配置加载了 {len(node_mappings)} 个节点类型")
    
    # 解析二进制文件
    result = parse_bin_file(args.bin_file, signals)
    
    if result:
        # 显示解析结果示例
        print_parse_results(result, signals, node_mappings, 300)
        
        # 写入 CSV 文件
        write_csv_output(args.output, result, signals, node_mappings)
        print(f"\n输出已写入 {args.output}")
        return 0
    
    return 1

if __name__ == "__main__":
    sys.exit(main())
