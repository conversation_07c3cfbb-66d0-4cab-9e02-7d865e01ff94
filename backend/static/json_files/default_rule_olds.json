[{"chapter_name": "1. WARRANTY / DEFECTS LIABILITY / LCC AND RELIABILITY", "terms": [{"term_name": "1.1 - Warranty Period / Defects Liability Period", "rules": [{"rule_id": 1, "rule_description": "General OE: > 24 months after commissioning/acceptance or 36 months after delivery by KB\nFreight Cars: > 24 months after commissioning/acceptance or 72 months after delivery by KB\nRailServices: > 12 months after installation or 24 months after delivery by KB\n", "prompt": "The following prompt has the highest priority: If the contract content exceeds the rule's warranty period limits, set Determination Result to true. If the relevant content does not exceed the rule's warranty period limits, set Determination Result to false. If there is no relevant contract content, set Determination Result to null. Special attention is often paid to the 36-month cycle after delivery.", "content_for_embedding": "Warranty period, normal warranty, months after delivery or related terms of quality assurance."}]}, {"term_name": "1.2 - Life Cycle Costs (LCC) Commitments", "rules": [{"rule_id": 2, "rule_description": "LCC is not calculated as average per sub-system and average per fleet and over complete commitment period\n", "prompt": "If hasBindingLCC is false or there is no binding LCC, set Determination Result to false and stop. If hasBindingLCC is true but LCC is not calculated as average per sub-system and average per fleet over complete commitment period, set Determination Result to true.", "content_for_embedding": "Life Cycle Costs (LCC) commitments and calculations."}]}, {"term_name": "1.3 - Serial Defect", "rules": [{"rule_id": 3, "rule_description": "Additional Liability for serial defects which goes beyond liability for defects arising from the same cause (1) which occur in ≤10% of the goods supplied (2) within any period of time exceeding consecutive 12 months\n", "prompt": "Set the Determination Result to true if any of the following conditions are met: if any of the following conditions are met:\n\nThe warranty period exceeds 12 consecutive months;\nThere are series defects caused by the same reason;\nThe number of defects is less than 10%.", "content_for_embedding": "Serial defects liability and conditions, consecutive 12 months, arising from the same cause, 10% of the parts or goods supplied."}]}, {"term_name": "1.4 - <PERSON><PERSON> for Purpose", "rules": [{"rule_id": 4, "rule_description": "'Fit-for-Purpose' commitments which go beyond the contractual agreed technical specification of the contract goods\n", "prompt": "If the Contract Clauses contains \"Fit-for-Purpose\" commitments, set Determination Result to true.", "content_for_embedding": "Fit-for-Purpose commitments and technical specifications."}]}]}, {"chapter_name": "2. LIQUIDATED DAMAGES (LD) / PENALTIES", "terms": [{"term_name": "2.1", "rules": [{"rule_id": 5, "rule_description": "Aggregate LD/Penalty for delay not capped at ≤10% of the total contract value\n", "prompt": "The following prompt has the highest priority: If the liquidated damages or Penalty for delay delivery is capped at ≤10% of the total contract value, set Determination Result to false. If the LD/Penalty for delay is capped at >10% of the total contract value, set Determination Result to true. If there is no relevant contract content, set Determination Result to null.", "content_for_embedding": "Liquidated Damages or Penalties for delay and their caps."}]}, {"term_name": "2.2", "rules": [{"rule_id": 6, "rule_description": "Any LDs/Penalties for issues other than delay which are not capped at ≤10% of the total contract value\n", "prompt": "The following prompt has the highest priority: If the LD/Penalty for issues other than delay is capped at ≤10% of the total contract value, set Determination Result to false. If the LD/Penalty for issues other than delay is capped at >10% of the total contract value, set Determination Result to true. If there is no relevant contract content, set Determination Result to null.", "content_for_embedding": "Liquidated Damages or Penalties for non-delay issues, including their caps."}]}]}, {"chapter_name": "3. LIABILITY / LIMITATION OF LIABILITY", "terms": [{"term_name": "3.1 - Limitation of Liability", "rules": [{"rule_id": 7, "rule_description": "Aggregate liability not capped at ≤20% of the total contract value\nRailServices/Zelisko/Microelettrica/Heine: Aggregate liability not capped at ≤20% of the contract value p.a. or (alternatively) not capped at ≤ Euro 500.000 p.a.\nNote: Call-offs from and/or options of approved GTC / Framework / Platform Agreements are not required to be approved again\n", "prompt": "This rule is triggered only if the contract specifies an aggregate liability limit greater than 20% of the total contract value. If the liability is capped at 20% or less, the rule is not triggered.", "content_for_embedding": "Aggregate liability caps and limitations."}, {"rule_id": 8, "rule_description": "Maximum liability > Euro 5 Mio. but ≤ Euro 10 Mio. or\nRailServices contract without limitation of liability and contract value ≤ Euro 500.000\n", "prompt": "If the contract is not RailService, set Determination Result to null.", "content_for_embedding": "Maximum liability amounts and RailServices contract limitations."}, {"rule_id": 9, "rule_description": "Maximum liability > Euro 10 Mio or\nRailServices contract without limitation of liability and contract value > Euro 500.000 or\nOE contract without limitation of liability\n", "prompt": "If the contract is not RailService, set Determination Result to null.", "content_for_embedding": "High-value liability limits and contracts excluding liability limitations."}]}, {"term_name": "3.2 - Exceptions from the Limitation of Liability", "rules": [{"rule_id": 10, "rule_description": "Any exceptions from the limitation of liability, except:\nLiability for personal injury or death\nLiability for losses caused by fraud, wilful misconduct and gross negligence\nLiability for mandatory third-party product liability claims\nIndemnification for IPR infringement\n", "prompt": "Only the following four situations can be exceptions to liability limitations: Personal injury or death Losses caused by intentional fraudulent behavior Product liability assumed by law Intellectual property infringement Any exception clauses beyond the above scope should set the Determination Result to true.", "content_for_embedding": "Exceptions to liability limitations and their scope, except liability for personal injury, death, fraud, wilful misconduct, gross negligence, third party product claims, and IPR infringement indemnification."}]}, {"term_name": "3.3 - Liability for Indirect Losses / Consequential Damages", "rules": [{"rule_id": 11, "rule_description": "No exclusion of liability for indirect losses / consequential damages, in particular:\nloss of profit or revenue,\nlosses due to production stoppage,\ninterruption of operations or loss of use,\nloss of information and data\n", "prompt": "The contract must explicitly exclude liability for indirect losses, including but not limited to: Loss of profits or revenue; Loss due to production stoppage; Operational interruption or loss of use Loss of information and data; If the liability for indirect losses is not explicitly excluded, set the Determination Result to true.", "content_for_embedding": "Indirect losses (Losses: profit/revenue, production stoppage, operational interruption, use loss, data loss.) and consequential damages liability exclusions."}]}, {"term_name": "3.4 - Joint and Several Liability", "rules": [{"rule_id": 12, "rule_description": "Joint and several liability for the scope of work of companies which are not consolidated within KB Group, e.g. in consortium agreements\n", "prompt": "If either of the below scenarios is met, set the Determination Result to true: Jointly and severally liable with non-KB Group affiliated companies; Jointly and severally liable with private enterprises or distributors(except Party A's company) in a consortium.", "content_for_embedding": "Joint and several liability with non-KB Group companies."}]}]}, {"chapter_name": "4. CHANGES / TERMINATION FOR CONVENIENCE / SUSPENSION OF CONTRACT", "terms": [{"term_name": "4.1 - Changes", "rules": [{"rule_id": 13, "rule_description": "No regulation about compensation for KB for additional costs through changes of technical specifications, scope of supply or technical state of the art after contract signature\nNo regulation about compensation for KB for additional costs through changes of law after contract signature\n", "prompt": "set the Determination Result to false if any of the following situations occur:\nParty A unilaterally requests changes and agrees to compensate Party B (KB) for the additional costs incurred.\nAdditional costs are incurred by Party B (KB) due to relevant changes, but both parties reach an agreement.\nSet the Determination Result to true if any of the following statements are not present:\nCompensation for the loss/cost increase of Party B (KB) due to changes in technical specifications after the contract is signed.\nCompensation for the loss/cost increase of Party B (KB) due to changes in the scope of supply after the contract is signed.\nCompensation for the loss/cost increase of Party B (KB) due to changes in the technical state of the art after the contract is signed.", "content_for_embedding": "Compensation for changes in specifications, scope, or technical requirements."}]}, {"term_name": "4.2 - Design Freeze", "rules": [{"rule_id": 14, "rule_description": "OE business: Delivery date not linked to a design freeze (unless off-the-shelf product)\n", "prompt": "If the contract type is not OE, then set Determination Result to null. If the contract type is OE, determine the design freeze point, such as 3-6 months before the delivery of the first train or a specific date. If there is no time specified, set the Determination Result to true.", "content_for_embedding": "Relationship between design freeze, delivery date, and delivery request in OE business."}]}, {"term_name": "4.3 - Termination of Contract", "rules": [{"rule_id": 15, "rule_description": "Termination of contract by customer for convenience and KB is not reimbursed for costs incurred (IFRS requirement)\n", "prompt": "The Determination Result should be set to true if any of the following situations occur:\n\nThe customer has the right to unilaterally terminate the contract for their own convenience without agreeing to compensate Party B (KB) for losses.\nThe customer has the right to unilaterally terminate the contract for their own reasons without agreeing to compensate.", "content_for_embedding": "Contract termination or order cancellation for convenience, including cost reimbursement."}]}, {"term_name": "4.4 - Suspension of Contract", "rules": [{"rule_id": 16, "rule_description": "Suspension of contract by Customer and the following conditions are not met:\nKB has the right to adjust delivery schedule or will be reimbursed for any additional costs\nKB has the right to terminate the contract in case the suspension lasts longer than 6 months or will be reimbursed for costs incurred\n", "prompt": "If any of the following situations is not met, set the Determination Result to true: KB has the right to adjust delivery schedule or will be reimbursed for any additional costs • KB has the right to terminate the contract in case the suspension lasts longer than 6 months or will be reimbursed for costs incurred.", "content_for_embedding": "Contract suspension, postponement or adjustment rights and compensation conditions."}]}]}, {"chapter_name": "5. SPARE PARTS / SUPPLIER WAREHOUSE / DOUBLE SOURCING", "terms": [{"term_name": "5.1 - Supply of Spare Parts", "rules": [{"rule_id": 17, "rule_description": "Delivery commitment of more than 30 years\n", "prompt": "If the spare parts supply period exceeds 30 years after the delivery of the OE product, set the Determination Result to true.", "content_for_embedding": "Delivery commitment period for spare parts, replacements, substitutes, and associated accessories."}, {"rule_id": 18, "rule_description": "Fixed prices for spare parts 2 years after first delivery of OE equipment by KB\n", "prompt": "If the spare parts price fixing period exceeds 2 years after the delivery of the OE equipment, set the Determination Result to true.", "content_for_embedding": "Fixed pricing period for spare parts or associated accessories."}]}, {"term_name": "5.2 - Advanced Supplier Warehouse", "rules": [{"rule_id": 19, "rule_description": "Request for Advanced Supplier Warehouse\n", "prompt": "set Determination Result to null.", "content_for_embedding": "Advanced Supplier Warehouse requirements."}]}, {"term_name": "5.3 - Double Sourcing Friction Material", "rules": [{"rule_id": 20, "rule_description": "Double sourcing requirement for friction material (disk and/or pads).\n", "prompt": "set Determination Result to null.", "content_for_embedding": "Double sourcing requirements for friction materials."}]}]}, {"chapter_name": "6. INTELLECTUAL PROPERTY / DOCUMENTATION / SOURCE CODE ESCROW / DATA", "terms": [{"term_name": "6.1 - IP License / Transfer", "rules": [{"rule_id": 21, "rule_description": "Any transfer/assignment of ownership or exclusive license of KB IP rights or know-how (no matter as to whether such rights or know how are pre-existing or are developed during the respective project)\n", "prompt": "If the contract clause includes any transfer/assignment of ownership or exclusive license of KB IP rights or know-how (pre-existing or developed during the project), return true; otherwise, return false.", "content_for_embedding": "IP rights transfer, attribution or exclusive licensing terms."}, {"rule_id": 22, "rule_description": "Any license or sub-license of KB IP rights other than for the purpose of operating and maintaining the contract goods.\n", "prompt": "If the contract clause grants any license or sub-license of KB IP rights for purposes other than operating and maintaining the contract goods, return true; otherwise, return false.", "content_for_embedding": "IP rights licensing scope and limitations of products maintenance."}]}, {"term_name": "6.2 - Technical Information / Documentation", "rules": [{"rule_id": 23, "rule_description": "Submission of sensitive technical information to customer and/or end-user without contractual protection for KB, e.g. via NDA, copyright disclaimer.\n", "prompt": "If the contract involves submitting sensitive technical information to the customer or end-user without contractual protection for KB, return true; otherwise, return false.", "content_for_embedding": "Sensitive technical information protection, confidentiality clause and disclosure terms."}, {"rule_id": 24, "rule_description": "Submission of Overhaul Documentation to customer (car-builder) and/or end-user\n", "prompt": "If the contract clause requires submission of overhaul documentation to the customer (car-builder) and/or end-user, return true; otherwise, return false.", "content_for_embedding": "Overhaul documentation submission requirements, carbuilder/end user."}]}, {"term_name": "6.3 - Source Code and other Escrow Agreements", "rules": [{"rule_id": 25, "rule_description": "Obligation to enter into any project related Source Code Escrow Agreement or other Escrow Agreement which deviates from the KB Group standard(s)\n", "prompt": "If the contract requires entering into a Source Code Escrow Agreement or any Escrow Agreement that deviates from KB Group standards, return true; otherwise, return false.", "content_for_embedding": "Source code escrow agreement requirements."}, {"rule_id": 26, "rule_description": "Escrow Agent not nominated by KB or release event other than insolvency of Knorr-Bremse AG\n", "prompt": "If the contract clause specifies an Escrow Agent not nominated by KB or a release event other than the insolvency of Knorr-Bremse AG, return true; otherwise, return false.", "content_for_embedding": "Source code, escrow agent nomination and release conditions of Knorr-Bremse AG."}]}, {"term_name": "6.4 - Condition Based Maintenance Data", "rules": [{"rule_id": 27, "rule_description": "Obligation to release events, signals or algorithms for predictive maintenance purposes to car-builder and/or end-customer\n", "prompt": "If the contract clause includes any obligation to release events, signals, or algorithms for predictive maintenance purposes to the car-builder and/or end-customer, return true; otherwise, return false.", "content_for_embedding": "Maintenance data sharing and algorithm disclosure requirements."}]}, {"term_name": "6.5 - Restrictions on Bus Data Access", "rules": [{"rule_id": 28, "rule_description": "Any expressed contractual restriction on access to and/or use of system relevant bus data of any onboard system regardless of whether KB or third party system\n", "prompt": "If the contract clause imposes any expressed restriction on access to or use of system-relevant bus data (regardless of whether it belongs to KB or a third party), return true; otherwise, return false.", "content_for_embedding": "Bus data access restrictions and system data from onboard system usage rights."}]}]}, {"chapter_name": "7. PRICING / PRICE FIXATION / TERMS OF PAYMENT", "terms": [{"term_name": "7.1 - Pricing", "rules": [{"rule_id": 29, "rule_description": "ROS < 8%\n", "prompt": "If the ROS (Return on Sales) value provided in the contract is less than 8%, return true; otherwise, return false.", "content_for_embedding": "Return on Sales (ROS) percentage requirements."}, {"rule_id": 30, "rule_description": "ROS < -10%\n", "prompt": "If the ROS (Return on Sales) value provided in the contract is less than -10%, return true; otherwise, return false.", "content_for_embedding": "Negative Return on Sales (ROS) thresholds."}]}, {"term_name": "7.2 - Price Fixation Period", "rules": [{"rule_id": 31, "rule_description": "Any period >24 months\n", "prompt": "If the contract specifies a price fixation period exceeding 24 months from the contract signing date, return true; otherwise, return false.", "content_for_embedding": "Price fixation period duration or price remaining, validity period."}, {"rule_id": 32, "rule_description": "If a price escalation formula with a fixed portion and variable portion is agreed and the fixed portion accounts for more than 15%; or\nPrice increase (only) expressed in % p.a. (i.e. no variable portion) for a period of over 5 years\n", "prompt": "If the contract includes a price escalation formula with a fixed portion exceeding 15% or a price increase expressed only in % p.a. (no variable portion) for over 5 years, return true; otherwise, return false. If the contract does not mention any price escalation formula, set Determination Result to null.", "content_for_embedding": "Price escalation formula or price adjustment formula, and fixed portion limitations."}]}, {"term_name": "7.3 - Options / Price Reduction", "rules": [{"rule_id": 33, "rule_description": "Price reductions with respect to options not linked to options actually exercised\n", "prompt": "If the contract includes price reductions for options not linked to actually exercised options, return true; otherwise, set Determination Result to null.", "content_for_embedding": "Price reduction for contract options."}]}, {"term_name": "7.4 - Terms of Payment", "rules": [{"rule_id": 34, "rule_description": "Deliveries: A down payment of the aggregate value of the equipment lower than 10% when the contract is signed or first order placed\\nThe balance of the value of the goods not be invoiced when equipment is delivered.", "prompt": "If the contract specifies that a down payment of at least 10% of the contract price or value is required upon signing or first order, and the remaining payment is due upon receipt of the invoice, return false; otherwise, return true.", "content_for_embedding": "Down payment or prepayment requirements, and invoice timing."}, {"rule_id": 35, "rule_description": "Terms of Payment: Non-Recurring Cost: • Payments for non-recurring costs were made without any pre-payment or linkage to pre-defined performance milestones to be accomplished by KB.", "prompt": "If there is no 'non-recurring cost' specified in the contract, set Determination Result to null then stop. If the contract clause requires payments for non-recurring costs (e.g., R&D fees) to be made upon signing the contract or before delivery completion, return false; otherwise, return true.", "content_for_embedding": "Non-recurring costs payment terms and milestones."}, {"rule_id": 36, "rule_description": "Services:\n100% of services offered to be paid latest after such services have been rendered. (no payment retention)\n", "prompt": "If the contract clause allows any payment retention (e.g., withholding a percentage as a warranty reserve) for services after they are rendered, return true; otherwise, return false. If the contract does not mention any payment retention for the services, set Determination Result to null.", "content_for_embedding": "Service payment terms and retention conditions."}, {"rule_id": 37, "rule_description": "Invoices are to be paid latest 60 days after the date they have been issued, without any discount, or according to group wide arrangements entered into with individual customer\n", "prompt": "If related_content null,Determination Result is null; payment terms require customers to pay within 60 days from the date they receive the invoice. If this condition is met, the Determination Result is false; otherwise, it is true.", "content_for_embedding": "Invoice payment terms and deadlines, billing period."}]}]}, {"chapter_name": "8. GUARANTEES / SURETYSHIPS / LETTER OF COMFORT", "terms": [{"term_name": "8.1", "rules": [{"rule_id": 38, "rule_description": "Bank and/or Insurance Guarantee instead of Corporate / Parent Company Guarantee", "prompt": "If the contract requires a bank or insurance guarantee instead of a corporate/parent company guarantee, return true; otherwise, return false. If the contract is a chinese contract, both of bank or insurance guarantee and corporate/parent company guarantee can return false; otherwise, set Determination Result to null.", "content_for_embedding": "Guarantee types (bank guarantee, parent company guarantee) and requirements, FICO confirm."}]}]}, {"chapter_name": "9. DISPUTE RESOLUTION", "terms": [{"term_name": "9.1", "rules": [{"rule_id": 39, "rule_description": "No arbitration agreement for cross-border transactions and no approval by Corporate Legal is obtained", "prompt": "Firstly, if they are from the same country, set Determination Result to null and stop. If the A and B parties are from different countries and does not include an arbitration agreement for dispute resolution, return true; otherwise, return false.", "content_for_embedding": "Buyer: , Seller: , Parties to the Contract."}]}, {"term_name": "9.2", "rules": [{"rule_id": 40, "rule_description": "Other high risk dispute resolution clauses as identified / advised by Corporate Legal ", "prompt": "Firstly, if they are from the same country, set Determination Result to null and stop. If the contract is cross-border (parties from different countries) and specifies litigation instead of arbitration for dispute resolution, return true; otherwise, return false.", "content_for_embedding": "Buyer: , Seller: , Parties to the Contract."}]}]}]